package com.service.impl;

import org.springframework.stereotype.Service;
import java.util.Map;
import java.util.List;

import com.baomidou.mybatisplus.mapper.Wrapper;
import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import com.utils.PageUtils;
import com.utils.Query;


import com.dao.ZichanleixingDao;
import com.entity.ZichanleixingEntity;
import com.service.ZichanleixingService;
import com.entity.vo.ZichanleixingVO;
import com.entity.view.ZichanleixingView;

@Service("zichanleixingService")
public class ZichanleixingServiceImpl extends ServiceImpl<ZichanleixingDao, ZichanleixingEntity> implements ZichanleixingService {
	
	
    @Override
    public PageUtils queryPage(Map<String, Object> params) {
        Page<ZichanleixingEntity> page = this.selectPage(
                new Query<ZichanleixingEntity>(params).getPage(),
                new EntityWrapper<ZichanleixingEntity>()
        );
        return new PageUtils(page);
    }
    
    @Override
	public PageUtils queryPage(Map<String, Object> params, Wrapper<ZichanleixingEntity> wrapper) {
		  Page<ZichanleixingView> page =new Query<ZichanleixingView>(params).getPage();
	        page.setRecords(baseMapper.selectListView(page,wrapper));
	    	PageUtils pageUtil = new PageUtils(page);
	    	return pageUtil;
 	}

    
    @Override
	public List<ZichanleixingVO> selectListVO(Wrapper<ZichanleixingEntity> wrapper) {
 		return baseMapper.selectListVO(wrapper);
	}
	
	@Override
	public ZichanleixingVO selectVO(Wrapper<ZichanleixingEntity> wrapper) {
 		return baseMapper.selectVO(wrapper);
	}
	
	@Override
	public List<ZichanleixingView> selectListView(Wrapper<ZichanleixingEntity> wrapper) {
		return baseMapper.selectListView(wrapper);
	}

	@Override
	public ZichanleixingView selectView(Wrapper<ZichanleixingEntity> wrapper) {
		return baseMapper.selectView(wrapper);
	}


}
