{"remainingRequest": "E:\\java系统\\公司财务系统\\src\\main\\resources\\admin\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\java系统\\公司财务系统\\src\\main\\resources\\admin\\admin\\src\\views\\modules\\kaoqinxinxi\\add-or-update.vue?vue&type=template&id=24c985d2&scoped=true", "dependencies": [{"path": "E:\\java系统\\公司财务系统\\src\\main\\resources\\admin\\admin\\src\\views\\modules\\kaoqinxinxi\\add-or-update.vue", "mtime": 1754641465013}, {"path": "E:\\java系统\\公司财务系统\\src\\main\\resources\\admin\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754805254356}, {"path": "E:\\java系统\\公司财务系统\\src\\main\\resources\\admin\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754805254356}, {"path": "E:\\java系统\\公司财务系统\\src\\main\\resources\\admin\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754805257322}, {"path": "E:\\java系统\\公司财务系统\\src\\main\\resources\\admin\\admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1754628163793}, {"path": "E:\\java系统\\公司财务系统\\src\\main\\resources\\admin\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754805254356}, {"path": "E:\\java系统\\公司财务系统\\src\\main\\resources\\admin\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754628160828}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}