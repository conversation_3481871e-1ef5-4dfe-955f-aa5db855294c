{"remainingRequest": "E:\\java系统\\公司财务系统\\src\\main\\resources\\admin\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\java系统\\公司财务系统\\src\\main\\resources\\admin\\admin\\src\\components\\index\\IndexMain.vue?vue&type=style&index=0&id=16fdb8a4&lang=scss&scoped=true", "dependencies": [{"path": "E:\\java系统\\公司财务系统\\src\\main\\resources\\admin\\admin\\src\\components\\index\\IndexMain.vue", "mtime": 1754631104830}, {"path": "E:\\java系统\\公司财务系统\\src\\main\\resources\\admin\\admin\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1754805255591}, {"path": "E:\\java系统\\公司财务系统\\src\\main\\resources\\admin\\admin\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1754628163326}, {"path": "E:\\java系统\\公司财务系统\\src\\main\\resources\\admin\\admin\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1754628158457}, {"path": "E:\\java系统\\公司财务系统\\src\\main\\resources\\admin\\admin\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1754628153247}, {"path": "E:\\java系统\\公司财务系统\\src\\main\\resources\\admin\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754805254356}, {"path": "E:\\java系统\\公司财务系统\\src\\main\\resources\\admin\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754628160828}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CmEgewoJdGV4dC1kZWNvcmF0aW9uOiBub25lOwoJY29sb3I6ICM1NTU7Cn0KCmE6aG92ZXIgewoJYmFja2dyb3VuZDogIzAwYzI5MjsKfQoKLmVsLW1haW4gewoJcGFkZGluZzogMDsKCWRpc3BsYXk6IGJsb2NrOwp9CgoubmF2LWxpc3QgewoJd2lkdGg6IDEwMCU7CgltYXJnaW46IDAgYXV0bzsKCXRleHQtYWxpZ246IGxlZnQ7CgltYXJnaW4tdG9wOiAyMHB4OwoKCS5uYXYtdGl0bGUgewoJCWRpc3BsYXk6IGlubGluZS1ibG9jazsKCQlmb250LXNpemU6IDE1cHg7CgkJY29sb3I6ICMzMzM7CgkJcGFkZGluZzogMTVweCAyNXB4OwoJCWJvcmRlcjogbm9uZTsKCX0KCgkubmF2LXRpdGxlLmFjdGl2ZSB7CgkJY29sb3I6ICM1NTU7CgkJY3Vyc29yOiBkZWZhdWx0OwoJCWJhY2tncm91bmQtY29sb3I6ICNmZmY7Cgl9Cn0KCi5uYXYtaXRlbSB7CgltYXJnaW4tdG9wOiAyMHB4OwoJYmFja2dyb3VuZDogI0ZGRkZGRjsKCXBhZGRpbmc6IDE1cHggMDsKCgkubWVudSB7CgkJcGFkZGluZzogMTVweCAyNXB4OwoJfQp9CgouZGV0YWlsLWZvcm0tY29udGVudCB7CiAgICBiYWNrZ3JvdW5kOiB0cmFuc3BhcmVudDsKfQo="}, {"version": 3, "sources": ["IndexMain.vue"], "names": [], "mappings": ";AAkKA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA", "file": "IndexMain.vue", "sourceRoot": "src/components/index", "sourcesContent": ["<template>\r\n\t<div style=\"height: 100%;\">\r\n\t\t<el-main :style='\"vertical\" == \"vertical\" ? (2 == 1 ? {\"minHeight\":\"100%\",\"padding\":\"0\",\"margin\":\"0 0 0 210px\",\"position\":\"relative\",\"display\":\"block\"} : (2 == 2 ? (isCollapse ? {\"minHeight\":\"100%\",\"padding\":\"0\",\"margin\":\"0 0 0 64px\",\"position\":\"relative\",\"display\":\"block\"} : {\"minHeight\":\"100%\",\"padding\":\"0\",\"margin\":\"0 0 0 210px\",\"background\":\"rgb(235, 242, 251)\",\"display\":\"block\",\"overflow-x\":\"hidden\",\"position\":\"relative\"}) : \"\")) : {\"minHeight\":\"100%\",\"margin\":\"0\",\"position\":\"relative\"}'>\r\n\t\t\t<!-- top -->\r\n\t\t\t<index-header :style='{\"padding\":\"8px 20px\",\"alignItems\":\"center\",\"top\":\"0\",\"left\":\"0\",\"background\":\"linear-gradient(120deg, #0f75ff 60%, #2ddcd3  100%)\",\"display\":\"flex\",\"width\":\"100%\",\"position\":\"fixed\",\"zIndex\":\"1002\",\"height\":\"60px\"}'></index-header>\r\n\t\t\t\r\n\t\t\t<!-- menu -->\r\n\t\t\t<template v-if=\"'vertical' == 'vertical'\">\r\n\t\t\t  <template v-if=\"2 == 1\">\r\n\t\t\t\t<index-aside :style='{\"boxShadow\":\"1px 0 6px  rgba(64, 158, 255, .3)\",\"overflow\":\"hidden\",\"top\":\"0\",\"left\":\"0\",\"background\":\"#304156\",\"bottom\":\"0\",\"width\":\"210px\",\"fontSize\":\"0px\",\"position\":\"fixed\",\"height\":\"100%\",\"zIndex\":\"1001\"}'></index-aside>\r\n\t\t\t  </template>\r\n\t\t\t  <template v-if=\"2 == 2\">\r\n\t\t\t\t<index-aside :is-collapse=\"isCollapse\" @oncollapsechange=\"collapseChange\" :style='isCollapse ? {\"boxShadow\":\"1px 0 6px  rgba(64, 158, 255, .3)\",\"overflow\":\"hidden\",\"top\":\"0\",\"left\":\"0\",\"background\":\"#304156\",\"bottom\":\"0\",\"width\":\"64px\",\"fontSize\":\"0px\",\"position\":\"fixed\",\"transition\":\"width 0.3s\",\"height\":\"100%\",\"zIndex\":\"1001\"} : {\"boxShadow\":\"1px 0 6px  rgba(64, 158, 255, .3)\",\"padding\":\"0 0 60px\",\"overflow\":\"hidden\",\"top\":\"60px\",\"left\":\"0\",\"background\":\"#0d102c\",\"bottom\":\"0\",\"width\":\"210px\",\"position\":\"fixed\",\"transition\":\"width 0.3s\",\"height\":\"100%\",\"zIndex\":\"1001\"}'></index-aside>\r\n\t\t\t  </template>\r\n\t\t\t</template>\r\n\t\t\t<template v-if=\"'vertical' == 'horizontal'\">\r\n\t\t\t  <template v-if=\"2 == 1\">\r\n\t\t\t\t<index-aside :style='{\"width\":\"100%\",\"borderColor\":\"#efefef\",\"borderStyle\":\"solid\",\"background\":\"#304156\",\"borderWidth\":\"0 0 1px 0\",\"height\":\"auto\"}'></index-aside>\r\n\t\t\t  </template>\r\n\t\t\t  <template v-if=\"2 == 2\">\r\n\t\t\t\t<index-aside :style='{\"borderColor\":\"#efefef\",\"background\":\"#FFF\",\"borderWidth\":\"0 0 1px 0\",\"display\":\"flex\",\"width\":\"100%\",\"borderStyle\":\"solid\",\"height\":\"auto\"}'></index-aside>\r\n\t\t\t  </template>\r\n\t\t\t</template>\r\n\t\t\t\r\n\t\t\t<!-- breadcrumb -->\r\n\t\t\t<bread-crumbs :title=\"title\" :style='{\"width\":\"calc(100% - 60px)\",\"padding\":\"20px\",\"margin\":\"60px 0 0 0px \",\"borderColor\":\"#eee\",\"borderStyle\":\"solid\",\"borderWidth\":\"0 0 1px 0\"}' class=\"bread-crumbs\"></bread-crumbs>\r\n\t\t\t\r\n\t\t\t<!-- TagsView -->\r\n\t\t\t<tags-view />\r\n\t\t\t\r\n\t\t\t<router-view class=\"router-view\"></router-view>\r\n\t\t</el-main>\r\n\t</div>\r\n</template>\r\n\r\n<script>\r\n\timport IndexAside from '@/components/index/IndexAsideStatic'\r\n\timport IndexHeader from '@/components/index/IndexHeader'\r\n\timport TagsView from '@/components/index/TagsView'\r\n\timport menu from \"@/utils/menu\";\r\n\texport default {\r\n\t\tcomponents: {\r\n\t\t\tIndexAside,\r\n\t\t\tIndexHeader,\r\n\t\t\tTagsView\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tmenuList: [],\r\n\t\t\t\trole: \"\",\r\n\t\t\t\tcurrentIndex: -2,\r\n\t\t\t\titemMenu: [],\r\n\t\t\t\ttitle: '',\r\n\t\t\t\tisCollapse: false,\r\n\t\t\t};\r\n\t\t},\r\n\t\tmounted() {\r\n\t\t\tlet menus = menu.list();\r\n\t\t\tthis.menuList = menus;\r\n\t\t\tthis.role = this.$storage.get(\"role\");\r\n\t\t},\r\n\t\tcreated() {\r\n\t\t\tthis.init();\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tinit(){\r\n\t\t\t\tthis.$nextTick(()=>{\r\n\t\t\t\t\t\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tcollapseChange(collapse) {\r\n\t\t\t\tthis.isCollapse = collapse\r\n\t\t\t},\r\n\t\t\tmenuHandler(menu) {\r\n\t\t\t\tthis.$router.push({\r\n\t\t\t\t\tname: menu.tableName\r\n\t\t\t\t});\r\n\t\t\t\tthis.title = menu.menu;\r\n\t\t\t},\r\n\t\t\ttitleChange(index, menus) {\r\n\t\t\t\tthis.currentIndex = index\r\n\t\t\t\tthis.itemMenu = menus;\r\n\t\t\t},\r\n\t\t\thomeChange(index) {\r\n\t\t\t\tthis.itemMenu = [];\r\n\t\t\t\tthis.title = \"\"\r\n\t\t\t\tthis.currentIndex = index\r\n\t\t\t\tthis.$router.push({\r\n\t\t\t\t\tname: 'home'\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tcenterChange(index) {\r\n\t\t\t\tthis.itemMenu = [{\r\n\t\t\t\t\t\"buttons\": [\"新增\", \"查看\", \"修改\", \"删除\"],\r\n\t\t\t\t\t\"menu\": \"修改密码\",\r\n\t\t\t\t\t\"tableName\": \"updatePassword\"\r\n\t\t\t\t}, {\r\n\t\t\t\t\t\"buttons\": [\"新增\", \"查看\", \"修改\", \"删除\"],\r\n\t\t\t\t\t\"menu\": \"个人信息\",\r\n\t\t\t\t\t\"tableName\": \"center\"\r\n\t\t\t\t}];\r\n\t\t\t\tthis.title = \"\"\r\n\t\t\t\tthis.currentIndex = index\r\n\t\t\t\tthis.$router.push({\r\n\t\t\t\t\tname: 'home'\r\n\t\t\t\t});\r\n\t\t\t\t\r\n\t\t\t}\r\n\t\t}\r\n\t};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n\ta {\r\n\t\ttext-decoration: none;\r\n\t\tcolor: #555;\r\n\t}\r\n\r\n\ta:hover {\r\n\t\tbackground: #00c292;\r\n\t}\r\n\t\r\n\t.el-main {\r\n\t\tpadding: 0;\r\n\t\tdisplay: block;\r\n\t}\r\n\r\n\t.nav-list {\r\n\t\twidth: 100%;\r\n\t\tmargin: 0 auto;\r\n\t\ttext-align: left;\r\n\t\tmargin-top: 20px;\r\n\r\n\t\t.nav-title {\r\n\t\t\tdisplay: inline-block;\r\n\t\t\tfont-size: 15px;\r\n\t\t\tcolor: #333;\r\n\t\t\tpadding: 15px 25px;\r\n\t\t\tborder: none;\r\n\t\t}\r\n\r\n\t\t.nav-title.active {\r\n\t\t\tcolor: #555;\r\n\t\t\tcursor: default;\r\n\t\t\tbackground-color: #fff;\r\n\t\t}\r\n\t}\r\n\r\n\t.nav-item {\r\n\t\tmargin-top: 20px;\r\n\t\tbackground: #FFFFFF;\r\n\t\tpadding: 15px 0;\r\n\r\n\t\t.menu {\r\n\t\t\tpadding: 15px 25px;\r\n\t\t}\r\n\t}\r\n\t\r\n\t.detail-form-content {\r\n\t    background: transparent;\r\n\t}\r\n</style>\r\n"]}]}