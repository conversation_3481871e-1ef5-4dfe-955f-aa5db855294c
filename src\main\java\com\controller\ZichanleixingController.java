package com.controller;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Map;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Date;
import java.util.List;
import javax.servlet.http.HttpServletRequest;

import com.utils.ValidatorUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.mapper.Wrapper;
import com.annotation.IgnoreAuth;

import com.entity.ZichanleixingEntity;
import com.entity.view.ZichanleixingView;

import com.service.ZichanleixingService;
import com.service.TokenService;
import com.utils.PageUtils;
import com.utils.R;
import com.utils.MPUtil;
import com.utils.MapUtils;
import com.utils.CommonUtil;
import java.io.IOException;

/**
 * 资产类型
 * 后端接口
 * <AUTHOR> @email 
 * @date 2024-02-19 14:47:30
 */
@RestController
@RequestMapping("/zichanleixing")
public class ZichanleixingController {
    @Autowired
    private ZichanleixingService zichanleixingService;




    



    /**
     * 后端列表
     */
    @RequestMapping("/page")
    public R page(@RequestParam Map<String, Object> params,ZichanleixingEntity zichanleixing,
		HttpServletRequest request){
        EntityWrapper<ZichanleixingEntity> ew = new EntityWrapper<ZichanleixingEntity>();

		PageUtils page = zichanleixingService.queryPage(params, MPUtil.sort(MPUtil.between(MPUtil.likeOrEq(ew, zichanleixing), params), params));

        return R.ok().put("data", page);
    }
    
    /**
     * 前端列表
     */
	@IgnoreAuth
    @RequestMapping("/list")
    public R list(@RequestParam Map<String, Object> params,ZichanleixingEntity zichanleixing, 
		HttpServletRequest request){
        EntityWrapper<ZichanleixingEntity> ew = new EntityWrapper<ZichanleixingEntity>();

		PageUtils page = zichanleixingService.queryPage(params, MPUtil.sort(MPUtil.between(MPUtil.likeOrEq(ew, zichanleixing), params), params));
        return R.ok().put("data", page);
    }



	/**
     * 列表
     */
    @RequestMapping("/lists")
    public R list( ZichanleixingEntity zichanleixing){
       	EntityWrapper<ZichanleixingEntity> ew = new EntityWrapper<ZichanleixingEntity>();
      	ew.allEq(MPUtil.allEQMapPre( zichanleixing, "zichanleixing")); 
        return R.ok().put("data", zichanleixingService.selectListView(ew));
    }

	 /**
     * 查询
     */
    @RequestMapping("/query")
    public R query(ZichanleixingEntity zichanleixing){
        EntityWrapper< ZichanleixingEntity> ew = new EntityWrapper< ZichanleixingEntity>();
 		ew.allEq(MPUtil.allEQMapPre( zichanleixing, "zichanleixing")); 
		ZichanleixingView zichanleixingView =  zichanleixingService.selectView(ew);
		return R.ok("查询资产类型成功").put("data", zichanleixingView);
    }
	
    /**
     * 后端详情
     */
    @RequestMapping("/info/{id}")
    public R info(@PathVariable("id") Long id){
        ZichanleixingEntity zichanleixing = zichanleixingService.selectById(id);
        return R.ok().put("data", zichanleixing);
    }

    /**
     * 前端详情
     */
	@IgnoreAuth
    @RequestMapping("/detail/{id}")
    public R detail(@PathVariable("id") Long id){
        ZichanleixingEntity zichanleixing = zichanleixingService.selectById(id);
        return R.ok().put("data", zichanleixing);
    }
    



    /**
     * 后端保存
     */
    @RequestMapping("/save")
    public R save(@RequestBody ZichanleixingEntity zichanleixing, HttpServletRequest request){
        if(zichanleixingService.selectCount(new EntityWrapper<ZichanleixingEntity>().eq("zichanleixing", zichanleixing.getZichanleixing()))>0) {
            return R.error("资产类型已存在");
        }
    	//ValidatorUtils.validateEntity(zichanleixing);
        zichanleixingService.insert(zichanleixing);
        return R.ok();
    }
    
    /**
     * 前端保存
     */
    @RequestMapping("/add")
    public R add(@RequestBody ZichanleixingEntity zichanleixing, HttpServletRequest request){
        if(zichanleixingService.selectCount(new EntityWrapper<ZichanleixingEntity>().eq("zichanleixing", zichanleixing.getZichanleixing()))>0) {
            return R.error("资产类型已存在");
        }
    	//ValidatorUtils.validateEntity(zichanleixing);
        zichanleixingService.insert(zichanleixing);
        return R.ok();
    }





    /**
     * 修改
     */
    @RequestMapping("/update")
    @Transactional
    public R update(@RequestBody ZichanleixingEntity zichanleixing, HttpServletRequest request){
        //ValidatorUtils.validateEntity(zichanleixing);
        if(zichanleixingService.selectCount(new EntityWrapper<ZichanleixingEntity>().ne("id", zichanleixing.getId()).eq("zichanleixing", zichanleixing.getZichanleixing()))>0) {
            return R.error("资产类型已存在");
        }
        zichanleixingService.updateById(zichanleixing);//全部更新
        return R.ok();
    }



    

    /**
     * 删除
     */
    @RequestMapping("/delete")
    public R delete(@RequestBody Long[] ids){
        zichanleixingService.deleteBatchIds(Arrays.asList(ids));
        return R.ok();
    }
    
	










}
