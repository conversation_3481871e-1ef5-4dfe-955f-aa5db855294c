{"remainingRequest": "E:\\java系统\\公司财务系统\\src\\main\\resources\\admin\\admin\\node_modules\\babel-loader\\lib\\index.js!E:\\java系统\\公司财务系统\\src\\main\\resources\\admin\\admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!E:\\java系统\\公司财务系统\\src\\main\\resources\\admin\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\java系统\\公司财务系统\\src\\main\\resources\\admin\\admin\\src\\views\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\java系统\\公司财务系统\\src\\main\\resources\\admin\\admin\\src\\views\\index.vue", "mtime": 1754631105339}, {"path": "E:\\java系统\\公司财务系统\\src\\main\\resources\\admin\\admin\\babel.config.js", "mtime": 1711079474000}, {"path": "E:\\java系统\\公司财务系统\\src\\main\\resources\\admin\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754805254356}, {"path": "E:\\java系统\\公司财务系统\\src\\main\\resources\\admin\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754805257322}, {"path": "E:\\java系统\\公司财务系统\\src\\main\\resources\\admin\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754805254356}, {"path": "E:\\java系统\\公司财务系统\\src\\main\\resources\\admin\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754628160828}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IEluZGV4TWFpbiBmcm9tICdAL2NvbXBvbmVudHMvaW5kZXgvSW5kZXhNYWluJzsKZXhwb3J0IGRlZmF1bHQgewogIGNvbXBvbmVudHM6IHsKICAgIEluZGV4TWFpbjogSW5kZXhNYWluCiAgfQp9Ow=="}, {"version": 3, "names": ["IndexMain", "components"], "sources": ["src/views/index.vue"], "sourcesContent": ["<template>\r\n\t<el-container>\r\n\t\t<index-main></index-main>\r\n\t</el-container>\r\n</template>\r\n<script>\r\n\timport IndexMain from '@/components/index/IndexMain'\r\n\texport default {\r\n\t\tcomponents: {\r\n\t\t\tIndexMain\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n\t// 铺满全屏\r\n\t.el-container {\r\n\t\tposition: absolute;\r\n\t\twidth: 100%;\r\n\t\ttop: 0;\r\n\t\tleft: 0;\r\n\t\tbottom: 0;\r\n\t\tdisplay: block;\r\n\t}\r\n</style>\r\n"], "mappings": "AAMA,OAAAA,SAAA;AACA;EACAC,UAAA;IACAD,SAAA,EAAAA;EACA;AACA", "ignoreList": []}]}