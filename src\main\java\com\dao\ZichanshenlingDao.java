package com.dao;

import com.entity.ZichanshenlingEntity;
import com.baomidou.mybatisplus.mapper.BaseMapper;
import java.util.List;
import java.util.Map;
import com.baomidou.mybatisplus.mapper.Wrapper;
import com.baomidou.mybatisplus.plugins.pagination.Pagination;

import org.apache.ibatis.annotations.Param;
import com.entity.vo.ZichanshenlingVO;
import com.entity.view.ZichanshenlingView;


/**
 * 资产申领
 * 
 * <AUTHOR> @email 
 * @date 2024-02-19 14:47:30
 */
public interface ZichanshenlingDao extends BaseMapper<ZichanshenlingEntity> {
	
	List<ZichanshenlingVO> selectListVO(@Param("ew") Wrapper<ZichanshenlingEntity> wrapper);
	
	ZichanshenlingVO selectVO(@Param("ew") Wrapper<ZichanshenlingEntity> wrapper);
	
	List<ZichanshenlingView> selectListView(@Param("ew") Wrapper<ZichanshenlingEntity> wrapper);

	List<ZichanshenlingView> selectListView(Pagination page,@Param("ew") Wrapper<ZichanshenlingEntity> wrapper);

	
	ZichanshenlingView selectView(@Param("ew") Wrapper<ZichanshenlingEntity> wrapper);
	

}
