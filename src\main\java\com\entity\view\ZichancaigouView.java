package com.entity.view;

import com.entity.ZichancaigouEntity;

import com.baomidou.mybatisplus.annotations.TableName;
import org.apache.commons.beanutils.BeanUtils;
import java.lang.reflect.InvocationTargetException;
import java.math.BigDecimal;

import java.io.Serializable;
import com.utils.EncryptUtil;
 

/**
 * 资产采购
 * 后端返回视图实体辅助类   
 * （通常后端关联的表或者自定义的字段需要返回使用）
 * <AUTHOR> @email 
 * @date 2024-02-19 14:47:30
 */
@TableName("zichancaigou")
public class ZichancaigouView  extends ZichancaigouEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	public ZichancaigouView(){
	}
 
 	public ZichancaigouView(ZichancaigouEntity zichancaigouEntity){
 	try {
			BeanUtils.copyProperties(this, zichancaigouEntity);
		} catch (IllegalAccessException | InvocationTargetException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
 		
	}


}
