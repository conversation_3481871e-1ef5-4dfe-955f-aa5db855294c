package com.controller;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Map;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Date;
import java.util.List;
import javax.servlet.http.HttpServletRequest;

import com.utils.ValidatorUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.mapper.Wrapper;
import com.annotation.IgnoreAuth;

import com.entity.ZichancaigouEntity;
import com.entity.view.ZichancaigouView;

import com.service.ZichancaigouService;
import com.service.TokenService;
import com.utils.PageUtils;
import com.utils.R;
import com.utils.MPUtil;
import com.utils.MapUtils;
import com.utils.CommonUtil;
import java.io.IOException;

/**
 * 资产采购
 * 后端接口
 * <AUTHOR> @email 
 * @date 2024-02-19 14:47:30
 */
@RestController
@RequestMapping("/zichancaigou")
public class ZichancaigouController {
    @Autowired
    private ZichancaigouService zichancaigouService;




    



    /**
     * 后端列表
     */
    @RequestMapping("/page")
    public R page(@RequestParam Map<String, Object> params,ZichancaigouEntity zichancaigou,
		HttpServletRequest request){
		String tableName = request.getSession().getAttribute("tableName").toString();
		if(tableName.equals("yuangong")) {
			zichancaigou.setGonghao((String)request.getSession().getAttribute("username"));
		}
        EntityWrapper<ZichancaigouEntity> ew = new EntityWrapper<ZichancaigouEntity>();

		PageUtils page = zichancaigouService.queryPage(params, MPUtil.sort(MPUtil.between(MPUtil.likeOrEq(ew, zichancaigou), params), params));

        return R.ok().put("data", page);
    }
    
    /**
     * 前端列表
     */
	@IgnoreAuth
    @RequestMapping("/list")
    public R list(@RequestParam Map<String, Object> params,ZichancaigouEntity zichancaigou, 
		HttpServletRequest request){
        EntityWrapper<ZichancaigouEntity> ew = new EntityWrapper<ZichancaigouEntity>();

		PageUtils page = zichancaigouService.queryPage(params, MPUtil.sort(MPUtil.between(MPUtil.likeOrEq(ew, zichancaigou), params), params));
        return R.ok().put("data", page);
    }



	/**
     * 列表
     */
    @RequestMapping("/lists")
    public R list( ZichancaigouEntity zichancaigou){
       	EntityWrapper<ZichancaigouEntity> ew = new EntityWrapper<ZichancaigouEntity>();
      	ew.allEq(MPUtil.allEQMapPre( zichancaigou, "zichancaigou")); 
        return R.ok().put("data", zichancaigouService.selectListView(ew));
    }

	 /**
     * 查询
     */
    @RequestMapping("/query")
    public R query(ZichancaigouEntity zichancaigou){
        EntityWrapper< ZichancaigouEntity> ew = new EntityWrapper< ZichancaigouEntity>();
 		ew.allEq(MPUtil.allEQMapPre( zichancaigou, "zichancaigou")); 
		ZichancaigouView zichancaigouView =  zichancaigouService.selectView(ew);
		return R.ok("查询资产采购成功").put("data", zichancaigouView);
    }
	
    /**
     * 后端详情
     */
    @RequestMapping("/info/{id}")
    public R info(@PathVariable("id") Long id){
        ZichancaigouEntity zichancaigou = zichancaigouService.selectById(id);
        return R.ok().put("data", zichancaigou);
    }

    /**
     * 前端详情
     */
	@IgnoreAuth
    @RequestMapping("/detail/{id}")
    public R detail(@PathVariable("id") Long id){
        ZichancaigouEntity zichancaigou = zichancaigouService.selectById(id);
        return R.ok().put("data", zichancaigou);
    }
    



    /**
     * 后端保存
     */
    @RequestMapping("/save")
    public R save(@RequestBody ZichancaigouEntity zichancaigou, HttpServletRequest request){
    	//ValidatorUtils.validateEntity(zichancaigou);
        zichancaigouService.insert(zichancaigou);
        return R.ok();
    }
    
    /**
     * 前端保存
     */
    @RequestMapping("/add")
    public R add(@RequestBody ZichancaigouEntity zichancaigou, HttpServletRequest request){
    	//ValidatorUtils.validateEntity(zichancaigou);
        zichancaigouService.insert(zichancaigou);
        return R.ok();
    }





    /**
     * 修改
     */
    @RequestMapping("/update")
    @Transactional
    public R update(@RequestBody ZichancaigouEntity zichancaigou, HttpServletRequest request){
        //ValidatorUtils.validateEntity(zichancaigou);
        zichancaigouService.updateById(zichancaigou);//全部更新
        return R.ok();
    }

    /**
     * 审核
     */
    @RequestMapping("/shBatch")
    @Transactional
    public R update(@RequestBody Long[] ids, @RequestParam String sfsh, @RequestParam String shhf){
        List<ZichancaigouEntity> list = new ArrayList<ZichancaigouEntity>();
        for(Long id : ids) {
            ZichancaigouEntity zichancaigou = zichancaigouService.selectById(id);
            zichancaigou.setSfsh(sfsh);
            zichancaigou.setShhf(shhf);
            list.add(zichancaigou);
        }
        zichancaigouService.updateBatchById(list);
        return R.ok();
    }


    

    /**
     * 删除
     */
    @RequestMapping("/delete")
    public R delete(@RequestBody Long[] ids){
        zichancaigouService.deleteBatchIds(Arrays.asList(ids));
        return R.ok();
    }
    
	










}
