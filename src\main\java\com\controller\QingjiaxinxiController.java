package com.controller;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Map;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Date;
import java.util.List;
import javax.servlet.http.HttpServletRequest;

import com.utils.ValidatorUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.mapper.Wrapper;
import com.annotation.IgnoreAuth;

import com.entity.QingjiaxinxiEntity;
import com.entity.view.QingjiaxinxiView;

import com.service.QingjiaxinxiService;
import com.service.TokenService;
import com.utils.PageUtils;
import com.utils.R;
import com.utils.MPUtil;
import com.utils.MapUtils;
import com.utils.CommonUtil;
import java.io.IOException;

/**
 * 请假信息
 * 后端接口
 * <AUTHOR> @email 
 * @date 2024-02-19 14:47:30
 */
@RestController
@RequestMapping("/qingjiaxinxi")
public class QingjiaxinxiController {
    @Autowired
    private QingjiaxinxiService qingjiaxinxiService;




    



    /**
     * 后端列表
     */
    @RequestMapping("/page")
    public R page(@RequestParam Map<String, Object> params,QingjiaxinxiEntity qingjiaxinxi,
		HttpServletRequest request){
		String tableName = request.getSession().getAttribute("tableName").toString();
		if(tableName.equals("yuangong")) {
			qingjiaxinxi.setGonghao((String)request.getSession().getAttribute("username"));
		}
        EntityWrapper<QingjiaxinxiEntity> ew = new EntityWrapper<QingjiaxinxiEntity>();

		PageUtils page = qingjiaxinxiService.queryPage(params, MPUtil.sort(MPUtil.between(MPUtil.likeOrEq(ew, qingjiaxinxi), params), params));

        return R.ok().put("data", page);
    }
    
    /**
     * 前端列表
     */
	@IgnoreAuth
    @RequestMapping("/list")
    public R list(@RequestParam Map<String, Object> params,QingjiaxinxiEntity qingjiaxinxi, 
		HttpServletRequest request){
        EntityWrapper<QingjiaxinxiEntity> ew = new EntityWrapper<QingjiaxinxiEntity>();

		PageUtils page = qingjiaxinxiService.queryPage(params, MPUtil.sort(MPUtil.between(MPUtil.likeOrEq(ew, qingjiaxinxi), params), params));
        return R.ok().put("data", page);
    }



	/**
     * 列表
     */
    @RequestMapping("/lists")
    public R list( QingjiaxinxiEntity qingjiaxinxi){
       	EntityWrapper<QingjiaxinxiEntity> ew = new EntityWrapper<QingjiaxinxiEntity>();
      	ew.allEq(MPUtil.allEQMapPre( qingjiaxinxi, "qingjiaxinxi")); 
        return R.ok().put("data", qingjiaxinxiService.selectListView(ew));
    }

	 /**
     * 查询
     */
    @RequestMapping("/query")
    public R query(QingjiaxinxiEntity qingjiaxinxi){
        EntityWrapper< QingjiaxinxiEntity> ew = new EntityWrapper< QingjiaxinxiEntity>();
 		ew.allEq(MPUtil.allEQMapPre( qingjiaxinxi, "qingjiaxinxi")); 
		QingjiaxinxiView qingjiaxinxiView =  qingjiaxinxiService.selectView(ew);
		return R.ok("查询请假信息成功").put("data", qingjiaxinxiView);
    }
	
    /**
     * 后端详情
     */
    @RequestMapping("/info/{id}")
    public R info(@PathVariable("id") Long id){
        QingjiaxinxiEntity qingjiaxinxi = qingjiaxinxiService.selectById(id);
        return R.ok().put("data", qingjiaxinxi);
    }

    /**
     * 前端详情
     */
	@IgnoreAuth
    @RequestMapping("/detail/{id}")
    public R detail(@PathVariable("id") Long id){
        QingjiaxinxiEntity qingjiaxinxi = qingjiaxinxiService.selectById(id);
        return R.ok().put("data", qingjiaxinxi);
    }
    



    /**
     * 后端保存
     */
    @RequestMapping("/save")
    public R save(@RequestBody QingjiaxinxiEntity qingjiaxinxi, HttpServletRequest request){
    	//ValidatorUtils.validateEntity(qingjiaxinxi);
        qingjiaxinxiService.insert(qingjiaxinxi);
        return R.ok();
    }
    
    /**
     * 前端保存
     */
    @RequestMapping("/add")
    public R add(@RequestBody QingjiaxinxiEntity qingjiaxinxi, HttpServletRequest request){
    	//ValidatorUtils.validateEntity(qingjiaxinxi);
        qingjiaxinxiService.insert(qingjiaxinxi);
        return R.ok();
    }





    /**
     * 修改
     */
    @RequestMapping("/update")
    @Transactional
    public R update(@RequestBody QingjiaxinxiEntity qingjiaxinxi, HttpServletRequest request){
        //ValidatorUtils.validateEntity(qingjiaxinxi);
        qingjiaxinxiService.updateById(qingjiaxinxi);//全部更新
        return R.ok();
    }

    /**
     * 审核
     */
    @RequestMapping("/shBatch")
    @Transactional
    public R update(@RequestBody Long[] ids, @RequestParam String sfsh, @RequestParam String shhf){
        List<QingjiaxinxiEntity> list = new ArrayList<QingjiaxinxiEntity>();
        for(Long id : ids) {
            QingjiaxinxiEntity qingjiaxinxi = qingjiaxinxiService.selectById(id);
            qingjiaxinxi.setSfsh(sfsh);
            qingjiaxinxi.setShhf(shhf);
            list.add(qingjiaxinxi);
        }
        qingjiaxinxiService.updateBatchById(list);
        return R.ok();
    }


    

    /**
     * 删除
     */
    @RequestMapping("/delete")
    public R delete(@RequestBody Long[] ids){
        qingjiaxinxiService.deleteBatchIds(Arrays.asList(ids));
        return R.ok();
    }
    
	










}
