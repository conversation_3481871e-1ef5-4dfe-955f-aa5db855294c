package com.entity.vo;

import com.entity.YuangonggongziEntity;

import com.baomidou.mybatisplus.annotations.TableName;
import java.util.Date;
import org.springframework.format.annotation.DateTimeFormat;

import com.fasterxml.jackson.annotation.JsonFormat;
import java.io.Serializable;
 

/**
 * 员工工资
 * <AUTHOR> @email 
 * @date 2024-02-19 14:47:30
 */
public class YuangonggongziVO  implements Serializable {
	private static final long serialVersionUID = 1L;

	 			
	/**
	 * 工号
	 */
	
	private String gonghao;
		
	/**
	 * 姓名
	 */
	
	private String xingming;
		
	/**
	 * 部门
	 */
	
	private String bumen;
		
	/**
	 * 职位
	 */
	
	private String zhiwei;
		
	/**
	 * 基本工资
	 */
	
	private Double jibengongzi;
		
	/**
	 * 加班工资
	 */
	
	private Double jiabangongzi;
		
	/**
	 * 绩效金额
	 */
	
	private Double jixiaojine;
		
	/**
	 * 扣款金额
	 */
	
	private Double koukuanjine;
		
	/**
	 * 其他补助
	 */
	
	private Double qitabuzhu;
		
	/**
	 * 扣款原因
	 */
	
	private String koukuanyuanyin;
		
	/**
	 * 实发工资
	 */
	
	private Double shifagongzi;
		
	/**
	 * 发布日期
	 */
		
	@JsonFormat(locale="zh", timezone="GMT+8", pattern="yyyy-MM-dd HH:mm:ss")
	@DateTimeFormat 
	private Date faburiqi;
		
	/**
	 * 福利
	 */
	
	private String fuli;
		
	/**
	 * 是否支付
	 */
	
	private String ispay;
				
	
	/**
	 * 设置：工号
	 */
	 
	public void setGonghao(String gonghao) {
		this.gonghao = gonghao;
	}
	
	/**
	 * 获取：工号
	 */
	public String getGonghao() {
		return gonghao;
	}
				
	
	/**
	 * 设置：姓名
	 */
	 
	public void setXingming(String xingming) {
		this.xingming = xingming;
	}
	
	/**
	 * 获取：姓名
	 */
	public String getXingming() {
		return xingming;
	}
				
	
	/**
	 * 设置：部门
	 */
	 
	public void setBumen(String bumen) {
		this.bumen = bumen;
	}
	
	/**
	 * 获取：部门
	 */
	public String getBumen() {
		return bumen;
	}
				
	
	/**
	 * 设置：职位
	 */
	 
	public void setZhiwei(String zhiwei) {
		this.zhiwei = zhiwei;
	}
	
	/**
	 * 获取：职位
	 */
	public String getZhiwei() {
		return zhiwei;
	}
				
	
	/**
	 * 设置：基本工资
	 */
	 
	public void setJibengongzi(Double jibengongzi) {
		this.jibengongzi = jibengongzi;
	}
	
	/**
	 * 获取：基本工资
	 */
	public Double getJibengongzi() {
		return jibengongzi;
	}
				
	
	/**
	 * 设置：加班工资
	 */
	 
	public void setJiabangongzi(Double jiabangongzi) {
		this.jiabangongzi = jiabangongzi;
	}
	
	/**
	 * 获取：加班工资
	 */
	public Double getJiabangongzi() {
		return jiabangongzi;
	}
				
	
	/**
	 * 设置：绩效金额
	 */
	 
	public void setJixiaojine(Double jixiaojine) {
		this.jixiaojine = jixiaojine;
	}
	
	/**
	 * 获取：绩效金额
	 */
	public Double getJixiaojine() {
		return jixiaojine;
	}
				
	
	/**
	 * 设置：扣款金额
	 */
	 
	public void setKoukuanjine(Double koukuanjine) {
		this.koukuanjine = koukuanjine;
	}
	
	/**
	 * 获取：扣款金额
	 */
	public Double getKoukuanjine() {
		return koukuanjine;
	}
				
	
	/**
	 * 设置：其他补助
	 */
	 
	public void setQitabuzhu(Double qitabuzhu) {
		this.qitabuzhu = qitabuzhu;
	}
	
	/**
	 * 获取：其他补助
	 */
	public Double getQitabuzhu() {
		return qitabuzhu;
	}
				
	
	/**
	 * 设置：扣款原因
	 */
	 
	public void setKoukuanyuanyin(String koukuanyuanyin) {
		this.koukuanyuanyin = koukuanyuanyin;
	}
	
	/**
	 * 获取：扣款原因
	 */
	public String getKoukuanyuanyin() {
		return koukuanyuanyin;
	}
				
	
	/**
	 * 设置：实发工资
	 */
	 
	public void setShifagongzi(Double shifagongzi) {
		this.shifagongzi = shifagongzi;
	}
	
	/**
	 * 获取：实发工资
	 */
	public Double getShifagongzi() {
		return shifagongzi;
	}
				
	
	/**
	 * 设置：发布日期
	 */
	 
	public void setFaburiqi(Date faburiqi) {
		this.faburiqi = faburiqi;
	}
	
	/**
	 * 获取：发布日期
	 */
	public Date getFaburiqi() {
		return faburiqi;
	}
				
	
	/**
	 * 设置：福利
	 */
	 
	public void setFuli(String fuli) {
		this.fuli = fuli;
	}
	
	/**
	 * 获取：福利
	 */
	public String getFuli() {
		return fuli;
	}
				
	
	/**
	 * 设置：是否支付
	 */
	 
	public void setIspay(String ispay) {
		this.ispay = ispay;
	}
	
	/**
	 * 获取：是否支付
	 */
	public String getIspay() {
		return ispay;
	}
			
}
