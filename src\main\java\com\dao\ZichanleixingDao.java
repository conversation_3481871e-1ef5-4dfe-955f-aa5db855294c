package com.dao;

import com.entity.ZichanleixingEntity;
import com.baomidou.mybatisplus.mapper.BaseMapper;
import java.util.List;
import java.util.Map;
import com.baomidou.mybatisplus.mapper.Wrapper;
import com.baomidou.mybatisplus.plugins.pagination.Pagination;

import org.apache.ibatis.annotations.Param;
import com.entity.vo.ZichanleixingVO;
import com.entity.view.ZichanleixingView;


/**
 * 资产类型
 * 
 * <AUTHOR> @email 
 * @date 2024-02-19 14:47:30
 */
public interface ZichanleixingDao extends BaseMapper<ZichanleixingEntity> {
	
	List<ZichanleixingVO> selectListVO(@Param("ew") Wrapper<ZichanleixingEntity> wrapper);
	
	ZichanleixingVO selectVO(@Param("ew") Wrapper<ZichanleixingEntity> wrapper);
	
	List<ZichanleixingView> selectListView(@Param("ew") Wrapper<ZichanleixingEntity> wrapper);

	List<ZichanleixingView> selectListView(Pagination page,@Param("ew") Wrapper<ZichanleixingEntity> wrapper);

	
	ZichanleixingView selectView(@Param("ew") Wrapper<ZichanleixingEntity> wrapper);
	

}
