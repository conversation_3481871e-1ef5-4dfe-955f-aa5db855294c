{"name": "mini-css-extract-plugin", "version": "0.9.0", "description": "extracts CSS into separate files", "license": "MIT", "repository": "webpack-contrib/mini-css-extract-plugin", "author": "<PERSON> @sokra", "homepage": "https://github.com/webpack-contrib/mini-css-extract-plugin", "bugs": "https://github.com/webpack-contrib/mini-css-extract-plugin/issues", "main": "dist/cjs.js", "engines": {"node": ">= 6.9.0"}, "scripts": {"start": "npm run build -- -w", "prebuild": "npm run clean", "build": "cross-env NODE_ENV=production babel src -d dist --ignore \"src/**/*.test.js\" --copy-files", "postbuild": "es-check es5 dist/hmr/hotModuleReplacement.js", "clean": "del-cli dist", "commitlint": "commitlint --from=master", "lint:prettier": "prettier \"{**/*,*}.{js,json,md,yml,css}\" --list-different", "lint:js": "eslint --cache src test", "lint": "npm-run-all -l -p \"lint:**\"", "prepare": "npm run build", "release": "standard-version", "security": "npm audit", "test:only": "cross-env NODE_ENV=test jest", "test:watch": "cross-env NODE_ENV=test jest --watch", "test:coverage": "cross-env NODE_ENV=test jest --collectCoverageFrom=\"src/**/*.js\" --coverage", "test:manual": "npm run build && webpack-dev-server test/manual/src/index.js --open --config test/manual/webpack.config.js", "pretest": "npm run lint", "test": "cross-env NODE_ENV=test npm run test:coverage", "defaults": "webpack-defaults"}, "files": ["dist"], "peerDependencies": {"webpack": "^4.4.0"}, "dependencies": {"loader-utils": "^1.1.0", "normalize-url": "1.9.1", "schema-utils": "^1.0.0", "webpack-sources": "^1.1.0"}, "devDependencies": {"@babel/cli": "^7.7.5", "@babel/core": "^7.7.5", "@babel/preset-env": "^7.7.6", "@commitlint/cli": "^8.1.0", "@commitlint/config-conventional": "^8.1.0", "@webpack-contrib/defaults": "^5.0.2", "@webpack-contrib/eslint-config-webpack": "^3.0.0", "babel-eslint": "^10.0.2", "babel-jest": "^24.8.0", "commitlint-azure-pipelines-cli": "^1.0.2", "cross-env": "^5.2.0", "css-loader": "^3.3.2", "del": "^4.1.1", "del-cli": "^1.1.0", "es-check": "^5.0.0", "eslint": "^6.7.2", "eslint-config-prettier": "^6.0.0", "eslint-plugin-import": "^2.19.1", "file-loader": "^4.0.0", "husky": "^3.0.0", "jest": "^24.8.0", "jest-junit": "^10.0.0", "lint-staged": "^9.5.0", "memory-fs": "^0.4.1", "npm-run-all": "^4.1.5", "prettier": "^1.19.1", "standard-version": "^7.0.1", "webpack": "^4.41.3", "webpack-cli": "^3.3.6", "webpack-dev-server": "^3.7.2"}, "keywords": ["webpack", "css", "extract", "hmr"]}