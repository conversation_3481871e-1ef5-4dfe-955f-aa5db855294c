{"remainingRequest": "E:\\java系统\\公司财务系统\\src\\main\\resources\\admin\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\java系统\\公司财务系统\\src\\main\\resources\\admin\\admin\\src\\components\\index\\TagsView\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\java系统\\公司财务系统\\src\\main\\resources\\admin\\admin\\src\\components\\index\\TagsView\\index.vue", "mtime": 1754631104896}, {"path": "E:\\java系统\\公司财务系统\\src\\main\\resources\\admin\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754805254356}, {"path": "E:\\java系统\\公司财务系统\\src\\main\\resources\\admin\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754805257322}, {"path": "E:\\java系统\\公司财务系统\\src\\main\\resources\\admin\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754805254356}, {"path": "E:\\java系统\\公司财务系统\\src\\main\\resources\\admin\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754628160828}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";AAoBA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/components/index/TagsView", "sourcesContent": ["<template>\r\n\t<div id=\"tags-view-container\" class=\"tags-view-container\" :style='{\"padding\":\"4px 30px\",\"margin\":\"0px 0 0px\",\"borderColor\":\"#d8dce5\",\"background\":\"none\",\"borderWidth\":\"0 0 1px\",\"width\":\"100%\",\"borderStyle\":\"solid\",\"height\":\"34px\"}'>\r\n\t\t<scroll-pane ref=\"scrollPane\" class=\"tags-view-wrapper\">\r\n\t\t\t<div class=\"tags-view-box\" :style='{\"width\":\"100%\",\"whiteSpace\":\"nowrap\",\"position\":\"relative\",\"background\":\"none\"}'>\r\n\t\t\t\t<router-link v-for=\"tag in visitedViews\" ref=\"tag\" :key=\"tag.path\" :class=\"isActive(tag)?'active':''\"\r\n\t\t\t\t\t:to=\"{ path: tag.path, query: tag.query, fullPath: tag.fullPath }\" tag=\"span\" class=\"tags-view-item\"\r\n\t\t\t\t\*********************=\"closeSelectedTag(tag)\" @contextmenu.prevent.native=\"openMenu(tag,$event)\">\r\n\t\t\t\t\t<span class=\"text\">{{ tag.name }}</span>\r\n\t\t\t\t\t<span v-if=\"!tag.meta.affix\" class=\"el-icon-close\" @click.prevent.stop=\"closeSelectedTag(tag)\" />\r\n\t\t\t\t</router-link>\r\n\t\t\t</div>\r\n\t\t</scroll-pane>\r\n\t\t<ul v-show=\"visible\" :style=\"{left:left+'px',top:top+'px'}\" class=\"contextmenu\">\r\n\t\t\t<li v-if=\"!(selectedTag.meta&&selectedTag.meta.affix)\" @click=\"closeSelectedTag(selectedTag)\">Close</li>\r\n\t\t\t<li @click=\"closeAllTags(selectedTag)\">Close All</li>\r\n\t\t</ul>\r\n\t</div>\r\n</template>\r\n\r\n<script>\r\n\timport ScrollPane from './ScrollPane'\r\n\timport path from 'path'\r\n\timport {\r\n\t\tgenerateTitle\r\n\t} from '@/utils/i18n'\r\n\timport menu from '@/utils/menu'\r\n\timport { routes } from '@/router/router-static.js'\r\n\t\r\n\texport default {\r\n\t\tcomponents: {\r\n\t\t\tScrollPane\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tvisible: false,\r\n\t\t\t\ttop: 0,\r\n\t\t\t\tleft: 0,\r\n\t\t\t\tselectedTag: {},\r\n\t\t\t\taffixTags: [],\r\n\t\t\t\troutes: []\r\n\t\t\t}\r\n\t\t},\r\n\t\tcomputed: {\r\n\t\t\tvisitedViews() {\r\n\t\t\t\treturn this.$store.state.tagsView.visitedViews\r\n\t\t\t},\r\n\t\t\t// routes() {\r\n\t\t\t//   return this.$store.state.menu.routes\r\n\t\t\t// }\r\n\t\t},\r\n\t\twatch: {\r\n\t\t\t$route() {\r\n\t\t\t\tthis.addTags()\r\n\t\t\t\tthis.moveToCurrentTag()\r\n\t\t\t},\r\n\t\t\tvisible(value) {\r\n\t\t\t\tif (value) {\r\n\t\t\t\t\tdocument.body.addEventListener('click', this.closeMenu)\r\n\t\t\t\t} else {\r\n\t\t\t\t\tdocument.body.removeEventListener('click', this.closeMenu)\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\t\tmounted() {\r\n\t\t\tthis.initTags()\r\n\t\t\tthis.addTags()\r\n\t\t},\r\n\t\tcreated() {\r\n\t\t\tthis.routes = menu\r\n\t\t\t\r\n\t\t\tlet menuList = []\r\n\t\t\tconst menus = menu.list()\r\n\t\t\tif (menus) {\r\n\t\t\t\tmenuList = menus\r\n\t\t\t} else {\r\n\t\t\t\tlet params = {\r\n\t\t\t\t\tpage: 1,\r\n\t\t\t\t\tlimit: 1,\r\n\t\t\t\t\tsort: 'id',\r\n\t\t\t\t}\r\n\t\t\t\r\n\t\t\t\tthis.$http({\r\n\t\t\t\t\turl: \"menu/list\",\r\n\t\t\t\t\tmethod: \"get\",\r\n\t\t\t\t\tparams: params\r\n\t\t\t\t}).then(({\r\n\t\t\t\t\tdata\r\n\t\t\t\t}) => {\r\n\t\t\t\t\tif (data && data.code === 0) {\r\n\t\t\t\t\t\tmenuList = JSON.parse(data.data.list[0].menujson);\r\n\t\t\t\t\t\tthis.$storage.set(\"menus\", menuList);\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t\tthis.role = this.$storage.get('role')\r\n\t\t\t\r\n\t\t\tfor (let i = 0; i < menuList.length; i++) {\r\n\t\t\t\tif (menuList[i].roleName == this.role) {\r\n\t\t\t\t\tthis.routes = menuList[i].backMenu;\r\n\t\t\t\t\tbreak;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\tthis.routes = routes.concat(this.routes);\r\n\t\t\t// console.log(this.visitedViews)\r\n\t\t\t// console.log(this.routes)\r\n\t\t\t// this.initTags()\r\n\t\t\t// this.addTags()\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tisActive(route) {\r\n\t\t\t\treturn route.path === this.$route.path\r\n\t\t\t},\r\n\t\t\tfilterAffixTags(routes, basePath = '/') {\r\n\t\t\t\tlet tags = []\r\n\t\t\t\troutes.forEach(route => {\r\n\t\t\t\t\tif (route.meta && route.meta.affix) {\r\n\t\t\t\t\t\tconst tagPath = path.resolve(basePath, route.path)\r\n\t\t\t\t\t\ttags.push({\r\n\t\t\t\t\t\t\tfullPath: tagPath,\r\n\t\t\t\t\t\t\tpath: tagPath,\r\n\t\t\t\t\t\t\tname: route.name,\r\n\t\t\t\t\t\t\tmeta: {\r\n\t\t\t\t\t\t\t\t...route.meta\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif (route.children) {\r\n\t\t\t\t\t\tconst tempTags = this.filterAffixTags(route.children, route.path)\r\n\t\t\t\t\t\tif (tempTags.length >= 1) {\r\n\t\t\t\t\t\t\ttags = [...tags, ...tempTags]\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t\treturn tags\r\n\t\t\t},\r\n\t\t\tgenerateTitle,\r\n\t\t\tinitTags() {\r\n\t\t\t\tconst affixTags = this.affixTags = this.filterAffixTags(this.routes)\r\n\t\t\t\tfor (const tag of affixTags) {\r\n\t\t\t\t\t// Must have tag name\r\n\t\t\t\t\tif (tag.name) {\r\n\t\t\t\t\t\tthis.$store.dispatch('tagsView/addVisitedView', tag)\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\taddTags() {\r\n\t\t\t\tconst {\r\n\t\t\t\t\tname\r\n\t\t\t\t} = this.$route\r\n\t\t\t\tif (name) {\r\n\t\t\t\t\tthis.$store.dispatch('tagsView/addView', this.$route)\r\n\t\t\t\t}\r\n\t\t\t\treturn false\r\n\t\t\t},\r\n\t\t\tmoveToCurrentTag() {\r\n\t\t\t\tconst tags = this.$refs.tag\r\n\t\t\t\tthis.$nextTick(() => {\r\n\t\t\t\t\tfor (const tag of tags) {\r\n\t\t\t\t\t\tif (tag.to.path === this.$route.path) {\r\n\t\t\t\t\t\t\tthis.$refs.scrollPane.moveToTarget(tag)\r\n\t\t\t\t\t\t\t// when query is different then update\r\n\t\t\t\t\t\t\tif (tag.to.fullPath !== this.$route.fullPath) {\r\n\t\t\t\t\t\t\t\tthis.$store.dispatch('tagsView/updateVisitedView', this.$route)\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\tbreak\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\trefreshSelectedTag(view) {\r\n\t\t\t\tthis.$store.dispatch('tagsView/delCachedView', view).then(() => {\r\n\t\t\t\t\tconst {\r\n\t\t\t\t\t\tfullPath\r\n\t\t\t\t\t} = view\r\n\t\t\t\t\tthis.$nextTick(() => {\r\n\t\t\t\t\t\tthis.$router.replace({\r\n\t\t\t\t\t\t\tpath: '/redirect' + fullPath\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t})\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tcloseSelectedTag(view) {\r\n\t\t\t\tthis.$store.dispatch('tagsView/delView', view).then(({\r\n\t\t\t\t\tvisitedViews\r\n\t\t\t\t}) => {\r\n\t\t\t\t\tif (this.isActive(view)) {\r\n\t\t\t\t\t\tthis.toLastView(visitedViews, view)\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tcloseOthersTags() {\r\n\t\t\t\tthis.$router.push(this.selectedTag)\r\n\t\t\t\tthis.$store.dispatch('tagsView/delOthersViews', this.selectedTag).then(() => {\r\n\t\t\t\t\tthis.moveToCurrentTag()\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tcloseAllTags(view) {\r\n\t\t\t\tthis.$store.dispatch('tagsView/delAllViews').then(({\r\n\t\t\t\t\tvisitedViews\r\n\t\t\t\t}) => {\r\n\t\t\t\t\tif (this.affixTags.some(tag => tag.path === view.path)) {\r\n\t\t\t\t\t\treturn\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthis.toLastView(visitedViews, view)\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\ttoLastView(visitedViews, view) {\r\n\t\t\t\tconst latestView = visitedViews.slice(-1)[0]\r\n\t\t\t\tif (latestView) {\r\n\t\t\t\t\tthis.$router.push(latestView)\r\n\t\t\t\t} else {\r\n\t\t\t\t\t// now the default is to redirect to the home page if there is no tags-view,\r\n\t\t\t\t\t// you can adjust it according to your needs.\r\n\t\t\t\t\tif (view.name === 'Dashboard') {\r\n\t\t\t\t\t\t// to reload home page\r\n\t\t\t\t\t\tthis.$router.replace({\r\n\t\t\t\t\t\t\tpath: '/redirect' + view.fullPath\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tthis.$router.push('/')\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\topenMenu(tag, e) {\r\n\t\t\t\tconst menuMinWidth = 105\r\n\t\t\t\tconst offsetLeft = this.$el.getBoundingClientRect().left // container margin left\r\n\t\t\t\tconst offsetWidth = this.$el.offsetWidth // container width\r\n\t\t\t\tconst maxLeft = offsetWidth - menuMinWidth // left boundary\r\n\t\t\t\tconst left = e.clientX - offsetLeft + 15 // 15: margin right\r\n\r\n\t\t\t\tif (left > maxLeft) {\r\n\t\t\t\t\tthis.left = maxLeft\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.left = left\r\n\t\t\t\t}\r\n\r\n\t\t\t\tthis.top = e.clientY\r\n\t\t\t\tthis.visible = true\r\n\t\t\t\tthis.selectedTag = tag\r\n\t\t\t},\r\n\t\t\tcloseMenu() {\r\n\t\t\t\tthis.visible = false\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n\t.tags-view-container {\r\n\t\theight: 34px;\r\n\t\twidth: 100%;\r\n\t\tbackground: #fff;\r\n\t\tborder-bottom: 1px solid #d8dce5;\r\n\t\tbox-shadow: 0 1px 3px 0 rgba(0, 0, 0, .12), 0 0 3px 0 rgba(0, 0, 0, .04);\r\n\r\n\t\t.contextmenu {\r\n\t\t\tmargin: 0;\r\n\t\t\tbackground: #fff;\r\n\t\t\tz-index: 3000;\r\n\t\t\tposition: absolute;\r\n\t\t\tlist-style-type: none;\r\n\t\t\tpadding: 5px 0;\r\n\t\t\tborder-radius: 4px;\r\n\t\t\tfont-size: 12px;\r\n\t\t\tfont-weight: 400;\r\n\t\t\tcolor: #333;\r\n\t\t\tbox-shadow: 2px 2px 3px 0 rgba(0, 0, 0, .3);\r\n\r\n\t\t\tli {\r\n\t\t\t\tmargin: 0;\r\n\t\t\t\tpadding: 7px 16px;\r\n\t\t\t\tcursor: pointer;\r\n\r\n\t\t\t\t&:hover {\r\n\t\t\t\t\tbackground: #eee;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t\r\n\t.tags-view-container .tags-view-wrapper .tags-view-item {\r\n\t\t\t\tcursor: pointer;\r\n\t\t\t\tpadding: 0 8px;\r\n\t\t\t\tmargin: 0 5px 0 0;\r\n\t\t\t\tcolor: #a3b1c9;\r\n\t\t\t\tdisplay: inline-block;\r\n\t\t\t\tfont-size: 12px;\r\n\t\t\t\tborder-color: #d8dce5;\r\n\t\t\t\tline-height: 25px;\r\n\t\t\t\tbackground: #fff;\r\n\t\t\t\twidth: auto;\r\n\t\t\t\tborder-width: 1px 1px 0 1px;\r\n\t\t\t\tborder-style: solid;\r\n\t\t\t\theight: 25px;\r\n\t\t\t}\r\n\t\r\n\t.tags-view-container .tags-view-wrapper .tags-view-item:hover {\r\n\t\t\t\tcolor: #2ddcd3;\r\n\t\t\t}\r\n\t\r\n\t.tags-view-container .tags-view-wrapper .tags-view-item.active {\r\n\t\t\t\tcolor: #2ddcd3;\r\n\t\t\t}\r\n\t\r\n\t.tags-view-container .tags-view-wrapper .tags-view-item .text {\r\n\t\t\t\tcolor: inherit;\r\n\t\t\t\tfont-size: inherit;\r\n\t\t\t}\r\n\t\r\n\t.tags-view-container .tags-view-wrapper .tags-view-item .el-icon-close {\r\n\t\t\t\tcolor: inherit;\r\n\t\t\t\tfont-size: inherit;\r\n\t\t\t}\r\n</style>\r\n"]}]}