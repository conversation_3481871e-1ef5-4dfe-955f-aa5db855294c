{"remainingRequest": "E:\\java系统\\公司财务系统\\src\\main\\resources\\admin\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\java系统\\公司财务系统\\src\\main\\resources\\admin\\admin\\src\\views\\modules\\jiangchengxinxi\\add-or-update.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\java系统\\公司财务系统\\src\\main\\resources\\admin\\admin\\src\\views\\modules\\jiangchengxinxi\\add-or-update.vue", "mtime": 1754641233285}, {"path": "E:\\java系统\\公司财务系统\\src\\main\\resources\\admin\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754805254356}, {"path": "E:\\java系统\\公司财务系统\\src\\main\\resources\\admin\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754805257322}, {"path": "E:\\java系统\\公司财务系统\\src\\main\\resources\\admin\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754805254356}, {"path": "E:\\java系统\\公司财务系统\\src\\main\\resources\\admin\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754628160828}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["add-or-update.vue"], "names": [], "mappings": ";AA4GA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;AAUA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;;;;;;;;;;AAUA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "add-or-update.vue", "sourceRoot": "src/views/modules/jiangchengxinxi", "sourcesContent": ["<template>\r\n\t<div class=\"addEdit-block\" :style='{\"padding\":\"30px\",\"margin\":\"0\"}'>\r\n\t\t<el-form\r\n\t\t\t:style='{\"border\":\"1px solid rgba(167, 180, 201,.3)  \",\"padding\":\"30px\",\"borderRadius\":\"6px\",\"background\":\"#fff\"}'\r\n\t\t\tclass=\"add-update-preview\"\r\n\t\t\tref=\"ruleForm\"\r\n\t\t\t:model=\"ruleForm\"\r\n\t\t\t:rules=\"rules\"\r\n\t\t\tlabel-width=\"80px\"\r\n\t\t>\r\n\t\t\t<template >\r\n\t\t\t\t<el-form-item :style='{\"margin\":\"0 0 20px 0\"}' class=\"select\" v-if=\"type!='info'\" label=\"工号\" prop=\"gonghao\">\r\n\t\t\t\t\t<el-select :disabled=\"ro.gonghao\" @change=\"gonghaoChange\" v-model=\"ruleForm.gonghao\" placeholder=\"请选择工号\">\r\n\t\t\t\t\t\t<el-option\r\n\t\t\t\t\t\t\tv-for=\"(item,index) in gonghaoOptions\"\r\n\t\t\t\t\t\t\tv-bind:key=\"index\"\r\n\t\t\t\t\t\t\t:label=\"item\"\r\n\t\t\t\t\t\t\t:value=\"item\">\r\n\t\t\t\t\t\t</el-option>\r\n\t\t\t\t\t</el-select>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<el-form-item :style='{\"margin\":\"0 0 20px 0\"}' class=\"input\" v-else-if=\"ruleForm.gonghao\" label=\"工号\" prop=\"gonghao\">\r\n\t\t\t\t\t<el-input v-model=\"ruleForm.gonghao\" placeholder=\"工号\" readonly></el-input>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<el-form-item :style='{\"margin\":\"0 0 20px 0\"}' class=\"input\" v-if=\"type!='info'\"  label=\"姓名\" prop=\"xingming\">\r\n\t\t\t\t\t<el-input v-model=\"ruleForm.xingming\" placeholder=\"姓名\" clearable  :readonly=\"ro.xingming\"></el-input>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<el-form-item :style='{\"margin\":\"0 0 20px 0\"}' v-else class=\"input\" label=\"姓名\" prop=\"xingming\">\r\n\t\t\t\t\t<el-input v-model=\"ruleForm.xingming\" placeholder=\"姓名\" readonly></el-input>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<el-form-item :style='{\"margin\":\"0 0 20px 0\"}' class=\"input\" v-if=\"type!='info'\"  label=\"部门\" prop=\"bumen\">\r\n\t\t\t\t\t<el-input v-model=\"ruleForm.bumen\" placeholder=\"部门\" clearable  :readonly=\"ro.bumen\"></el-input>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<el-form-item :style='{\"margin\":\"0 0 20px 0\"}' v-else class=\"input\" label=\"部门\" prop=\"bumen\">\r\n\t\t\t\t\t<el-input v-model=\"ruleForm.bumen\" placeholder=\"部门\" readonly></el-input>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<el-form-item :style='{\"margin\":\"0 0 20px 0\"}' class=\"input\" v-if=\"type!='info'\"  label=\"职位\" prop=\"zhiwei\">\r\n\t\t\t\t\t<el-input v-model=\"ruleForm.zhiwei\" placeholder=\"职位\" clearable  :readonly=\"ro.zhiwei\"></el-input>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<el-form-item :style='{\"margin\":\"0 0 20px 0\"}' v-else class=\"input\" label=\"职位\" prop=\"zhiwei\">\r\n\t\t\t\t\t<el-input v-model=\"ruleForm.zhiwei\" placeholder=\"职位\" readonly></el-input>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<el-form-item :style='{\"margin\":\"0 0 20px 0\"}' class=\"select\" v-if=\"type!='info'\"  label=\"奖惩类型\" prop=\"jiangchengleixing\">\r\n\t\t\t\t\t<el-select :disabled=\"ro.jiangchengleixing\" v-model=\"ruleForm.jiangchengleixing\" placeholder=\"请选择奖惩类型\" >\r\n\t\t\t\t\t\t<el-option\r\n\t\t\t\t\t\t\tv-for=\"(item,index) in jiangchengleixingOptions\"\r\n\t\t\t\t\t\t\tv-bind:key=\"index\"\r\n\t\t\t\t\t\t\t:label=\"item\"\r\n\t\t\t\t\t\t\t:value=\"item\">\r\n\t\t\t\t\t\t</el-option>\r\n\t\t\t\t\t</el-select>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<el-form-item :style='{\"margin\":\"0 0 20px 0\"}' v-else class=\"input\" label=\"奖惩类型\" prop=\"jiangchengleixing\">\r\n\t\t\t\t\t<el-input v-model=\"ruleForm.jiangchengleixing\"\r\n\t\t\t\t\t\tplaceholder=\"奖惩类型\" readonly></el-input>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<el-form-item :style='{\"margin\":\"0 0 20px 0\"}' class=\"input\" v-if=\"type!='info'\"  label=\"奖惩金额\" prop=\"jiangchengjine\">\r\n\t\t\t\t\t<el-input v-model.number=\"ruleForm.jiangchengjine\" placeholder=\"奖惩金额\" clearable  :readonly=\"ro.jiangchengjine\"></el-input>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<el-form-item :style='{\"margin\":\"0 0 20px 0\"}' v-else class=\"input\" label=\"奖惩金额\" prop=\"jiangchengjine\">\r\n\t\t\t\t\t<el-input v-model=\"ruleForm.jiangchengjine\" placeholder=\"奖惩金额\" readonly></el-input>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<el-form-item :style='{\"margin\":\"0 0 20px 0\"}' class=\"date\" v-if=\"type!='info'\" label=\"奖惩日期\" prop=\"jiangchengriqi\">\r\n\t\t\t\t\t<el-date-picker\r\n\t\t\t\t\t\tformat=\"yyyy �?MM �?dd �?\r\n\t\t\t\t\t\tvalue-format=\"yyyy-MM-dd\"\r\n\t\t\t\t\t\tv-model=\"ruleForm.jiangchengriqi\" \r\n\t\t\t\t\t\ttype=\"date\"\r\n\t\t\t\t\t\t:readonly=\"ro.jiangchengriqi\"\r\n\t\t\t\t\t\tplaceholder=\"奖惩日期\"\r\n\t\t\t\t\t></el-date-picker> \r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<el-form-item :style='{\"margin\":\"0 0 20px 0\"}' class=\"input\" v-else-if=\"ruleForm.jiangchengriqi\" label=\"奖惩日期\" prop=\"jiangchengriqi\">\r\n\t\t\t\t\t<el-input v-model=\"ruleForm.jiangchengriqi\" placeholder=\"奖惩日期\" readonly></el-input>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t</template>\r\n\t\t\t\t<el-form-item :style='{\"margin\":\"0 0 20px 0\"}' class=\"textarea\" v-if=\"type!='info'\" label=\"奖惩原因\" prop=\"jiangchengyuanyin\">\r\n\t\t\t\t\t<el-input\r\n\t\t\t\t\t  style=\"min-width: 200px; max-width: 600px;\"\r\n\t\t\t\t\t  type=\"textarea\"\r\n\t\t\t\t\t  :rows=\"8\"\r\n\t\t\t\t\t  placeholder=\"奖惩原因\"\r\n\t\t\t\t\t  v-model=\"ruleForm.jiangchengyuanyin\" >\r\n\t\t\t\t\t</el-input>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<el-form-item :style='{\"margin\":\"0 0 20px 0\"}' v-else-if=\"ruleForm.jiangchengyuanyin\" label=\"奖惩原因\" prop=\"jiangchengyuanyin\">\r\n\t\t\t\t\t<span :style='{\"fontSize\":\"14px\",\"lineHeight\":\"40px\",\"color\":\"#333\",\"fontWeight\":\"500\",\"display\":\"inline-block\"}'>{{ruleForm.jiangchengyuanyin}}</span>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t<el-form-item :style='{\"padding\":\"0\",\"margin\":\"0\"}' class=\"btn\">\r\n\t\t\t\t<el-button class=\"btn3\"  v-if=\"type!='info'\" type=\"success\" @click=\"onSubmit\">\r\n\t\t\t\t\t<span class=\"icon iconfont icon-xihuan\" :style='{\"margin\":\"0 2px\",\"fontSize\":\"14px\",\"color\":\"#fff\",\"height\":\"40px\"}'></span>\r\n\t\t\t\t\t提交\r\n\t\t\t\t</el-button>\r\n\t\t\t\t<el-button class=\"btn4\" v-if=\"type!='info'\" type=\"success\" @click=\"back()\">\r\n\t\t\t\t\t<span class=\"icon iconfont icon-xihuan\" :style='{\"margin\":\"0 2px\",\"fontSize\":\"14px\",\"color\":\"#fff\",\"height\":\"40px\"}'></span>\r\n\t\t\t\t\t取消\r\n\t\t\t\t</el-button>\r\n\t\t\t\t<el-button class=\"btn5\" v-if=\"type=='info'\" type=\"success\" @click=\"back()\">\r\n\t\t\t\t\t<span class=\"icon iconfont icon-xihuan\" :style='{\"margin\":\"0 2px\",\"fontSize\":\"14px\",\"color\":\"#fff\",\"height\":\"40px\"}'></span>\r\n\t\t\t\t\t返回\r\n\t\t\t\t</el-button>\r\n\t\t\t</el-form-item>\r\n\t\t</el-form>\r\n    \r\n\r\n  </div>\r\n</template>\r\n<script>\r\n// 数字，邮件，手机，url，身份证校验\r\nimport { isNumber,isIntNumer,isEmail,isPhone, isMobile,isURL,checkIdCard } from \"@/utils/validate\";\r\nexport default {\r\n\tdata() {\r\n\t\tlet self = this\r\n\t\tvar validateIdCard = (rule, value, callback) => {\r\n\t\t\tif(!value){\r\n\t\t\t\tcallback();\r\n\t\t\t} else if (!checkIdCard(value)) {\r\n\t\t\t\tcallback(new Error(\"请输入正确的身份证号?\"));\r\n\t\t\t} else {\r\n\t\t\t\tcallback();\r\n\t\t\t}\r\n\t\t};\r\n\t\tvar validateUrl = (rule, value, callback) => {\r\n\t\t\tif(!value){\r\n\t\t\t\tcallback();\r\n\t\t\t} else if (!isURL(value)) {\r\n\t\t\t\tcallback(new Error(\"请输入正确的URL地址\"));\r\n\t\t\t} else {\r\n\t\t\t\tcallback();\r\n\t\t\t}\r\n\t\t};\r\n\t\tvar validateMobile = (rule, value, callback) => {\r\n\t\t\tif(!value){\r\n\t\t\t\tcallback();\r\n\t\t\t} else if (!isMobile(value)) {\r\n\t\t\t\tcallback(new Error(\"请输入正确的手机号码\"));\r\n\t\t\t} else {\r\n\t\t\t\tcallback();\r\n\t\t\t}\r\n\t\t};\r\n\t\tvar validatePhone = (rule, value, callback) => {\r\n\t\t\tif(!value){\r\n\t\t\t\tcallback();\r\n\t\t\t} else if (!isPhone(value)) {\r\n\t\t\t\tcallback(new Error(\"请输入正确的电话号码\"));\r\n\t\t\t} else {\r\n\t\t\t\tcallback();\r\n\t\t\t}\r\n\t\t};\r\n\t\tvar validateEmail = (rule, value, callback) => {\r\n\t\t\tif(!value){\r\n\t\t\t\tcallback();\r\n\t\t\t} else if (!isEmail(value)) {\r\n\t\t\t\tcallback(new Error(\"请输入正确的邮箱地址\"));\r\n\t\t\t} else {\r\n\t\t\t\tcallback();\r\n\t\t\t}\r\n\t\t};\r\n\t\tvar validateNumber = (rule, value, callback) => {\r\n\t\t\tif(!value){\r\n\t\t\t\tcallback();\r\n\t\t\t} else if (!isNumber(value)) {\r\n\t\t\t\tcallback(new Error(\"请输入数字?\"));\r\n\t\t\t} else {\r\n\t\t\t\tcallback();\r\n\t\t\t}\r\n\t\t};\r\n\t\tvar validateIntNumber = (rule, value, callback) => {\r\n\t\t\tif(!value){\r\n\t\t\t\tcallback();\r\n\t\t\t} else if (!isIntNumer(value)) {\r\n\t\t\t\tcallback(new Error(\"请输入整数?\"));\r\n\t\t\t} else {\r\n\t\t\t\tcallback();\r\n\t\t\t}\r\n\t\t};\r\n\t\treturn {\r\n\t\t\tid: '',\r\n\t\t\ttype: '',\r\n\t\t\t\r\n\t\t\t\r\n\t\t\tro:{\r\n\t\t\t\tgonghao : false,\r\n\t\t\t\txingming : false,\r\n\t\t\t\tbumen : false,\r\n\t\t\t\tzhiwei : false,\r\n\t\t\t\tjiangchengleixing : false,\r\n\t\t\t\tjiangchengjine : false,\r\n\t\t\t\tjiangchengyuanyin : false,\r\n\t\t\t\tjiangchengriqi : false,\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t\r\n\t\t\truleForm: {\r\n\t\t\t\tgonghao: '',\r\n\t\t\t\txingming: '',\r\n\t\t\t\tbumen: '',\r\n\t\t\t\tzhiwei: '',\r\n\t\t\t\tjiangchengleixing: '',\r\n\t\t\t\tjiangchengjine: '',\r\n\t\t\t\tjiangchengyuanyin: '',\r\n\t\t\t\tjiangchengriqi: '',\r\n\t\t\t},\r\n\t\t\r\n\t\t\tgonghaoOptions: [],\r\n\t\t\tjiangchengleixingOptions: [],\r\n\r\n\t\t\t\r\n\t\t\trules: {\r\n\t\t\t\tgonghao: [\r\n\t\t\t\t],\r\n\t\t\t\txingming: [\r\n\t\t\t\t],\r\n\t\t\t\tbumen: [\r\n\t\t\t\t],\r\n\t\t\t\tzhiwei: [\r\n\t\t\t\t],\r\n\t\t\t\tjiangchengleixing: [\r\n\t\t\t\t\t{ required: true, message: '奖惩类型不能为空', trigger: 'blur' },\r\n\t\t\t\t],\r\n\t\t\t\tjiangchengjine: [\r\n\t\t\t\t\t{ required: true, message: '奖惩金额不能为空', trigger: 'blur' },\r\n\t\t\t\t\t{ validator: validateIntNumber, trigger: 'blur' },\r\n\t\t\t\t],\r\n\t\t\t\tjiangchengyuanyin: [\r\n\t\t\t\t\t{ required: true, message: '奖惩原因不能为空', trigger: 'blur' },\r\n\t\t\t\t],\r\n\t\t\t\tjiangchengriqi: [\r\n\t\t\t\t],\r\n\t\t\t}\r\n\t\t};\r\n\t},\r\n\tprops: [\"parent\"],\r\n\tcomputed: {\r\n\r\n\r\n\r\n\t},\r\n    components: {\r\n    },\r\n\tcreated() {\r\n\t\tthis.ruleForm.jiangchengriqi = this.getCurDate()\r\n\t},\r\n\tmethods: {\r\n\t\t\r\n\t\t// 下载\r\n\t\tdownload(file){\r\n\t\t\twindow.open(`${file}`)\r\n\t\t},\r\n\t\t// 初始�?\r\n\t\tinit(id,type) {\r\n\t\t\tif (id) {\r\n\t\t\t\tthis.id = id;\r\n\t\t\t\tthis.type = type;\r\n\t\t\t}\r\n\t\t\tif(this.type=='info'||this.type=='else'){\r\n\t\t\t\tthis.info(id);\r\n\t\t\t}else if(this.type=='logistics'){\r\n\t\t\t\tthis.logistics=false;\r\n\t\t\t\tthis.info(id);\r\n\t\t\t}else if(this.type=='cross'){\r\n\t\t\t\tvar obj = this.$storage.getObj('crossObj');\r\n\t\t\t\tfor (var o in obj){\r\n\t\t\t\t\t\tif(o=='gonghao'){\r\n\t\t\t\t\t\t\tthis.ruleForm.gonghao = obj[o];\r\n\t\t\t\t\t\t\tthis.ro.gonghao = true;\r\n\t\t\t\t\t\t\tcontinue;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif(o=='xingming'){\r\n\t\t\t\t\t\t\tthis.ruleForm.xingming = obj[o];\r\n\t\t\t\t\t\t\tthis.ro.xingming = true;\r\n\t\t\t\t\t\t\tcontinue;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif(o=='bumen'){\r\n\t\t\t\t\t\t\tthis.ruleForm.bumen = obj[o];\r\n\t\t\t\t\t\t\tthis.ro.bumen = true;\r\n\t\t\t\t\t\t\tcontinue;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif(o=='zhiwei'){\r\n\t\t\t\t\t\t\tthis.ruleForm.zhiwei = obj[o];\r\n\t\t\t\t\t\t\tthis.ro.zhiwei = true;\r\n\t\t\t\t\t\t\tcontinue;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif(o=='jiangchengleixing'){\r\n\t\t\t\t\t\t\tthis.ruleForm.jiangchengleixing = obj[o];\r\n\t\t\t\t\t\t\tthis.ro.jiangchengleixing = true;\r\n\t\t\t\t\t\t\tcontinue;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif(o=='jiangchengjine'){\r\n\t\t\t\t\t\t\tthis.ruleForm.jiangchengjine = obj[o];\r\n\t\t\t\t\t\t\tthis.ro.jiangchengjine = true;\r\n\t\t\t\t\t\t\tcontinue;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif(o=='jiangchengyuanyin'){\r\n\t\t\t\t\t\t\tthis.ruleForm.jiangchengyuanyin = obj[o];\r\n\t\t\t\t\t\t\tthis.ro.jiangchengyuanyin = true;\r\n\t\t\t\t\t\t\tcontinue;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif(o=='jiangchengriqi'){\r\n\t\t\t\t\t\t\tthis.ruleForm.jiangchengriqi = obj[o];\r\n\t\t\t\t\t\t\tthis.ro.jiangchengriqi = true;\r\n\t\t\t\t\t\t\tcontinue;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t// 获取用户信息\r\n\t\t\tthis.$http({\r\n\t\t\t\turl: `${this.$storage.get('sessionTable')}/session`,\r\n\t\t\t\tmethod: \"get\"\r\n\t\t\t}).then(({ data }) => {\r\n\t\t\t\tif (data && data.code === 0) {\r\n\t\t\t\t\t\r\n\t\t\t\t\tvar json = data.data;\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.$message.error(data.msg);\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t\t\r\n            this.$http({\r\n\t\t\t\turl: `option/yuangong/gonghao`,\r\n\t\t\t\tmethod: \"get\"\r\n            }).then(({ data }) => {\r\n\t\t\t\tif (data && data.code === 0) {\r\n\t\t\t\t\tthis.gonghaoOptions = data.data;\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.$message.error(data.msg);\r\n\t\t\t\t}\r\n            });\r\n            this.jiangchengleixingOptions = \"表扬,嘉奖,警告,记过\".split(',')\r\n\t\t\t\r\n\t\t},\r\n\t\t\t// 下二�?\r\n\t\t\tgonghaoChange () {\r\n\t\t\t\tthis.$http({\r\n\t\t\t\t\turl: `follow/yuangong/gonghao?columnValue=`+ this.ruleForm.gonghao,\r\n\t\t\t\t\tmethod: \"get\"\r\n\t\t\t\t}).then(({ data }) => {\r\n\t\t\t\t\tif (data && data.code === 0) {\r\n\t\t\t\t\t\tif(data.data.xingming){\r\n\t\t\t\t\t\t\tthis.ruleForm.xingming = data.data.xingming\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif(data.data.bumen){\r\n\t\t\t\t\t\t\tthis.ruleForm.bumen = data.data.bumen\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif(data.data.zhiwei){\r\n\t\t\t\t\t\t\tthis.ruleForm.zhiwei = data.data.zhiwei\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tthis.$message.error(data.msg);\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n    // 多级联动参数\r\n\r\n    info(id) {\r\n      this.$http({\r\n        url: `jiangchengxinxi/info/${id}`,\r\n        method: \"get\"\r\n      }).then(({ data }) => {\r\n        if (data && data.code === 0) {\r\n        this.ruleForm = data.data;\r\n        //解决前台上传图片后台不显示的问题\r\n        let reg=new RegExp('../../../upload','g')//g代表全部\r\n        } else {\r\n          this.$message.error(data.msg);\r\n        }\r\n      });\r\n    },\r\n\r\n\r\n    // 提交\r\n    onSubmit() {\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\nvar objcross = this.$storage.getObj('crossObj');\r\n      //更新跨表属�?\r\n       var crossuserid;\r\n       var crossrefid;\r\n       var crossoptnum;\r\n       if(this.type=='cross'){\r\n                var statusColumnName = this.$storage.get('statusColumnName');\r\n                var statusColumnValue = this.$storage.get('statusColumnValue');\r\n                if(statusColumnName!='') {\r\n                        var obj = this.$storage.getObj('crossObj');\r\n                       if(statusColumnName && !statusColumnName.startsWith(\"[\")) {\r\n                               for (var o in obj){\r\n                                 if(o==statusColumnName){\r\n                                   obj[o] = statusColumnValue;\r\n                                 }\r\n                               }\r\n                               var table = this.$storage.get('crossTable');\r\n                             this.$http({\r\n                                 url: `${table}/update`,\r\n                                 method: \"post\",\r\n                                 data: obj\r\n                               }).then(({ data }) => {});\r\n                       } else {\r\n                               crossuserid=this.$storage.get('userid');\r\n                               crossrefid=obj['id'];\r\n                               crossoptnum=this.$storage.get('statusColumnName');\r\n                               crossoptnum=crossoptnum.replace(/\\[/,\"\").replace(/\\]/,\"\");\r\n                        }\r\n                }\r\n        }\r\n\t\tthis.$refs[\"ruleForm\"].validate(valid => {\r\n\t\t\tif (valid) {\r\n\t\t\t\tif(crossrefid && crossuserid) {\r\n\t\t\t\t\tthis.ruleForm.crossuserid = crossuserid;\r\n\t\t\t\t\tthis.ruleForm.crossrefid = crossrefid;\r\n\t\t\t\t\tlet params = { \r\n\t\t\t\t\t\tpage: 1, \r\n\t\t\t\t\t\tlimit: 10, \r\n\t\t\t\t\t\tcrossuserid:this.ruleForm.crossuserid,\r\n\t\t\t\t\t\tcrossrefid:this.ruleForm.crossrefid,\r\n\t\t\t\t\t} \r\n\t\t\t\tthis.$http({ \r\n\t\t\t\t\turl: \"jiangchengxinxi/page\", \r\n\t\t\t\t\tmethod: \"get\", \r\n\t\t\t\t\tparams: params \r\n\t\t\t\t}).then(({ \r\n\t\t\t\t\tdata \r\n\t\t\t\t}) => { \r\n\t\t\t\t\tif (data && data.code === 0) { \r\n\t\t\t\t\t\tif(data.data.total>=crossoptnum) {\r\n\t\t\t\t\t\t\tthis.$message.error(this.$storage.get('tips'));\r\n\t\t\t\t\t\t\treturn false;\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tthis.$http({\r\n\t\t\t\t\t\t\t\turl: `jiangchengxinxi/${!this.ruleForm.id ? \"save\" : \"update\"}`,\r\n\t\t\t\t\t\t\t\tmethod: \"post\",\r\n\t\t\t\t\t\t\t\tdata: this.ruleForm\r\n\t\t\t\t\t\t\t}).then(({ data }) => {\r\n\t\t\t\t\t\t\t\tif (data && data.code === 0) {\r\n\t\t\t\t\t\t\t\t\tthis.$message({\r\n\t\t\t\t\t\t\t\t\t\tmessage: \"操作成功\",\r\n\t\t\t\t\t\t\t\t\t\ttype: \"success\",\r\n\t\t\t\t\t\t\t\t\t\tduration: 1500,\r\n\t\t\t\t\t\t\t\t\t\tonClose: () => {\r\n\t\t\t\t\t\t\t\t\t\t\tthis.parent.showFlag = true;\r\n\t\t\t\t\t\t\t\t\t\t\tthis.parent.addOrUpdateFlag = false;\r\n\t\t\t\t\t\t\t\t\t\t\tthis.parent.jiangchengxinxiCrossAddOrUpdateFlag = false;\r\n\t\t\t\t\t\t\t\t\t\t\tthis.parent.search();\r\n\t\t\t\t\t\t\t\t\t\t\tthis.parent.contentStyleChange();\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\tthis.$message.error(data.msg);\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t});\r\n\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t} else { \r\n\t\t\t\t} \r\n\t\t\t});\r\n\t\t} else {\r\n\t\t\tthis.$http({\r\n\t\t\t\turl: `jiangchengxinxi/${!this.ruleForm.id ? \"save\" : \"update\"}`,\r\n\t\t\t\tmethod: \"post\",\r\n\t\t\t   data: this.ruleForm\r\n\t\t\t}).then(({ data }) => {\r\n\t\t\t\tif (data && data.code === 0) {\r\n\t\t\t\t\tthis.$message({\r\n\t\t\t\t\t\tmessage: \"操作成功\",\r\n\t\t\t\t\t\ttype: \"success\",\r\n\t\t\t\t\t\tduration: 1500,\r\n\t\t\t\t\t\tonClose: () => {\r\n\t\t\t\t\t\t\tthis.parent.showFlag = true;\r\n\t\t\t\t\t\t\tthis.parent.addOrUpdateFlag = false;\r\n\t\t\t\t\t\t\tthis.parent.jiangchengxinxiCrossAddOrUpdateFlag = false;\r\n\t\t\t\t\t\t\tthis.parent.search();\r\n\t\t\t\t\t\t\tthis.parent.contentStyleChange();\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t});\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.$message.error(data.msg);\r\n\t\t\t   }\r\n\t\t\t});\r\n\t\t }\r\n         }\r\n       });\r\n    },\r\n    // 获取uuid\r\n    getUUID () {\r\n      return new Date().getTime();\r\n    },\r\n    // 返回\r\n    back() {\r\n      this.parent.showFlag = true;\r\n      this.parent.addOrUpdateFlag = false;\r\n      this.parent.jiangchengxinxiCrossAddOrUpdateFlag = false;\r\n      this.parent.contentStyleChange();\r\n    },\r\n  }\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n\t.amap-wrapper {\r\n\t\twidth: 100%;\r\n\t\theight: 500px;\r\n\t}\r\n\t\r\n\t.search-box {\r\n\t\tposition: absolute;\r\n\t}\r\n\t\r\n\t.el-date-editor.el-input {\r\n\t\twidth: auto;\r\n\t}\r\n\t\r\n\t.add-update-preview .el-form-item :deep .el-form-item__label {\r\n\t  \t  padding: 0 10px 0 0;\r\n\t  \t  color: #374254;\r\n\t  \t  font-weight: 600;\r\n\t  \t  width: 80px;\r\n\t  \t  font-size: 14px;\r\n\t  \t  line-height: 40px;\r\n\t  \t  text-align: right;\r\n\t  \t}\r\n\t\r\n\t.add-update-preview .el-form-item :deep .el-form-item__content {\r\n\t  margin-left: 80px;\r\n\t}\r\n\t\r\n\t.add-update-preview .el-input :deep .el-input__inner {\r\n\t  \t  border: 1px solid rgba(167, 180, 201,.3);\r\n\t  \t  border-radius: 4px;\r\n\t  \t  padding: 0 12px;\r\n\t  \t  outline: none;\r\n\t  \t  color: #a7b4c9;\r\n\t  \t  width: 400px;\r\n\t  \t  font-size: 14px;\r\n\t  \t  height: 40px;\r\n\t  \t}\r\n\t.add-update-preview .el-input-number :deep .el-input__inner {\r\n\t\ttext-align: left;\r\n\t  \t  border: 1px solid rgba(167, 180, 201,.3);\r\n\t  \t  border-radius: 4px;\r\n\t  \t  padding: 0 12px;\r\n\t  \t  outline: none;\r\n\t  \t  color: #a7b4c9;\r\n\t  \t  width: 400px;\r\n\t  \t  font-size: 14px;\r\n\t  \t  height: 40px;\r\n\t  \t}\r\n\t.add-update-preview .el-input-number :deep .el-input-number__decrease {\r\n\t\tdisplay: none;\r\n\t}\r\n\t.add-update-preview .el-input-number :deep .el-input-number__increase {\r\n\t\tdisplay: none;\r\n\t}\r\n\t\r\n\t.add-update-preview .el-select :deep .el-input__inner {\r\n\t  \t  border:  1px solid rgba(167, 180, 201,.3) ;\r\n\t  \t  border-radius: 4px;\r\n\t  \t  padding: 0 10px;\r\n\t  \t  outline: none;\r\n\t  \t  color: #a7b4c9;\r\n\t  \t  width: 400px;\r\n\t  \t  font-size: 14px;\r\n\t  \t  height: 40px;\r\n\t  \t}\r\n\t\r\n\t.add-update-preview .el-date-editor :deep .el-input__inner {\r\n\t  \t  border:  1px solid rgba(167, 180, 201,.3) ;\r\n\t  \t  border-radius: 4px;\r\n\t  \t  padding: 0 10px 0 30px;\r\n\t  \t  outline: none;\r\n\t  \t  color: #a7b4c9;\r\n\t  \t  width: 400px;\r\n\t  \t  font-size: 14px;\r\n\t  \t  height: 40px;\r\n\t  \t}\r\n\t\r\n\t.add-update-preview :deep .el-upload--picture-card {\r\n\t\tbackground: transparent;\r\n\t\tborder: 0;\r\n\t\tborder-radius: 0;\r\n\t\twidth: auto;\r\n\t\theight: auto;\r\n\t\tline-height: initial;\r\n\t\tvertical-align: middle;\r\n\t}\r\n\t\r\n\t.add-update-preview :deep .upload .upload-img {\r\n\t  \t  border: 2px dashed rgba(167, 180, 201,.3) ;\r\n\t  \t  cursor: pointer;\r\n\t  \t  border-radius: 6px;\r\n\t  \t  color: #a7b4c9  ;\r\n\t  \t  width: 200px;\r\n\t  \t  font-size: 32px;\r\n\t  \t  line-height: 200px;\r\n\t  \t  text-align: center;\r\n\t  \t  height: 200px;\r\n\t  \t}\r\n\t\r\n\t.add-update-preview :deep .el-upload-list .el-upload-list__item {\r\n\t  \t  border: 2px dashed rgba(167, 180, 201,.3) ;\r\n\t  \t  cursor: pointer;\r\n\t  \t  border-radius: 6px;\r\n\t  \t  color: #a7b4c9  ;\r\n\t  \t  width: 200px;\r\n\t  \t  font-size: 32px;\r\n\t  \t  line-height: 200px;\r\n\t  \t  text-align: center;\r\n\t  \t  height: 200px;\r\n\t  \t}\r\n\t\r\n\t.add-update-preview :deep .el-upload .el-icon-plus {\r\n\t  \t  border: 2px dashed rgba(167, 180, 201,.3) ;\r\n\t  \t  cursor: pointer;\r\n\t  \t  border-radius: 6px;\r\n\t  \t  color: #a7b4c9  ;\r\n\t  \t  width: 200px;\r\n\t  \t  font-size: 32px;\r\n\t  \t  line-height: 200px;\r\n\t  \t  text-align: center;\r\n\t  \t  height: 200px;\r\n\t  \t}\r\n\t\r\n\t.add-update-preview .el-textarea :deep .el-textarea__inner {\r\n\t  \t  border: 1px solid rgba(167, 180, 201,.3)     ;\r\n\t  \t  border-radius: 4px;\r\n\t  \t  padding: 12px;\r\n\t  \t  outline: none;\r\n\t  \t  color: #a7b4c9 ;\r\n\t  \t  width: 400px;\r\n\t  \t  font-size: 14px;\r\n\t  \t  height: 120px;\r\n\t  \t}\r\n\t\r\n\t.add-update-preview .btn .btn1 {\r\n\t\t\t\tborder: 0;\r\n\t\t\t\tcursor: pointer;\r\n\t\t\t\tborder-radius: 4px;\r\n\t\t\t\tpadding: 0 24px;\r\n\t\t\t\tmargin: 4px;\r\n\t\t\t\toutline: none;\r\n\t\t\t\tcolor: #fff;\r\n\t\t\t\tbackground: #6574cd;\r\n\t\t\t\twidth: auto;\r\n\t\t\t\tfont-size: 14px;\r\n\t\t\t\theight: 40px;\r\n\t\t\t}\r\n\t\r\n\t.add-update-preview .btn .btn1:hover {\r\n\t\t\t\topacity: 0.8;\r\n\t\t\t}\r\n\t\r\n\t.add-update-preview .btn .btn2 {\r\n\t\t\t\tborder: 0;\r\n\t\t\t\tcursor: pointer;\r\n\t\t\t\tborder-radius: 4px;\r\n\t\t\t\tpadding: 0 24px;\r\n\t\t\t\tmargin: 4px;\r\n\t\t\t\toutline: none;\r\n\t\t\t\tcolor: #fff;\r\n\t\t\t\tbackground: #6574cd;\r\n\t\t\t\twidth: auto;\r\n\t\t\t\tfont-size: 14px;\r\n\t\t\t\theight: 40px;\r\n\t\t\t}\r\n\t\r\n\t.add-update-preview .btn .btn2:hover {\r\n\t\t\t\topacity: 0.8;\r\n\t\t\t}\r\n\t\r\n\t.add-update-preview .btn .btn3 {\r\n\t\t\t\tborder: 0;\r\n\t\t\t\tcursor: pointer;\r\n\t\t\t\tborder-radius: 4px;\r\n\t\t\t\tpadding: 0 24px;\r\n\t\t\t\tmargin: 4px;\r\n\t\t\t\toutline: none;\r\n\t\t\t\tcolor: #fff;\r\n\t\t\t\tbackground: #ff2b88;\r\n\t\t\t\twidth: auto;\r\n\t\t\t\tfont-size: 14px;\r\n\t\t\t\theight: 40px;\r\n\t\t\t}\r\n\t\r\n\t.add-update-preview .btn .btn3:hover {\r\n\t\t\t\topacity: 0.8;\r\n\t\t\t}\r\n\t\r\n\t.add-update-preview .btn .btn4 {\r\n\t\t\t\tborder: 0;\r\n\t\t\t\tcursor: pointer;\r\n\t\t\t\tborder-radius: 4px;\r\n\t\t\t\tpadding: 0 24px;\r\n\t\t\t\tmargin: 4px;\r\n\t\t\t\toutline: none;\r\n\t\t\t\tcolor: #fff;\r\n\t\t\t\tbackground: #ff2b88;\r\n\t\t\t\twidth: auto;\r\n\t\t\t\tfont-size: 14px;\r\n\t\t\t\theight: 40px;\r\n\t\t\t}\r\n\t\r\n\t.add-update-preview .btn .btn4:hover {\r\n\t\t\t\topacity: 0.8;\r\n\t\t\t}\r\n\t\r\n\t.add-update-preview .btn .btn5 {\r\n\t\t\t\tborder: 0;\r\n\t\t\t\tcursor: pointer;\r\n\t\t\t\tborder-radius: 4px;\r\n\t\t\t\tpadding: 0 24px;\r\n\t\t\t\tmargin: 4px;\r\n\t\t\t\toutline: none;\r\n\t\t\t\tcolor: #fff;\r\n\t\t\t\tbackground: #ff2b88;\r\n\t\t\t\twidth: auto;\r\n\t\t\t\tfont-size: 14px;\r\n\t\t\t\theight: 40px;\r\n\t\t\t}\r\n\t\r\n\t.add-update-preview .btn .btn5:hover {\r\n\t\t\t\topacity: 0.8;\r\n\t\t\t}\r\n</style>\r\n"]}]}