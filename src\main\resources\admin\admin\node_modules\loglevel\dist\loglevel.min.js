/*! loglevel - v1.9.2 - https://github.com/pimterry/loglevel - (c) 2024 <PERSON> - licensed MIT */

!function(e,o){"use strict";"function"==typeof define&&define.amd?define(o):"object"==typeof module&&module.exports?module.exports=o():e.log=o()}(this,function(){"use strict";var l=function(){},f="undefined",i=typeof window!==f&&typeof window.navigator!==f&&/Trident\/|MSIE /.test(window.navigator.userAgent),s=["trace","debug","info","warn","error"],p={},d=null;function r(o,e){var n=o[e];if("function"==typeof n.bind)return n.bind(o);try{return Function.prototype.bind.call(n,o)}catch(e){return function(){return Function.prototype.apply.apply(n,[o,arguments])}}}function c(){console.log&&(console.log.apply?console.log.apply(console,arguments):Function.prototype.apply.apply(console.log,[console,arguments])),console.trace&&console.trace()}function v(){for(var e=this.getLevel(),o=0;o<s.length;o++){var n=s[o];this[n]=o<e?l:this.methodFactory(n,e,this.name)}if(this.log=this.debug,typeof console===f&&e<this.levels.SILENT)return"No console available for logging"}function y(e,o,n){return"debug"===(t=e)&&(t="log"),typeof console!==f&&("trace"===t&&i?c:void 0!==console[t]?r(console,t):void 0!==console.log?r(console,"log"):l)||function(e){return function(){typeof console!==f&&(v.call(this),this[e].apply(this,arguments))}}.apply(this,arguments);var t}function n(e,o){var n,t,l,i=this,r="loglevel";function c(){var e;if(typeof window!==f&&r){try{e=window.localStorage[r]}catch(e){}if(typeof e===f)try{var o=window.document.cookie,n=encodeURIComponent(r),t=o.indexOf(n+"=");-1!==t&&(e=/^([^;]+)/.exec(o.slice(t+n.length+1))[1])}catch(e){}return void 0===i.levels[e]&&(e=void 0),e}}function a(e){var o=e;if("string"==typeof o&&void 0!==i.levels[o.toUpperCase()]&&(o=i.levels[o.toUpperCase()]),"number"==typeof o&&0<=o&&o<=i.levels.SILENT)return o;throw new TypeError("log.setLevel() called with invalid level: "+e)}"string"==typeof e?r+=":"+e:"symbol"==typeof e&&(r=void 0),i.name=e,i.levels={TRACE:0,DEBUG:1,INFO:2,WARN:3,ERROR:4,SILENT:5},i.methodFactory=o||y,i.getLevel=function(){return null!=l?l:null!=t?t:n},i.setLevel=function(e,o){return l=a(e),!1!==o&&function(e){var o=(s[e]||"silent").toUpperCase();if(typeof window!==f&&r){try{return window.localStorage[r]=o}catch(e){}try{window.document.cookie=encodeURIComponent(r)+"="+o+";"}catch(e){}}}(l),v.call(i)},i.setDefaultLevel=function(e){t=a(e),c()||i.setLevel(e,!1)},i.resetLevel=function(){l=null,function(){if(typeof window!==f&&r){try{window.localStorage.removeItem(r)}catch(e){}try{window.document.cookie=encodeURIComponent(r)+"=; expires=Thu, 01 Jan 1970 00:00:00 UTC"}catch(e){}}}(),v.call(i)},i.enableAll=function(e){i.setLevel(i.levels.TRACE,e)},i.disableAll=function(e){i.setLevel(i.levels.SILENT,e)},i.rebuild=function(){if(d!==i&&(n=a(d.getLevel())),v.call(i),d===i)for(var e in p)p[e].rebuild()},n=a(d?d.getLevel():"WARN");var u=c();null!=u&&(l=a(u)),v.call(i)}(d=new n).getLogger=function(e){if("symbol"!=typeof e&&"string"!=typeof e||""===e)throw new TypeError("You must supply a name when creating a logger.");var o=p[e];return o||(o=p[e]=new n(e,d.methodFactory)),o};var e=typeof window!==f?window.log:void 0;return d.noConflict=function(){return typeof window!==f&&window.log===d&&(window.log=e),d},d.getLoggers=function(){return p},d.default=d});