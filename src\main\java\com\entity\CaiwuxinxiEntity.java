package com.entity;

import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.annotations.TableName;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import java.lang.reflect.InvocationTargetException;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

import org.springframework.format.annotation.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.beanutils.BeanUtils;
import com.baomidou.mybatisplus.annotations.TableField;
import com.baomidou.mybatisplus.enums.FieldFill;
import com.baomidou.mybatisplus.enums.IdType;


/**
 * 财务信息
 * 数据库通用操作实体类（普通增删改查）
 * <AUTHOR> @email 
 * @date 2024-02-19 14:47:31
 */
@TableName("caiwuxinxi")
public class CaiwuxinxiEntity<T> implements Serializable {
	private static final long serialVersionUID = 1L;


	public CaiwuxinxiEntity() {
		
	}
	
	public CaiwuxinxiEntity(T t) {
		try {
			BeanUtils.copyProperties(this, t);
		} catch (IllegalAccessException | InvocationTargetException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
	}
	
	/**
	 * 主键id
	 */
    @TableId(type = IdType.AUTO)
    private Long id;
	/**
	 * 统计编号
	 */
					
	private String tongjibianhao;
	
	/**
	 * 月份
	 */
					
	private String yuefen;
	
	/**
	 * 收入金额
	 */
					
	private Double shourujine;
	
	/**
	 * 支出金额
	 */
					
	private Double zhichujine;
	
	/**
	 * 利润
	 */
					
	private Double lirun;
	
	/**
	 * 登记日期
	 */
				
	@JsonFormat(locale="zh", timezone="GMT+8", pattern="yyyy-MM-dd")
	@DateTimeFormat 		
	private Date dengjiriqi;
	
	/**
	 * 收入日期
	 */
				
	@JsonFormat(locale="zh", timezone="GMT+8", pattern="yyyy-MM-dd")
	@DateTimeFormat 		
	private Date shoururiqi;
	
	/**
	 * 收入来源
	 */
					
	private String shourulaiyuan;
	
	/**
	 * 支出时间
	 */
				
	@JsonFormat(locale="zh", timezone="GMT+8", pattern="yyyy-MM-dd")
	@DateTimeFormat 		
	private Date zhichushijian;
	
	/**
	 * 支出原因
	 */
					
	private String zhichuyuanyin;
	
	
	@JsonFormat(locale="zh", timezone="GMT+8", pattern="yyyy-MM-dd HH:mm:ss")
	@DateTimeFormat
	private Date addtime;

	public Date getAddtime() {
		return addtime;
	}
	public void setAddtime(Date addtime) {
		this.addtime = addtime;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}
	/**
	 * 设置：统计编号
	 */
	public void setTongjibianhao(String tongjibianhao) {
		this.tongjibianhao = tongjibianhao;
	}
	/**
	 * 获取：统计编号
	 */
	public String getTongjibianhao() {
		return tongjibianhao;
	}
	/**
	 * 设置：月份
	 */
	public void setYuefen(String yuefen) {
		this.yuefen = yuefen;
	}
	/**
	 * 获取：月份
	 */
	public String getYuefen() {
		return yuefen;
	}
	/**
	 * 设置：收入金额
	 */
	public void setShourujine(Double shourujine) {
		this.shourujine = shourujine;
	}
	/**
	 * 获取：收入金额
	 */
	public Double getShourujine() {
		return shourujine;
	}
	/**
	 * 设置：支出金额
	 */
	public void setZhichujine(Double zhichujine) {
		this.zhichujine = zhichujine;
	}
	/**
	 * 获取：支出金额
	 */
	public Double getZhichujine() {
		return zhichujine;
	}
	/**
	 * 设置：利润
	 */
	public void setLirun(Double lirun) {
		this.lirun = lirun;
	}
	/**
	 * 获取：利润
	 */
	public Double getLirun() {
		return lirun;
	}
	/**
	 * 设置：登记日期
	 */
	public void setDengjiriqi(Date dengjiriqi) {
		this.dengjiriqi = dengjiriqi;
	}
	/**
	 * 获取：登记日期
	 */
	public Date getDengjiriqi() {
		return dengjiriqi;
	}
	/**
	 * 设置：收入日期
	 */
	public void setShoururiqi(Date shoururiqi) {
		this.shoururiqi = shoururiqi;
	}
	/**
	 * 获取：收入日期
	 */
	public Date getShoururiqi() {
		return shoururiqi;
	}
	/**
	 * 设置：收入来源
	 */
	public void setShourulaiyuan(String shourulaiyuan) {
		this.shourulaiyuan = shourulaiyuan;
	}
	/**
	 * 获取：收入来源
	 */
	public String getShourulaiyuan() {
		return shourulaiyuan;
	}
	/**
	 * 设置：支出时间
	 */
	public void setZhichushijian(Date zhichushijian) {
		this.zhichushijian = zhichushijian;
	}
	/**
	 * 获取：支出时间
	 */
	public Date getZhichushijian() {
		return zhichushijian;
	}
	/**
	 * 设置：支出原因
	 */
	public void setZhichuyuanyin(String zhichuyuanyin) {
		this.zhichuyuanyin = zhichuyuanyin;
	}
	/**
	 * 获取：支出原因
	 */
	public String getZhichuyuanyin() {
		return zhichuyuanyin;
	}

}
