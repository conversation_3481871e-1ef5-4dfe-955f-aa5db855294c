{"remainingRequest": "E:\\java系统\\公司财务系统\\src\\main\\resources\\admin\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\java系统\\公司财务系统\\src\\main\\resources\\admin\\admin\\src\\components\\index\\TagsView\\index.vue?vue&type=style&index=0&id=2b3b3dee&lang=scss&scoped=true", "dependencies": [{"path": "E:\\java系统\\公司财务系统\\src\\main\\resources\\admin\\admin\\src\\components\\index\\TagsView\\index.vue", "mtime": 1754631104896}, {"path": "E:\\java系统\\公司财务系统\\src\\main\\resources\\admin\\admin\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1754805255591}, {"path": "E:\\java系统\\公司财务系统\\src\\main\\resources\\admin\\admin\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1754628163326}, {"path": "E:\\java系统\\公司财务系统\\src\\main\\resources\\admin\\admin\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1754628158457}, {"path": "E:\\java系统\\公司财务系统\\src\\main\\resources\\admin\\admin\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1754628153247}, {"path": "E:\\java系统\\公司财务系统\\src\\main\\resources\\admin\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754805254356}, {"path": "E:\\java系统\\公司财务系统\\src\\main\\resources\\admin\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754628160828}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ci50YWdzLXZpZXctY29udGFpbmVyIHsKCWhlaWdodDogMzRweDsKCXdpZHRoOiAxMDAlOwoJYmFja2dyb3VuZDogI2ZmZjsKCWJvcmRlci1ib3R0b206IDFweCBzb2xpZCAjZDhkY2U1OwoJYm94LXNoYWRvdzogMCAxcHggM3B4IDAgcmdiYSgwLCAwLCAwLCAuMTIpLCAwIDAgM3B4IDAgcmdiYSgwLCAwLCAwLCAuMDQpOwoKCS5jb250ZXh0bWVudSB7CgkJbWFyZ2luOiAwOwoJCWJhY2tncm91bmQ6ICNmZmY7CgkJei1pbmRleDogMzAwMDsKCQlwb3NpdGlvbjogYWJzb2x1dGU7CgkJbGlzdC1zdHlsZS10eXBlOiBub25lOwoJCXBhZGRpbmc6IDVweCAwOwoJCWJvcmRlci1yYWRpdXM6IDRweDsKCQlmb250LXNpemU6IDEycHg7CgkJZm9udC13ZWlnaHQ6IDQwMDsKCQljb2xvcjogIzMzMzsKCQlib3gtc2hhZG93OiAycHggMnB4IDNweCAwIHJnYmEoMCwgMCwgMCwgLjMpOwoKCQlsaSB7CgkJCW1hcmdpbjogMDsKCQkJcGFkZGluZzogN3B4IDE2cHg7CgkJCWN1cnNvcjogcG9pbnRlcjsKCgkJCSY6aG92ZXIgewoJCQkJYmFja2dyb3VuZDogI2VlZTsKCQkJfQoJCX0KCX0KfQoKLnRhZ3Mtdmlldy1jb250YWluZXIgLnRhZ3Mtdmlldy13cmFwcGVyIC50YWdzLXZpZXctaXRlbSB7CgkJCWN1cnNvcjogcG9pbnRlcjsKCQkJcGFkZGluZzogMCA4cHg7CgkJCW1hcmdpbjogMCA1cHggMCAwOwoJCQljb2xvcjogI2EzYjFjOTsKCQkJZGlzcGxheTogaW5saW5lLWJsb2NrOwoJCQlmb250LXNpemU6IDEycHg7CgkJCWJvcmRlci1jb2xvcjogI2Q4ZGNlNTsKCQkJbGluZS1oZWlnaHQ6IDI1cHg7CgkJCWJhY2tncm91bmQ6ICNmZmY7CgkJCXdpZHRoOiBhdXRvOwoJCQlib3JkZXItd2lkdGg6IDFweCAxcHggMCAxcHg7CgkJCWJvcmRlci1zdHlsZTogc29saWQ7CgkJCWhlaWdodDogMjVweDsKCQl9CgoudGFncy12aWV3LWNvbnRhaW5lciAudGFncy12aWV3LXdyYXBwZXIgLnRhZ3Mtdmlldy1pdGVtOmhvdmVyIHsKCQkJY29sb3I6ICMyZGRjZDM7CgkJfQoKLnRhZ3Mtdmlldy1jb250YWluZXIgLnRhZ3Mtdmlldy13cmFwcGVyIC50YWdzLXZpZXctaXRlbS5hY3RpdmUgewoJCQljb2xvcjogIzJkZGNkMzsKCQl9CgoudGFncy12aWV3LWNvbnRhaW5lciAudGFncy12aWV3LXdyYXBwZXIgLnRhZ3Mtdmlldy1pdGVtIC50ZXh0IHsKCQkJY29sb3I6IGluaGVyaXQ7CgkJCWZvbnQtc2l6ZTogaW5oZXJpdDsKCQl9CgoudGFncy12aWV3LWNvbnRhaW5lciAudGFncy12aWV3LXdyYXBwZXIgLnRhZ3Mtdmlldy1pdGVtIC5lbC1pY29uLWNsb3NlIHsKCQkJY29sb3I6IGluaGVyaXQ7CgkJCWZvbnQtc2l6ZTogaW5oZXJpdDsKCQl9Cg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";AA2TA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/components/index/TagsView", "sourcesContent": ["<template>\r\n\t<div id=\"tags-view-container\" class=\"tags-view-container\" :style='{\"padding\":\"4px 30px\",\"margin\":\"0px 0 0px\",\"borderColor\":\"#d8dce5\",\"background\":\"none\",\"borderWidth\":\"0 0 1px\",\"width\":\"100%\",\"borderStyle\":\"solid\",\"height\":\"34px\"}'>\r\n\t\t<scroll-pane ref=\"scrollPane\" class=\"tags-view-wrapper\">\r\n\t\t\t<div class=\"tags-view-box\" :style='{\"width\":\"100%\",\"whiteSpace\":\"nowrap\",\"position\":\"relative\",\"background\":\"none\"}'>\r\n\t\t\t\t<router-link v-for=\"tag in visitedViews\" ref=\"tag\" :key=\"tag.path\" :class=\"isActive(tag)?'active':''\"\r\n\t\t\t\t\t:to=\"{ path: tag.path, query: tag.query, fullPath: tag.fullPath }\" tag=\"span\" class=\"tags-view-item\"\r\n\t\t\t\t\*********************=\"closeSelectedTag(tag)\" @contextmenu.prevent.native=\"openMenu(tag,$event)\">\r\n\t\t\t\t\t<span class=\"text\">{{ tag.name }}</span>\r\n\t\t\t\t\t<span v-if=\"!tag.meta.affix\" class=\"el-icon-close\" @click.prevent.stop=\"closeSelectedTag(tag)\" />\r\n\t\t\t\t</router-link>\r\n\t\t\t</div>\r\n\t\t</scroll-pane>\r\n\t\t<ul v-show=\"visible\" :style=\"{left:left+'px',top:top+'px'}\" class=\"contextmenu\">\r\n\t\t\t<li v-if=\"!(selectedTag.meta&&selectedTag.meta.affix)\" @click=\"closeSelectedTag(selectedTag)\">Close</li>\r\n\t\t\t<li @click=\"closeAllTags(selectedTag)\">Close All</li>\r\n\t\t</ul>\r\n\t</div>\r\n</template>\r\n\r\n<script>\r\n\timport ScrollPane from './ScrollPane'\r\n\timport path from 'path'\r\n\timport {\r\n\t\tgenerateTitle\r\n\t} from '@/utils/i18n'\r\n\timport menu from '@/utils/menu'\r\n\timport { routes } from '@/router/router-static.js'\r\n\t\r\n\texport default {\r\n\t\tcomponents: {\r\n\t\t\tScrollPane\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tvisible: false,\r\n\t\t\t\ttop: 0,\r\n\t\t\t\tleft: 0,\r\n\t\t\t\tselectedTag: {},\r\n\t\t\t\taffixTags: [],\r\n\t\t\t\troutes: []\r\n\t\t\t}\r\n\t\t},\r\n\t\tcomputed: {\r\n\t\t\tvisitedViews() {\r\n\t\t\t\treturn this.$store.state.tagsView.visitedViews\r\n\t\t\t},\r\n\t\t\t// routes() {\r\n\t\t\t//   return this.$store.state.menu.routes\r\n\t\t\t// }\r\n\t\t},\r\n\t\twatch: {\r\n\t\t\t$route() {\r\n\t\t\t\tthis.addTags()\r\n\t\t\t\tthis.moveToCurrentTag()\r\n\t\t\t},\r\n\t\t\tvisible(value) {\r\n\t\t\t\tif (value) {\r\n\t\t\t\t\tdocument.body.addEventListener('click', this.closeMenu)\r\n\t\t\t\t} else {\r\n\t\t\t\t\tdocument.body.removeEventListener('click', this.closeMenu)\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\t\tmounted() {\r\n\t\t\tthis.initTags()\r\n\t\t\tthis.addTags()\r\n\t\t},\r\n\t\tcreated() {\r\n\t\t\tthis.routes = menu\r\n\t\t\t\r\n\t\t\tlet menuList = []\r\n\t\t\tconst menus = menu.list()\r\n\t\t\tif (menus) {\r\n\t\t\t\tmenuList = menus\r\n\t\t\t} else {\r\n\t\t\t\tlet params = {\r\n\t\t\t\t\tpage: 1,\r\n\t\t\t\t\tlimit: 1,\r\n\t\t\t\t\tsort: 'id',\r\n\t\t\t\t}\r\n\t\t\t\r\n\t\t\t\tthis.$http({\r\n\t\t\t\t\turl: \"menu/list\",\r\n\t\t\t\t\tmethod: \"get\",\r\n\t\t\t\t\tparams: params\r\n\t\t\t\t}).then(({\r\n\t\t\t\t\tdata\r\n\t\t\t\t}) => {\r\n\t\t\t\t\tif (data && data.code === 0) {\r\n\t\t\t\t\t\tmenuList = JSON.parse(data.data.list[0].menujson);\r\n\t\t\t\t\t\tthis.$storage.set(\"menus\", menuList);\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t\tthis.role = this.$storage.get('role')\r\n\t\t\t\r\n\t\t\tfor (let i = 0; i < menuList.length; i++) {\r\n\t\t\t\tif (menuList[i].roleName == this.role) {\r\n\t\t\t\t\tthis.routes = menuList[i].backMenu;\r\n\t\t\t\t\tbreak;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\tthis.routes = routes.concat(this.routes);\r\n\t\t\t// console.log(this.visitedViews)\r\n\t\t\t// console.log(this.routes)\r\n\t\t\t// this.initTags()\r\n\t\t\t// this.addTags()\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tisActive(route) {\r\n\t\t\t\treturn route.path === this.$route.path\r\n\t\t\t},\r\n\t\t\tfilterAffixTags(routes, basePath = '/') {\r\n\t\t\t\tlet tags = []\r\n\t\t\t\troutes.forEach(route => {\r\n\t\t\t\t\tif (route.meta && route.meta.affix) {\r\n\t\t\t\t\t\tconst tagPath = path.resolve(basePath, route.path)\r\n\t\t\t\t\t\ttags.push({\r\n\t\t\t\t\t\t\tfullPath: tagPath,\r\n\t\t\t\t\t\t\tpath: tagPath,\r\n\t\t\t\t\t\t\tname: route.name,\r\n\t\t\t\t\t\t\tmeta: {\r\n\t\t\t\t\t\t\t\t...route.meta\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif (route.children) {\r\n\t\t\t\t\t\tconst tempTags = this.filterAffixTags(route.children, route.path)\r\n\t\t\t\t\t\tif (tempTags.length >= 1) {\r\n\t\t\t\t\t\t\ttags = [...tags, ...tempTags]\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t\treturn tags\r\n\t\t\t},\r\n\t\t\tgenerateTitle,\r\n\t\t\tinitTags() {\r\n\t\t\t\tconst affixTags = this.affixTags = this.filterAffixTags(this.routes)\r\n\t\t\t\tfor (const tag of affixTags) {\r\n\t\t\t\t\t// Must have tag name\r\n\t\t\t\t\tif (tag.name) {\r\n\t\t\t\t\t\tthis.$store.dispatch('tagsView/addVisitedView', tag)\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\taddTags() {\r\n\t\t\t\tconst {\r\n\t\t\t\t\tname\r\n\t\t\t\t} = this.$route\r\n\t\t\t\tif (name) {\r\n\t\t\t\t\tthis.$store.dispatch('tagsView/addView', this.$route)\r\n\t\t\t\t}\r\n\t\t\t\treturn false\r\n\t\t\t},\r\n\t\t\tmoveToCurrentTag() {\r\n\t\t\t\tconst tags = this.$refs.tag\r\n\t\t\t\tthis.$nextTick(() => {\r\n\t\t\t\t\tfor (const tag of tags) {\r\n\t\t\t\t\t\tif (tag.to.path === this.$route.path) {\r\n\t\t\t\t\t\t\tthis.$refs.scrollPane.moveToTarget(tag)\r\n\t\t\t\t\t\t\t// when query is different then update\r\n\t\t\t\t\t\t\tif (tag.to.fullPath !== this.$route.fullPath) {\r\n\t\t\t\t\t\t\t\tthis.$store.dispatch('tagsView/updateVisitedView', this.$route)\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\tbreak\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\trefreshSelectedTag(view) {\r\n\t\t\t\tthis.$store.dispatch('tagsView/delCachedView', view).then(() => {\r\n\t\t\t\t\tconst {\r\n\t\t\t\t\t\tfullPath\r\n\t\t\t\t\t} = view\r\n\t\t\t\t\tthis.$nextTick(() => {\r\n\t\t\t\t\t\tthis.$router.replace({\r\n\t\t\t\t\t\t\tpath: '/redirect' + fullPath\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t})\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tcloseSelectedTag(view) {\r\n\t\t\t\tthis.$store.dispatch('tagsView/delView', view).then(({\r\n\t\t\t\t\tvisitedViews\r\n\t\t\t\t}) => {\r\n\t\t\t\t\tif (this.isActive(view)) {\r\n\t\t\t\t\t\tthis.toLastView(visitedViews, view)\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tcloseOthersTags() {\r\n\t\t\t\tthis.$router.push(this.selectedTag)\r\n\t\t\t\tthis.$store.dispatch('tagsView/delOthersViews', this.selectedTag).then(() => {\r\n\t\t\t\t\tthis.moveToCurrentTag()\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tcloseAllTags(view) {\r\n\t\t\t\tthis.$store.dispatch('tagsView/delAllViews').then(({\r\n\t\t\t\t\tvisitedViews\r\n\t\t\t\t}) => {\r\n\t\t\t\t\tif (this.affixTags.some(tag => tag.path === view.path)) {\r\n\t\t\t\t\t\treturn\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthis.toLastView(visitedViews, view)\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\ttoLastView(visitedViews, view) {\r\n\t\t\t\tconst latestView = visitedViews.slice(-1)[0]\r\n\t\t\t\tif (latestView) {\r\n\t\t\t\t\tthis.$router.push(latestView)\r\n\t\t\t\t} else {\r\n\t\t\t\t\t// now the default is to redirect to the home page if there is no tags-view,\r\n\t\t\t\t\t// you can adjust it according to your needs.\r\n\t\t\t\t\tif (view.name === 'Dashboard') {\r\n\t\t\t\t\t\t// to reload home page\r\n\t\t\t\t\t\tthis.$router.replace({\r\n\t\t\t\t\t\t\tpath: '/redirect' + view.fullPath\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tthis.$router.push('/')\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\topenMenu(tag, e) {\r\n\t\t\t\tconst menuMinWidth = 105\r\n\t\t\t\tconst offsetLeft = this.$el.getBoundingClientRect().left // container margin left\r\n\t\t\t\tconst offsetWidth = this.$el.offsetWidth // container width\r\n\t\t\t\tconst maxLeft = offsetWidth - menuMinWidth // left boundary\r\n\t\t\t\tconst left = e.clientX - offsetLeft + 15 // 15: margin right\r\n\r\n\t\t\t\tif (left > maxLeft) {\r\n\t\t\t\t\tthis.left = maxLeft\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.left = left\r\n\t\t\t\t}\r\n\r\n\t\t\t\tthis.top = e.clientY\r\n\t\t\t\tthis.visible = true\r\n\t\t\t\tthis.selectedTag = tag\r\n\t\t\t},\r\n\t\t\tcloseMenu() {\r\n\t\t\t\tthis.visible = false\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n\t.tags-view-container {\r\n\t\theight: 34px;\r\n\t\twidth: 100%;\r\n\t\tbackground: #fff;\r\n\t\tborder-bottom: 1px solid #d8dce5;\r\n\t\tbox-shadow: 0 1px 3px 0 rgba(0, 0, 0, .12), 0 0 3px 0 rgba(0, 0, 0, .04);\r\n\r\n\t\t.contextmenu {\r\n\t\t\tmargin: 0;\r\n\t\t\tbackground: #fff;\r\n\t\t\tz-index: 3000;\r\n\t\t\tposition: absolute;\r\n\t\t\tlist-style-type: none;\r\n\t\t\tpadding: 5px 0;\r\n\t\t\tborder-radius: 4px;\r\n\t\t\tfont-size: 12px;\r\n\t\t\tfont-weight: 400;\r\n\t\t\tcolor: #333;\r\n\t\t\tbox-shadow: 2px 2px 3px 0 rgba(0, 0, 0, .3);\r\n\r\n\t\t\tli {\r\n\t\t\t\tmargin: 0;\r\n\t\t\t\tpadding: 7px 16px;\r\n\t\t\t\tcursor: pointer;\r\n\r\n\t\t\t\t&:hover {\r\n\t\t\t\t\tbackground: #eee;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t\r\n\t.tags-view-container .tags-view-wrapper .tags-view-item {\r\n\t\t\t\tcursor: pointer;\r\n\t\t\t\tpadding: 0 8px;\r\n\t\t\t\tmargin: 0 5px 0 0;\r\n\t\t\t\tcolor: #a3b1c9;\r\n\t\t\t\tdisplay: inline-block;\r\n\t\t\t\tfont-size: 12px;\r\n\t\t\t\tborder-color: #d8dce5;\r\n\t\t\t\tline-height: 25px;\r\n\t\t\t\tbackground: #fff;\r\n\t\t\t\twidth: auto;\r\n\t\t\t\tborder-width: 1px 1px 0 1px;\r\n\t\t\t\tborder-style: solid;\r\n\t\t\t\theight: 25px;\r\n\t\t\t}\r\n\t\r\n\t.tags-view-container .tags-view-wrapper .tags-view-item:hover {\r\n\t\t\t\tcolor: #2ddcd3;\r\n\t\t\t}\r\n\t\r\n\t.tags-view-container .tags-view-wrapper .tags-view-item.active {\r\n\t\t\t\tcolor: #2ddcd3;\r\n\t\t\t}\r\n\t\r\n\t.tags-view-container .tags-view-wrapper .tags-view-item .text {\r\n\t\t\t\tcolor: inherit;\r\n\t\t\t\tfont-size: inherit;\r\n\t\t\t}\r\n\t\r\n\t.tags-view-container .tags-view-wrapper .tags-view-item .el-icon-close {\r\n\t\t\t\tcolor: inherit;\r\n\t\t\t\tfont-size: inherit;\r\n\t\t\t}\r\n</style>\r\n"]}]}