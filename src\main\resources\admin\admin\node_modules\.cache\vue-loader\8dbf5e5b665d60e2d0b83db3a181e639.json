{"remainingRequest": "E:\\java系统\\公司财务系统\\src\\main\\resources\\admin\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\java系统\\公司财务系统\\src\\main\\resources\\admin\\admin\\src\\components\\index\\IndexHeader.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\java系统\\公司财务系统\\src\\main\\resources\\admin\\admin\\src\\components\\index\\IndexHeader.vue", "mtime": 1754631104825}, {"path": "E:\\java系统\\公司财务系统\\src\\main\\resources\\admin\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754805254356}, {"path": "E:\\java系统\\公司财务系统\\src\\main\\resources\\admin\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754805257322}, {"path": "E:\\java系统\\公司财务系统\\src\\main\\resources\\admin\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754805254356}, {"path": "E:\\java系统\\公司财务系统\\src\\main\\resources\\admin\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754628160828}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["IndexHeader.vue"], "names": [], "mappings": ";AAgCA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "IndexHeader.vue", "sourceRoot": "src/components/index", "sourcesContent": ["<template>\r\n\t<div class=\"navbar\">\r\n\t\t<div class=\"title\" :style='{\"display\":\"none\"}'>\r\n\t\t\t<el-image v-if=\"true\" class=\"title-img\" :style='{\"width\":\"44px\",\"objectFit\":\"cover\",\"borderRadius\":\"100%\",\"float\":\"left\",\"height\":\"44px\"}' src=\"http://codegen.caihongy.cn/20201114/7856ba26477849ea828f481fa2773a95.jpg\" fit=\"cover\" />\r\n\t\t\t<span class=\"title-name\" :style='{\"padding\":\"0 0 0 12px\",\"lineHeight\":\"44px\",\"color\":\"#fff\",\"float\":\"left\"}'>{{this.$project.projectName}}</span>\r\n\t\t</div>\r\n\t\t<!--\r\n\t\t<div class=\"right\" :style='{\"position\":\"absolute\",\"right\":\"20px\",\"top\":\"8px\",\"display\":\"flex\"}'>\r\n\t\t\t<div :style='{\"cursor\":\"pointer\",\"margin\":\"0 5px\",\"lineHeight\":\"44px\",\"color\":\"#333\"}' class=\"nickname\">{{this.$storage.get('role')}} {{this.$storage.get('adminName')}}</div>\r\n\t\t\t<div :style='{\"cursor\":\"pointer\",\"margin\":\"0 5px\",\"lineHeight\":\"44px\",\"color\":\"#666\"}' v-if=\"this.$storage.get('role')=='管理员'\" class=\"backUp\" @click=\"backUp\">数据备份</div>\r\n\t\t\t<div :style='{\"cursor\":\"pointer\",\"margin\":\"0 5px\",\"lineHeight\":\"44px\",\"color\":\"#666\"}' class=\"logout\" @click=\"onLogout\">退出登录</div>\r\n\t\t</div>\r\n\t\t-->\r\n\t\t\r\n\t\t<el-dropdown @command=\"handleCommand\" trigger=\"click\" :style='{\"fontSize\":\"14px\",\"position\":\"absolute\",\"right\":\"20px\",\"color\":\"#666\",\"display\":\"flex\"}'>\r\n\t\t  <div class=\"el-dropdown-link\" :style='{\"alignItems\":\"center\",\"display\":\"flex\"}'>\r\n\t\t    <el-image v-if=\"user\" :style='{\"width\":\"32px\",\"margin\":\"0 10px\",\"objectFit\":\"cover\",\"borderRadius\":\"100%\",\"display\":\"none\",\"height\":\"32px\"}' :src=\"avatar?this.$base.url + avatar : require('@/assets/img/avator.png')\" fit=\"cover\"></el-image>\r\n\t\t    <span :style='{\"color\":\"#fff\",\"lineHeight\":\"32px\",\"fontSize\":\"14px\"}'>{{this.$storage.get('adminName')}}</span>\r\n\t\t    <span class=\"icon iconfont icon-xiala\" :style='{\"color\":\"#fff\",\"margin\":\"0 0 0 5px\",\"fontSize\":\"14px\"}'></span>\r\n\t\t  </div>\r\n\t\t  <el-dropdown-menu class=\"top-el-dropdown-menu\" slot=\"dropdown\">\r\n\t\t    <el-dropdown-item class=\"item1\" :command=\"''\">首页</el-dropdown-item>\r\n\t\t    <el-dropdown-item class=\"item2\" :command=\"'center'\">个人中心</el-dropdown-item>\r\n\t\t    <el-dropdown-item v-if=\"this.$storage.get('role')=='管理员'\" class=\"item3\" :command=\"'backUp'\">数据备份</el-dropdown-item>\r\n\t\t    <el-dropdown-item class=\"item4\" :command=\"'logout'\">退出登录</el-dropdown-item>\r\n\t\t  </el-dropdown-menu>\r\n\t\t</el-dropdown>\r\n\t\t\r\n\t</div>\r\n</template>\r\n\r\n<script>\r\n\timport axios from 'axios'\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tdialogVisible: false,\r\n\t\t\t\truleForm: {},\r\n\t\t\t\tuser: null,\r\n\t\t\t};\r\n\t\t},\r\n\t\tcreated() {\r\n\t\t},\r\n\t\tcomputed: {\r\n\t\t\tavatar(){\r\n\t\t\t\treturn this.$storage.get('headportrait')?this.$storage.get('headportrait'):''\r\n\t\t\t}\r\n\t\t},\r\n\t\tmounted() {\r\n\t\t\tlet sessionTable = this.$storage.get(\"sessionTable\")\r\n\t\t\tthis.$http({\r\n\t\t\t\turl: sessionTable + '/session',\r\n\t\t\t\tmethod: \"get\"\r\n\t\t\t}).then(({\r\n\t\t\t\tdata\r\n\t\t\t}) => {\r\n\t\t\t\tif (data && data.code === 0) {\r\n\t\t\t\t\tif(sessionTable == 'yuangong') {\r\n\t\t\t\t\t\tthis.$storage.set('headportrait',data.data.touxiang)\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif(sessionTable == 'users') {\r\n\t\t\t\t\t\tthis.$storage.set('headportrait',data.data.image)\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthis.$storage.set('userForm',JSON.stringify(data.data))\r\n\t\t\t\t\tthis.user = data.data;\r\n\t\t\t\t\tthis.$storage.set('userid',data.data.id);\r\n\t\t\t\t} else {\r\n\t\t\t\t\tlet message = this.$message\r\n\t\t\t\t\tmessage.error(data.msg);\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\thandleCommand(name) {\r\n\t\t\t\tif (name == 'front') {\r\n\t\t\t\t\tthis.onIndexTap()\r\n\t\t\t\t} else if (name == 'logout') {\r\n\t\t\t\t\tthis.onLogout()\r\n\t\t\t\t} else if (name == 'board'){\r\n\t\t\t\t\tthis.toBoard()\r\n\t\t\t\t} else if (name == 'backUp'){\r\n\t\t\t\t\tthis.backUp()\r\n\t\t\t\t} else {\r\n\t\t\t\t\tlet router = this.$router\r\n\t\t\t\t\tname = '/'+name\r\n\t\t\t\t\trouter.push(name)\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\tonLogout() {\r\n\t\t\t\tlet storage = this.$storage\r\n\t\t\t\tlet router = this.$router\r\n\t\t\t\tstorage.clear()\r\n\t\t\t\tthis.$store.dispatch('tagsView/delAllViews')\r\n\t\t\t\trouter.replace({\r\n\t\t\t\t\tname: \"login\"\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tonIndexTap(){\r\n\t\t\t\twindow.location.href = `${this.$base.indexUrl}`\r\n\t\t\t},\r\n            backUp() {\r\n                this.$confirm('是否备份数据？', '数据备份提示', {\r\n                    confirmButtonText: '确定',\r\n                    cancelButtonText: '取消',\r\n                    type: 'warning'\r\n                }).then(() => {\r\n                    this.$http({\r\n                        url: '/mysqldump',\r\n                        method: \"get\"\r\n                    }).then(({\r\n                        data\r\n                    }) => {\r\n                        if (data) {\r\n                            const binaryData = [];\r\n                            binaryData.push(data);\r\n                            const objectUrl = window.URL.createObjectURL(new Blob(binaryData, {\r\n                                type: 'application/pdf;chartset=UTF-8'\r\n                            }))\r\n                            const a = document.createElement('a')\r\n                            a.href = objectUrl\r\n                            a.download = 'mysql.dmp'\r\n                            // a.click()\r\n                            // 下面这个写法兼容火狐\r\n                            a.dispatchEvent(new MouseEvent('click', {\r\n                                bubbles: true,\r\n                                cancelable: true,\r\n                                view: window\r\n                            }))\r\n                            window.URL.revokeObjectURL(data)\r\n                            message.message(\"数据备份成功\")\r\n                        } else {\r\n                            let message = this.$message\r\n                            message.error(data.msg);\r\n                        }\r\n                    });\r\n                });\r\n            },\r\n\t\t}\r\n\t};\r\n</script>\r\n\r\n\r\n<style lang=\"scss\" scoped>\r\n\t.top-el-dropdown-menu {\r\n\t\t\t\tborder: 1px solid #EBEEF5;\r\n\t\t\t\tborder-radius: 4px;\r\n\t\t\t\tpadding: 10px 0;\r\n\t\t\t\tbox-shadow: 0 2px 12px 0 rgba(0,0,0,.1);\r\n\t\t\t\tmargin: 18px 0;\r\n\t\t\t\tbackground: #FFF;\r\n\t\t\t}\r\n\t\r\n\t.top-el-dropdown-menu li.el-dropdown-menu__item.item1 {\r\n\t\t\t\tcursor: pointer;\r\n\t\t\t\tpadding: 0 20px;\r\n\t\t\t\tmargin: 0;\r\n\t\t\t\toutline: 0;\r\n\t\t\t\tcolor: #606266;\r\n\t\t\t\tbackground: #fff;\r\n\t\t\t\tfont-size: 14px;\r\n\t\t\t\tline-height: 36px;\r\n\t\t\t\tlist-style: none;\r\n\t\t\t}\r\n\t\r\n\t.top-el-dropdown-menu li.el-dropdown-menu__item.item1:hover {\r\n\t\t\t\tbackground: #ecf5ff;\r\n\t\t\t}\r\n\t\r\n\t.top-el-dropdown-menu li.el-dropdown-menu__item.item2 {\r\n\t\t\t\tcursor: pointer;\r\n\t\t\t\tpadding: 0 20px;\r\n\t\t\t\tmargin: 0;\r\n\t\t\t\toutline: 0;\r\n\t\t\t\tcolor: #606266;\r\n\t\t\t\tbackground: #fff;\r\n\t\t\t\tfont-size: 14px;\r\n\t\t\t\tline-height: 36px;\r\n\t\t\t\tlist-style: none;\r\n\t\t\t}\r\n\t\r\n\t.top-el-dropdown-menu li.el-dropdown-menu__item.item2:hover {\r\n\t\t\t\tbackground: #ecf5ff;\r\n\t\t\t}\r\n\t\r\n\t.top-el-dropdown-menu li.el-dropdown-menu__item.item3 {\r\n\t\t\t\tcursor: pointer;\r\n\t\t\t\tpadding: 0 20px;\r\n\t\t\t\tmargin: 0;\r\n\t\t\t\toutline: 0;\r\n\t\t\t\tcolor: #606266;\r\n\t\t\t\tbackground: #fff;\r\n\t\t\t\tfont-size: 14px;\r\n\t\t\t\tline-height: 36px;\r\n\t\t\t\tlist-style: none;\r\n\t\t\t}\r\n\t\r\n\t.top-el-dropdown-menu li.el-dropdown-menu__item.item3:hover {\r\n\t\t\t\tbackground: #ecf5ff;\r\n\t\t\t}\r\n\t\r\n\t.top-el-dropdown-menu li.el-dropdown-menu__item.item4 {\r\n\t\t\t\tcursor: pointer;\r\n\t\t\t\tpadding: 0 20px;\r\n\t\t\t\tmargin: 0;\r\n\t\t\t\toutline: 0;\r\n\t\t\t\tcolor: #606266;\r\n\t\t\t\tbackground: #fff;\r\n\t\t\t\tfont-size: 14px;\r\n\t\t\t\tline-height: 36px;\r\n\t\t\t\tlist-style: none;\r\n\t\t\t}\r\n\t\r\n\t.top-el-dropdown-menu li.el-dropdown-menu__item.item4:hover {\r\n\t\t\t\tbackground: #ecf5ff;\r\n\t\t\t}\r\n</style>\r\n"]}]}