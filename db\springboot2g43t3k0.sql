-- MySQL dump 10.13  Distrib 5.7.31, for Linux (x86_64)
--
-- Host: localhost    Database: springboot2g43t3k0
-- ------------------------------------------------------
-- Server version	5.7.31

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Current Database: `springboot2g43t3k0`
--

/*!40000 DROP DATABASE IF EXISTS `springboot2g43t3k0`*/;

CREATE DATABASE /*!32312 IF NOT EXISTS*/ `springboot2g43t3k0` /*!40100 DEFAULT CHARACTER SET utf8mb4 */;

USE `springboot2g43t3k0`;

--
-- Table structure for table `address`
--

DROP TABLE IF EXISTS `address`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `address` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `addtime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `userid` bigint(20) NOT NULL COMMENT '用户id',
  `address` varchar(200) NOT NULL COMMENT '地址',
  `name` varchar(200) NOT NULL COMMENT '收货人',
  `phone` varchar(200) NOT NULL COMMENT '电话',
  `isdefault` varchar(200) NOT NULL COMMENT '是否默认地址[是/否]',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8 COMMENT='地址';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `address`
--

LOCK TABLES `address` WRITE;
/*!40000 ALTER TABLE `address` DISABLE KEYS */;
INSERT INTO `address` VALUES (1,'2024-05-13 14:31:19',11,'宇宙银河系金星1号','金某','13823888881','是'),(2,'2024-05-13 14:31:19',12,'宇宙银河系木星1号','木某','13823888882','是'),(3,'2024-05-13 14:31:19',13,'宇宙银河系水星1号','水某','13823888883','是'),(4,'2024-05-13 14:31:19',14,'宇宙银河系火星1号','火某','13823888884','是'),(5,'2024-05-13 14:31:19',15,'宇宙银河系土星1号','土某','13823888885','是'),(6,'2024-05-13 14:31:19',16,'宇宙银河系月球1号','月某','13823888886','是'),(7,'2024-05-13 14:31:19',17,'宇宙银河系黑洞1号','黑某','13823888887','是'),(8,'2024-05-13 14:31:19',18,'宇宙银河系地球1号','地某','13823888888','是');
/*!40000 ALTER TABLE `address` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `bumen`
--

DROP TABLE IF EXISTS `bumen`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `bumen` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `addtime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `bumen` varchar(200) NOT NULL COMMENT '部门',
  PRIMARY KEY (`id`),
  UNIQUE KEY `bumen` (`bumen`)
) ENGINE=InnoDB AUTO_INCREMENT=29 DEFAULT CHARSET=utf8 COMMENT='部门';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `bumen`
--

LOCK TABLES `bumen` WRITE;
/*!40000 ALTER TABLE `bumen` DISABLE KEYS */;
INSERT INTO `bumen` VALUES (21,'2024-05-13 14:31:19','部门1'),(22,'2024-05-13 14:31:19','部门2'),(23,'2024-05-13 14:31:19','部门3'),(24,'2024-05-13 14:31:19','部门4'),(25,'2024-05-13 14:31:19','部门5'),(26,'2024-05-13 14:31:19','部门6'),(27,'2024-05-13 14:31:19','部门7'),(28,'2024-05-13 14:31:19','部门8');
/*!40000 ALTER TABLE `bumen` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `caiwuxinxi`
--

DROP TABLE IF EXISTS `caiwuxinxi`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `caiwuxinxi` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `addtime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `tongjibianhao` varchar(200) DEFAULT NULL COMMENT '统计编号',
  `yuefen` varchar(200) DEFAULT NULL COMMENT '月份',
  `shourujine` double NOT NULL COMMENT '收入金额',
  `zhichujine` double NOT NULL COMMENT '支出金额',
  `lirun` double DEFAULT NULL COMMENT '利润',
  `dengjiriqi` date DEFAULT NULL COMMENT '登记日期',
  `shoururiqi` date DEFAULT NULL COMMENT '收入日期',
  `shourulaiyuan` longtext COMMENT '收入来源',
  `zhichushijian` date DEFAULT NULL COMMENT '支出时间',
  `zhichuyuanyin` longtext COMMENT '支出原因',
  PRIMARY KEY (`id`),
  UNIQUE KEY `tongjibianhao` (`tongjibianhao`)
) ENGINE=InnoDB AUTO_INCREMENT=149 DEFAULT CHARSET=utf8 COMMENT='财务信息';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `caiwuxinxi`
--

LOCK TABLES `caiwuxinxi` WRITE;
/*!40000 ALTER TABLE `caiwuxinxi` DISABLE KEYS */;
INSERT INTO `caiwuxinxi` VALUES (141,'2024-05-13 14:31:19','1111111111','一月',1,1,1,'2024-05-13','2024-05-13','收入来源1','2024-05-13','支出原因1'),(142,'2024-05-13 14:31:19','2222222222','一月',2,2,2,'2024-05-13','2024-05-13','收入来源2','2024-05-13','支出原因2'),(143,'2024-05-13 14:31:19','3333333333','一月',3,3,3,'2024-05-13','2024-05-13','收入来源3','2024-05-13','支出原因3'),(144,'2024-05-13 14:31:19','4444444444','一月',4,4,4,'2024-05-13','2024-05-13','收入来源4','2024-05-13','支出原因4'),(145,'2024-05-13 14:31:19','5555555555','一月',5,5,5,'2024-05-13','2024-05-13','收入来源5','2024-05-13','支出原因5'),(146,'2024-05-13 14:31:19','6666666666','一月',6,6,6,'2024-05-13','2024-05-13','收入来源6','2024-05-13','支出原因6'),(147,'2024-05-13 14:31:19','7777777777','一月',7,7,7,'2024-05-13','2024-05-13','收入来源7','2024-05-13','支出原因7'),(148,'2024-05-13 14:31:19','8888888888','一月',8,8,8,'2024-05-13','2024-05-13','收入来源8','2024-05-13','支出原因8');
/*!40000 ALTER TABLE `caiwuxinxi` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `config`
--

DROP TABLE IF EXISTS `config`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `config` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `name` varchar(100) NOT NULL COMMENT '配置参数名称',
  `value` varchar(100) DEFAULT NULL COMMENT '配置参数值',
  `url` varchar(500) DEFAULT NULL COMMENT 'url',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8 COMMENT='配置文件';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `config`
--

LOCK TABLES `config` WRITE;
/*!40000 ALTER TABLE `config` DISABLE KEYS */;
INSERT INTO `config` VALUES (1,'picture1','upload/picture1.jpg',NULL),(2,'picture2','upload/picture2.jpg',NULL),(3,'picture3','upload/picture3.jpg',NULL);
/*!40000 ALTER TABLE `config` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `gonggaoxinxi`
--

DROP TABLE IF EXISTS `gonggaoxinxi`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `gonggaoxinxi` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `addtime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `biaoti` varchar(200) NOT NULL COMMENT '标题',
  `jianjie` longtext COMMENT '简介',
  `fengmian` longtext COMMENT '封面',
  `neirong` longtext COMMENT '内容',
  `faburen` varchar(200) NOT NULL COMMENT '发布人',
  `fabushijian` datetime DEFAULT NULL COMMENT '发布时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=49 DEFAULT CHARSET=utf8 COMMENT='公告信息';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `gonggaoxinxi`
--

LOCK TABLES `gonggaoxinxi` WRITE;
/*!40000 ALTER TABLE `gonggaoxinxi` DISABLE KEYS */;
INSERT INTO `gonggaoxinxi` VALUES (41,'2024-05-13 14:31:19','标题1','简介1','upload/gonggaoxinxi_fengmian1.jpg,upload/gonggaoxinxi_fengmian2.jpg,upload/gonggaoxinxi_fengmian3.jpg','内容1','发布人1','2024-05-13 22:31:19'),(42,'2024-05-13 14:31:19','标题2','简介2','upload/gonggaoxinxi_fengmian2.jpg,upload/gonggaoxinxi_fengmian3.jpg,upload/gonggaoxinxi_fengmian4.jpg','内容2','发布人2','2024-05-13 22:31:19'),(43,'2024-05-13 14:31:19','标题3','简介3','upload/gonggaoxinxi_fengmian3.jpg,upload/gonggaoxinxi_fengmian4.jpg,upload/gonggaoxinxi_fengmian5.jpg','内容3','发布人3','2024-05-13 22:31:19'),(44,'2024-05-13 14:31:19','标题4','简介4','upload/gonggaoxinxi_fengmian4.jpg,upload/gonggaoxinxi_fengmian5.jpg,upload/gonggaoxinxi_fengmian6.jpg','内容4','发布人4','2024-05-13 22:31:19'),(45,'2024-05-13 14:31:19','标题5','简介5','upload/gonggaoxinxi_fengmian5.jpg,upload/gonggaoxinxi_fengmian6.jpg,upload/gonggaoxinxi_fengmian7.jpg','内容5','发布人5','2024-05-13 22:31:19'),(46,'2024-05-13 14:31:19','标题6','简介6','upload/gonggaoxinxi_fengmian6.jpg,upload/gonggaoxinxi_fengmian7.jpg,upload/gonggaoxinxi_fengmian8.jpg','内容6','发布人6','2024-05-13 22:31:19'),(47,'2024-05-13 14:31:19','标题7','简介7','upload/gonggaoxinxi_fengmian7.jpg,upload/gonggaoxinxi_fengmian8.jpg,upload/gonggaoxinxi_fengmian9.jpg','内容7','发布人7','2024-05-13 22:31:19'),(48,'2024-05-13 14:31:19','标题8','简介8','upload/gonggaoxinxi_fengmian8.jpg,upload/gonggaoxinxi_fengmian9.jpg,upload/gonggaoxinxi_fengmian10.jpg','内容8','发布人8','2024-05-13 22:31:19');
/*!40000 ALTER TABLE `gonggaoxinxi` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `gudingzichan`
--

DROP TABLE IF EXISTS `gudingzichan`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `gudingzichan` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `addtime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `zichanbianma` varchar(200) DEFAULT NULL COMMENT '资产编码',
  `zichanmingcheng` varchar(200) NOT NULL COMMENT '资产名称',
  `zichanleixing` varchar(200) DEFAULT NULL COMMENT '资产类型',
  `zichandanjia` double NOT NULL COMMENT '资产单价',
  `zichantupian` longtext NOT NULL COMMENT '资产图片',
  `zichanshuliang` int(11) NOT NULL COMMENT '资产数量',
  `shiyongzhuangkuang` varchar(200) NOT NULL COMMENT '使用状况',
  `zichanxiangqing` longtext COMMENT '资产详情',
  `weihuxiangqing` longtext COMMENT '维护详情',
  `shiyongmiaoshu` longtext COMMENT '使用描述',
  `jilushijian` date DEFAULT NULL COMMENT '记录时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `zichanbianma` (`zichanbianma`)
) ENGINE=InnoDB AUTO_INCREMENT=109 DEFAULT CHARSET=utf8 COMMENT='固定资产';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `gudingzichan`
--

LOCK TABLES `gudingzichan` WRITE;
/*!40000 ALTER TABLE `gudingzichan` DISABLE KEYS */;
INSERT INTO `gudingzichan` VALUES (101,'2024-05-13 14:31:19','1111111111','资产名称1','资产类型1',1,'upload/gudingzichan_zichantupian1.jpg,upload/gudingzichan_zichantupian2.jpg,upload/gudingzichan_zichantupian3.jpg',1,'使用状况1','资产详情1','维护详情1','使用描述1','2024-05-13'),(102,'2024-05-13 14:31:19','2222222222','资产名称2','资产类型2',2,'upload/gudingzichan_zichantupian2.jpg,upload/gudingzichan_zichantupian3.jpg,upload/gudingzichan_zichantupian4.jpg',2,'使用状况2','资产详情2','维护详情2','使用描述2','2024-05-13'),(103,'2024-05-13 14:31:19','3333333333','资产名称3','资产类型3',3,'upload/gudingzichan_zichantupian3.jpg,upload/gudingzichan_zichantupian4.jpg,upload/gudingzichan_zichantupian5.jpg',3,'使用状况3','资产详情3','维护详情3','使用描述3','2024-05-13'),(104,'2024-05-13 14:31:19','4444444444','资产名称4','资产类型4',4,'upload/gudingzichan_zichantupian4.jpg,upload/gudingzichan_zichantupian5.jpg,upload/gudingzichan_zichantupian6.jpg',4,'使用状况4','资产详情4','维护详情4','使用描述4','2024-05-13'),(105,'2024-05-13 14:31:19','5555555555','资产名称5','资产类型5',5,'upload/gudingzichan_zichantupian5.jpg,upload/gudingzichan_zichantupian6.jpg,upload/gudingzichan_zichantupian7.jpg',5,'使用状况5','资产详情5','维护详情5','使用描述5','2024-05-13'),(106,'2024-05-13 14:31:19','6666666666','资产名称6','资产类型6',6,'upload/gudingzichan_zichantupian6.jpg,upload/gudingzichan_zichantupian7.jpg,upload/gudingzichan_zichantupian8.jpg',6,'使用状况6','资产详情6','维护详情6','使用描述6','2024-05-13'),(107,'2024-05-13 14:31:19','7777777777','资产名称7','资产类型7',7,'upload/gudingzichan_zichantupian7.jpg,upload/gudingzichan_zichantupian8.jpg,upload/gudingzichan_zichantupian9.jpg',7,'使用状况7','资产详情7','维护详情7','使用描述7','2024-05-13'),(108,'2024-05-13 14:31:19','8888888888','资产名称8','资产类型8',8,'upload/gudingzichan_zichantupian8.jpg,upload/gudingzichan_zichantupian9.jpg,upload/gudingzichan_zichantupian10.jpg',8,'使用状况8','资产详情8','维护详情8','使用描述8','2024-05-13');
/*!40000 ALTER TABLE `gudingzichan` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `jiangchengxinxi`
--

DROP TABLE IF EXISTS `jiangchengxinxi`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `jiangchengxinxi` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `addtime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `gonghao` varchar(200) DEFAULT NULL COMMENT '工号',
  `xingming` varchar(200) DEFAULT NULL COMMENT '姓名',
  `bumen` varchar(200) DEFAULT NULL COMMENT '部门',
  `zhiwei` varchar(200) DEFAULT NULL COMMENT '职位',
  `jiangchengleixing` varchar(200) NOT NULL COMMENT '奖惩类型',
  `jiangchengjine` int(11) NOT NULL COMMENT '奖惩金额',
  `jiangchengyuanyin` longtext NOT NULL COMMENT '奖惩原因',
  `jiangchengriqi` date DEFAULT NULL COMMENT '奖惩日期',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=79 DEFAULT CHARSET=utf8 COMMENT='奖惩信息';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `jiangchengxinxi`
--

LOCK TABLES `jiangchengxinxi` WRITE;
/*!40000 ALTER TABLE `jiangchengxinxi` DISABLE KEYS */;
INSERT INTO `jiangchengxinxi` VALUES (71,'2024-05-13 14:31:19','工号1','姓名1','部门1','职位1','表扬',1,'奖惩原因1','2024-05-13'),(72,'2024-05-13 14:31:19','工号2','姓名2','部门2','职位2','表扬',2,'奖惩原因2','2024-05-13'),(73,'2024-05-13 14:31:19','工号3','姓名3','部门3','职位3','表扬',3,'奖惩原因3','2024-05-13'),(74,'2024-05-13 14:31:19','工号4','姓名4','部门4','职位4','表扬',4,'奖惩原因4','2024-05-13'),(75,'2024-05-13 14:31:19','工号5','姓名5','部门5','职位5','表扬',5,'奖惩原因5','2024-05-13'),(76,'2024-05-13 14:31:19','工号6','姓名6','部门6','职位6','表扬',6,'奖惩原因6','2024-05-13'),(77,'2024-05-13 14:31:19','工号7','姓名7','部门7','职位7','表扬',7,'奖惩原因7','2024-05-13'),(78,'2024-05-13 14:31:19','工号8','姓名8','部门8','职位8','表扬',8,'奖惩原因8','2024-05-13');
/*!40000 ALTER TABLE `jiangchengxinxi` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `kaoqinxinxi`
--

DROP TABLE IF EXISTS `kaoqinxinxi`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `kaoqinxinxi` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `addtime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `yuefen` varchar(200) DEFAULT NULL COMMENT '月份',
  `gonghao` varchar(200) DEFAULT NULL COMMENT '工号',
  `xingming` varchar(200) DEFAULT NULL COMMENT '姓名',
  `bumen` varchar(200) DEFAULT NULL COMMENT '部门',
  `zhiwei` varchar(200) DEFAULT NULL COMMENT '职位',
  `yingchuqintianshu` int(11) DEFAULT NULL COMMENT '应出勤天数',
  `qingjiatianshu` int(11) DEFAULT NULL COMMENT '请假天数',
  `chidaocishu` int(11) DEFAULT NULL COMMENT '迟到次数',
  `zaotuicishu` int(11) DEFAULT NULL COMMENT '早退次数',
  `chuchatianshu` int(11) DEFAULT NULL COMMENT '出差天数',
  `shidaotianshu` int(11) DEFAULT NULL COMMENT '实到天数',
  `dengjishijian` datetime DEFAULT NULL COMMENT '登记时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=89 DEFAULT CHARSET=utf8 COMMENT='考勤信息';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `kaoqinxinxi`
--

LOCK TABLES `kaoqinxinxi` WRITE;
/*!40000 ALTER TABLE `kaoqinxinxi` DISABLE KEYS */;
INSERT INTO `kaoqinxinxi` VALUES (81,'2024-05-13 14:31:19','一月','工号1','姓名1','部门1','职位1',1,1,1,1,1,1,'2024-05-13 22:31:19'),(82,'2024-05-13 14:31:19','一月','工号2','姓名2','部门2','职位2',2,2,2,2,2,2,'2024-05-13 22:31:19'),(83,'2024-05-13 14:31:19','一月','工号3','姓名3','部门3','职位3',3,3,3,3,3,3,'2024-05-13 22:31:19'),(84,'2024-05-13 14:31:19','一月','工号4','姓名4','部门4','职位4',4,4,4,4,4,4,'2024-05-13 22:31:19'),(85,'2024-05-13 14:31:19','一月','工号5','姓名5','部门5','职位5',5,5,5,5,5,5,'2024-05-13 22:31:19'),(86,'2024-05-13 14:31:19','一月','工号6','姓名6','部门6','职位6',6,6,6,6,6,6,'2024-05-13 22:31:19'),(87,'2024-05-13 14:31:19','一月','工号7','姓名7','部门7','职位7',7,7,7,7,7,7,'2024-05-13 22:31:19'),(88,'2024-05-13 14:31:19','一月','工号8','姓名8','部门8','职位8',8,8,8,8,8,8,'2024-05-13 22:31:19');
/*!40000 ALTER TABLE `kaoqinxinxi` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `qingjiaxinxi`
--

DROP TABLE IF EXISTS `qingjiaxinxi`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `qingjiaxinxi` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `addtime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `qingjiabianhao` varchar(200) DEFAULT NULL COMMENT '请假编号',
  `qingjiamingcheng` varchar(200) NOT NULL COMMENT '请假名称',
  `qingjialeixing` varchar(200) NOT NULL COMMENT '请假类型',
  `qingjiatianshu` int(11) NOT NULL COMMENT '请假天数',
  `qingjiayuanyin` longtext COMMENT '请假原因',
  `gonghao` varchar(200) DEFAULT NULL COMMENT '工号',
  `xingming` varchar(200) DEFAULT NULL COMMENT '姓名',
  `bumen` varchar(200) DEFAULT NULL COMMENT '部门',
  `zhiwei` varchar(200) DEFAULT NULL COMMENT '职位',
  `shenqingshijian` datetime DEFAULT NULL COMMENT '申请时间',
  `sfsh` varchar(200) DEFAULT '待审核' COMMENT '是否审核',
  `shhf` longtext COMMENT '审核回复',
  PRIMARY KEY (`id`),
  UNIQUE KEY `qingjiabianhao` (`qingjiabianhao`)
) ENGINE=InnoDB AUTO_INCREMENT=69 DEFAULT CHARSET=utf8 COMMENT='请假信息';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `qingjiaxinxi`
--

LOCK TABLES `qingjiaxinxi` WRITE;
/*!40000 ALTER TABLE `qingjiaxinxi` DISABLE KEYS */;
INSERT INTO `qingjiaxinxi` VALUES (61,'2024-05-13 14:31:19','1111111111','请假名称1','事假',1,'请假原因1','工号1','姓名1','部门1','职位1','2024-05-13 22:31:19','是',''),(62,'2024-05-13 14:31:19','2222222222','请假名称2','事假',2,'请假原因2','工号2','姓名2','部门2','职位2','2024-05-13 22:31:19','是',''),(63,'2024-05-13 14:31:19','3333333333','请假名称3','事假',3,'请假原因3','工号3','姓名3','部门3','职位3','2024-05-13 22:31:19','是',''),(64,'2024-05-13 14:31:19','4444444444','请假名称4','事假',4,'请假原因4','工号4','姓名4','部门4','职位4','2024-05-13 22:31:19','是',''),(65,'2024-05-13 14:31:19','5555555555','请假名称5','事假',5,'请假原因5','工号5','姓名5','部门5','职位5','2024-05-13 22:31:19','是',''),(66,'2024-05-13 14:31:19','6666666666','请假名称6','事假',6,'请假原因6','工号6','姓名6','部门6','职位6','2024-05-13 22:31:19','是',''),(67,'2024-05-13 14:31:19','7777777777','请假名称7','事假',7,'请假原因7','工号7','姓名7','部门7','职位7','2024-05-13 22:31:19','是',''),(68,'2024-05-13 14:31:19','8888888888','请假名称8','事假',8,'请假原因8','工号8','姓名8','部门8','职位8','2024-05-13 22:31:19','是','');
/*!40000 ALTER TABLE `qingjiaxinxi` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `token`
--

DROP TABLE IF EXISTS `token`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `token` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `userid` bigint(20) NOT NULL COMMENT '用户id',
  `username` varchar(100) NOT NULL COMMENT '用户名',
  `tablename` varchar(100) DEFAULT NULL COMMENT '表名',
  `role` varchar(100) DEFAULT NULL COMMENT '角色',
  `token` varchar(200) NOT NULL COMMENT '密码',
  `addtime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '新增时间',
  `expiratedtime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '过期时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='token表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `token`
--

LOCK TABLES `token` WRITE;
/*!40000 ALTER TABLE `token` DISABLE KEYS */;
/*!40000 ALTER TABLE `token` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `users`
--

DROP TABLE IF EXISTS `users`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `users` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `username` varchar(100) NOT NULL COMMENT '用户名',
  `password` varchar(100) NOT NULL COMMENT '密码',
  `image` varchar(200) DEFAULT NULL COMMENT '头像',
  `role` varchar(100) DEFAULT '管理员' COMMENT '角色',
  `addtime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '新增时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8 COMMENT='用户表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `users`
--

LOCK TABLES `users` WRITE;
/*!40000 ALTER TABLE `users` DISABLE KEYS */;
INSERT INTO `users` VALUES (1,'admin','admin','upload/image1.jpg','管理员','2024-05-13 14:31:19');
/*!40000 ALTER TABLE `users` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `yuangong`
--

DROP TABLE IF EXISTS `yuangong`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `yuangong` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `addtime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `gonghao` varchar(200) NOT NULL COMMENT '工号',
  `mima` varchar(200) NOT NULL COMMENT '密码',
  `xingming` varchar(200) NOT NULL COMMENT '姓名',
  `touxiang` longtext COMMENT '头像',
  `xingbie` varchar(200) DEFAULT NULL COMMENT '性别',
  `lianxidianhua` varchar(200) DEFAULT NULL COMMENT '联系电话',
  `bumen` varchar(200) DEFAULT NULL COMMENT '部门',
  `zhiwei` varchar(200) DEFAULT NULL COMMENT '职位',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=19 DEFAULT CHARSET=utf8 COMMENT='员工';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `yuangong`
--

LOCK TABLES `yuangong` WRITE;
/*!40000 ALTER TABLE `yuangong` DISABLE KEYS */;
INSERT INTO `yuangong` VALUES (11,'2024-05-13 14:31:19','工号1','123456','姓名1','upload/yuangong_touxiang1.jpg','男','13823888881','部门1','职位1'),(12,'2024-05-13 14:31:19','工号2','123456','姓名2','upload/yuangong_touxiang2.jpg','男','13823888882','部门2','职位2'),(13,'2024-05-13 14:31:19','工号3','123456','姓名3','upload/yuangong_touxiang3.jpg','男','13823888883','部门3','职位3'),(14,'2024-05-13 14:31:19','工号4','123456','姓名4','upload/yuangong_touxiang4.jpg','男','13823888884','部门4','职位4'),(15,'2024-05-13 14:31:19','工号5','123456','姓名5','upload/yuangong_touxiang5.jpg','男','13823888885','部门5','职位5'),(16,'2024-05-13 14:31:19','工号6','123456','姓名6','upload/yuangong_touxiang6.jpg','男','13823888886','部门6','职位6'),(17,'2024-05-13 14:31:19','工号7','123456','姓名7','upload/yuangong_touxiang7.jpg','男','13823888887','部门7','职位7'),(18,'2024-05-13 14:31:19','工号8','123456','姓名8','upload/yuangong_touxiang8.jpg','男','13823888888','部门8','职位8');
/*!40000 ALTER TABLE `yuangong` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `yuangongdangan`
--

DROP TABLE IF EXISTS `yuangongdangan`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `yuangongdangan` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `addtime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `gonghao` varchar(200) DEFAULT NULL COMMENT '工号',
  `xingming` varchar(200) DEFAULT NULL COMMENT '姓名',
  `xingbie` varchar(200) DEFAULT NULL COMMENT '性别',
  `lianxidianhua` varchar(200) DEFAULT NULL COMMENT '联系电话',
  `bumen` varchar(200) DEFAULT NULL COMMENT '部门',
  `zhiwei` varchar(200) DEFAULT NULL COMMENT '职位',
  `yuangongzhuangtai` varchar(200) NOT NULL COMMENT '员工状态',
  `yuangongdangan` longtext COMMENT '员工档案',
  `ruzhiriqi` date DEFAULT NULL COMMENT '入职日期',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=59 DEFAULT CHARSET=utf8 COMMENT='员工档案';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `yuangongdangan`
--

LOCK TABLES `yuangongdangan` WRITE;
/*!40000 ALTER TABLE `yuangongdangan` DISABLE KEYS */;
INSERT INTO `yuangongdangan` VALUES (51,'2024-05-13 14:31:19','工号1','姓名1','性别1','联系电话1','部门1','职位1','在职','','2024-05-13'),(52,'2024-05-13 14:31:19','工号2','姓名2','性别2','联系电话2','部门2','职位2','在职','','2024-05-13'),(53,'2024-05-13 14:31:19','工号3','姓名3','性别3','联系电话3','部门3','职位3','在职','','2024-05-13'),(54,'2024-05-13 14:31:19','工号4','姓名4','性别4','联系电话4','部门4','职位4','在职','','2024-05-13'),(55,'2024-05-13 14:31:19','工号5','姓名5','性别5','联系电话5','部门5','职位5','在职','','2024-05-13'),(56,'2024-05-13 14:31:19','工号6','姓名6','性别6','联系电话6','部门6','职位6','在职','','2024-05-13'),(57,'2024-05-13 14:31:19','工号7','姓名7','性别7','联系电话7','部门7','职位7','在职','','2024-05-13'),(58,'2024-05-13 14:31:19','工号8','姓名8','性别8','联系电话8','部门8','职位8','在职','','2024-05-13');
/*!40000 ALTER TABLE `yuangongdangan` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `yuangonggongzi`
--

DROP TABLE IF EXISTS `yuangonggongzi`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `yuangonggongzi` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `addtime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `yuefen` varchar(200) NOT NULL COMMENT '月份',
  `gonghao` varchar(200) NOT NULL COMMENT '工号',
  `xingming` varchar(200) NOT NULL COMMENT '姓名',
  `bumen` varchar(200) NOT NULL COMMENT '部门',
  `zhiwei` varchar(200) DEFAULT NULL COMMENT '职位',
  `jibengongzi` double NOT NULL COMMENT '基本工资',
  `jiabangongzi` double NOT NULL COMMENT '加班工资',
  `jixiaojine` double NOT NULL COMMENT '绩效金额',
  `koukuanjine` double NOT NULL COMMENT '扣款金额',
  `qitabuzhu` double NOT NULL COMMENT '其他补助',
  `koukuanyuanyin` longtext NOT NULL COMMENT '扣款原因',
  `shifagongzi` double DEFAULT NULL COMMENT '实发工资',
  `faburiqi` date DEFAULT NULL COMMENT '发布日期',
  `fuli` longtext COMMENT '福利',
  `ispay` varchar(200) DEFAULT '未支付' COMMENT '是否支付',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=139 DEFAULT CHARSET=utf8 COMMENT='员工工资';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `yuangonggongzi`
--

LOCK TABLES `yuangonggongzi` WRITE;
/*!40000 ALTER TABLE `yuangonggongzi` DISABLE KEYS */;
INSERT INTO `yuangonggongzi` VALUES (131,'2024-05-13 14:31:19','一月','工号1','姓名1','部门1','职位1',1,1,1,1,1,'扣款原因1',1,'2024-05-13','福利1','未支付'),(132,'2024-05-13 14:31:19','一月','工号2','姓名2','部门2','职位2',2,2,2,2,2,'扣款原因2',2,'2024-05-13','福利2','未支付'),(133,'2024-05-13 14:31:19','一月','工号3','姓名3','部门3','职位3',3,3,3,3,3,'扣款原因3',3,'2024-05-13','福利3','未支付'),(134,'2024-05-13 14:31:19','一月','工号4','姓名4','部门4','职位4',4,4,4,4,4,'扣款原因4',4,'2024-05-13','福利4','未支付'),(135,'2024-05-13 14:31:19','一月','工号5','姓名5','部门5','职位5',5,5,5,5,5,'扣款原因5',5,'2024-05-13','福利5','未支付'),(136,'2024-05-13 14:31:19','一月','工号6','姓名6','部门6','职位6',6,6,6,6,6,'扣款原因6',6,'2024-05-13','福利6','未支付'),(137,'2024-05-13 14:31:19','一月','工号7','姓名7','部门7','职位7',7,7,7,7,7,'扣款原因7',7,'2024-05-13','福利7','未支付'),(138,'2024-05-13 14:31:19','一月','工号8','姓名8','部门8','职位8',8,8,8,8,8,'扣款原因8',8,'2024-05-13','福利8','未支付');
/*!40000 ALTER TABLE `yuangonggongzi` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `zhiwei`
--

DROP TABLE IF EXISTS `zhiwei`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `zhiwei` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `addtime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `bumen` varchar(200) DEFAULT NULL COMMENT '部门',
  `zhiwei` varchar(200) NOT NULL COMMENT '职位',
  PRIMARY KEY (`id`),
  UNIQUE KEY `zhiwei` (`zhiwei`)
) ENGINE=InnoDB AUTO_INCREMENT=39 DEFAULT CHARSET=utf8 COMMENT='职位';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `zhiwei`
--

LOCK TABLES `zhiwei` WRITE;
/*!40000 ALTER TABLE `zhiwei` DISABLE KEYS */;
INSERT INTO `zhiwei` VALUES (31,'2024-05-13 14:31:19','部门1','职位1'),(32,'2024-05-13 14:31:19','部门2','职位2'),(33,'2024-05-13 14:31:19','部门3','职位3'),(34,'2024-05-13 14:31:19','部门4','职位4'),(35,'2024-05-13 14:31:19','部门5','职位5'),(36,'2024-05-13 14:31:19','部门6','职位6'),(37,'2024-05-13 14:31:19','部门7','职位7'),(38,'2024-05-13 14:31:19','部门8','职位8');
/*!40000 ALTER TABLE `zhiwei` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `zichancaigou`
--

DROP TABLE IF EXISTS `zichancaigou`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `zichancaigou` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `addtime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `zichanbianma` varchar(200) DEFAULT NULL COMMENT '资产编码',
  `zichanmingcheng` varchar(200) NOT NULL COMMENT '资产名称',
  `zichanleixing` varchar(200) DEFAULT NULL COMMENT '资产类型',
  `zichantupian` longtext NOT NULL COMMENT '资产图片',
  `zichandanjia` int(11) NOT NULL COMMENT '资产单价',
  `zichanshuliang` int(11) DEFAULT NULL COMMENT '采购数量',
  `zichanzongjia` varchar(200) DEFAULT NULL COMMENT '采购总价',
  `rukuyuanyin` longtext COMMENT '入库原因',
  `rukushijian` date DEFAULT NULL COMMENT '入库时间',
  `gonghao` varchar(200) DEFAULT NULL COMMENT '工号',
  `xingming` varchar(200) DEFAULT NULL COMMENT '姓名',
  `sfsh` varchar(200) DEFAULT '待审核' COMMENT '是否审核',
  `shhf` longtext COMMENT '审核回复',
  `ispay` varchar(200) DEFAULT '未支付' COMMENT '是否支付',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=129 DEFAULT CHARSET=utf8 COMMENT='资产采购';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `zichancaigou`
--

LOCK TABLES `zichancaigou` WRITE;
/*!40000 ALTER TABLE `zichancaigou` DISABLE KEYS */;
INSERT INTO `zichancaigou` VALUES (121,'2024-05-13 14:31:19','资产编码1','资产名称1','资产类型1','upload/zichancaigou_zichantupian1.jpg,upload/zichancaigou_zichantupian2.jpg,upload/zichancaigou_zichantupian3.jpg',1,1,'采购总价1','入库原因1','2024-05-13','工号1','姓名1','是','','未支付'),(122,'2024-05-13 14:31:19','资产编码2','资产名称2','资产类型2','upload/zichancaigou_zichantupian2.jpg,upload/zichancaigou_zichantupian3.jpg,upload/zichancaigou_zichantupian4.jpg',2,2,'采购总价2','入库原因2','2024-05-13','工号2','姓名2','是','','未支付'),(123,'2024-05-13 14:31:19','资产编码3','资产名称3','资产类型3','upload/zichancaigou_zichantupian3.jpg,upload/zichancaigou_zichantupian4.jpg,upload/zichancaigou_zichantupian5.jpg',3,3,'采购总价3','入库原因3','2024-05-13','工号3','姓名3','是','','未支付'),(124,'2024-05-13 14:31:19','资产编码4','资产名称4','资产类型4','upload/zichancaigou_zichantupian4.jpg,upload/zichancaigou_zichantupian5.jpg,upload/zichancaigou_zichantupian6.jpg',4,4,'采购总价4','入库原因4','2024-05-13','工号4','姓名4','是','','未支付'),(125,'2024-05-13 14:31:19','资产编码5','资产名称5','资产类型5','upload/zichancaigou_zichantupian5.jpg,upload/zichancaigou_zichantupian6.jpg,upload/zichancaigou_zichantupian7.jpg',5,5,'采购总价5','入库原因5','2024-05-13','工号5','姓名5','是','','未支付'),(126,'2024-05-13 14:31:19','资产编码6','资产名称6','资产类型6','upload/zichancaigou_zichantupian6.jpg,upload/zichancaigou_zichantupian7.jpg,upload/zichancaigou_zichantupian8.jpg',6,6,'采购总价6','入库原因6','2024-05-13','工号6','姓名6','是','','未支付'),(127,'2024-05-13 14:31:19','资产编码7','资产名称7','资产类型7','upload/zichancaigou_zichantupian7.jpg,upload/zichancaigou_zichantupian8.jpg,upload/zichancaigou_zichantupian9.jpg',7,7,'采购总价7','入库原因7','2024-05-13','工号7','姓名7','是','','未支付'),(128,'2024-05-13 14:31:19','资产编码8','资产名称8','资产类型8','upload/zichancaigou_zichantupian8.jpg,upload/zichancaigou_zichantupian9.jpg,upload/zichancaigou_zichantupian10.jpg',8,8,'采购总价8','入库原因8','2024-05-13','工号8','姓名8','是','','未支付');
/*!40000 ALTER TABLE `zichancaigou` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `zichanleixing`
--

DROP TABLE IF EXISTS `zichanleixing`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `zichanleixing` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `addtime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `zichanleixing` varchar(200) NOT NULL COMMENT '资产类型',
  PRIMARY KEY (`id`),
  UNIQUE KEY `zichanleixing` (`zichanleixing`)
) ENGINE=InnoDB AUTO_INCREMENT=99 DEFAULT CHARSET=utf8 COMMENT='资产类型';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `zichanleixing`
--

LOCK TABLES `zichanleixing` WRITE;
/*!40000 ALTER TABLE `zichanleixing` DISABLE KEYS */;
INSERT INTO `zichanleixing` VALUES (91,'2024-05-13 14:31:19','资产类型1'),(92,'2024-05-13 14:31:19','资产类型2'),(93,'2024-05-13 14:31:19','资产类型3'),(94,'2024-05-13 14:31:19','资产类型4'),(95,'2024-05-13 14:31:19','资产类型5'),(96,'2024-05-13 14:31:19','资产类型6'),(97,'2024-05-13 14:31:19','资产类型7'),(98,'2024-05-13 14:31:19','资产类型8');
/*!40000 ALTER TABLE `zichanleixing` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `zichanshenling`
--

DROP TABLE IF EXISTS `zichanshenling`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `zichanshenling` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `addtime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `zichanbianma` varchar(200) DEFAULT NULL COMMENT '资产编码',
  `zichanmingcheng` varchar(200) NOT NULL COMMENT '资产名称',
  `zichanleixing` varchar(200) DEFAULT NULL COMMENT '资产类型',
  `zichantupian` longtext NOT NULL COMMENT '资产图片',
  `zichanshuliang` int(11) NOT NULL COMMENT '领用数量',
  `lingyongshuoming` longtext NOT NULL COMMENT '领用说明',
  `shenqingshijian` date DEFAULT NULL COMMENT '申请时间',
  `gonghao` varchar(200) DEFAULT NULL COMMENT '工号',
  `xingming` varchar(200) DEFAULT NULL COMMENT '姓名',
  `sfsh` varchar(200) DEFAULT '待审核' COMMENT '是否审核',
  `shhf` longtext COMMENT '审核回复',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=119 DEFAULT CHARSET=utf8 COMMENT='资产申领';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `zichanshenling`
--

LOCK TABLES `zichanshenling` WRITE;
/*!40000 ALTER TABLE `zichanshenling` DISABLE KEYS */;
INSERT INTO `zichanshenling` VALUES (111,'2024-05-13 14:31:19','资产编码1','资产名称1','资产类型1','upload/zichanshenling_zichantupian1.jpg,upload/zichanshenling_zichantupian2.jpg,upload/zichanshenling_zichantupian3.jpg',1,'领用说明1','2024-05-13','工号1','姓名1','是',''),(112,'2024-05-13 14:31:19','资产编码2','资产名称2','资产类型2','upload/zichanshenling_zichantupian2.jpg,upload/zichanshenling_zichantupian3.jpg,upload/zichanshenling_zichantupian4.jpg',2,'领用说明2','2024-05-13','工号2','姓名2','是',''),(113,'2024-05-13 14:31:19','资产编码3','资产名称3','资产类型3','upload/zichanshenling_zichantupian3.jpg,upload/zichanshenling_zichantupian4.jpg,upload/zichanshenling_zichantupian5.jpg',3,'领用说明3','2024-05-13','工号3','姓名3','是',''),(114,'2024-05-13 14:31:19','资产编码4','资产名称4','资产类型4','upload/zichanshenling_zichantupian4.jpg,upload/zichanshenling_zichantupian5.jpg,upload/zichanshenling_zichantupian6.jpg',4,'领用说明4','2024-05-13','工号4','姓名4','是',''),(115,'2024-05-13 14:31:19','资产编码5','资产名称5','资产类型5','upload/zichanshenling_zichantupian5.jpg,upload/zichanshenling_zichantupian6.jpg,upload/zichanshenling_zichantupian7.jpg',5,'领用说明5','2024-05-13','工号5','姓名5','是',''),(116,'2024-05-13 14:31:19','资产编码6','资产名称6','资产类型6','upload/zichanshenling_zichantupian6.jpg,upload/zichanshenling_zichantupian7.jpg,upload/zichanshenling_zichantupian8.jpg',6,'领用说明6','2024-05-13','工号6','姓名6','是',''),(117,'2024-05-13 14:31:19','资产编码7','资产名称7','资产类型7','upload/zichanshenling_zichantupian7.jpg,upload/zichanshenling_zichantupian8.jpg,upload/zichanshenling_zichantupian9.jpg',7,'领用说明7','2024-05-13','工号7','姓名7','是',''),(118,'2024-05-13 14:31:19','资产编码8','资产名称8','资产类型8','upload/zichanshenling_zichantupian8.jpg,upload/zichanshenling_zichantupian9.jpg,upload/zichanshenling_zichantupian10.jpg',8,'领用说明8','2024-05-13','工号8','姓名8','是','');
/*!40000 ALTER TABLE `zichanshenling` ENABLE KEYS */;
UNLOCK TABLES;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2024-05-13 22:51:19
