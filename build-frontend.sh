#!/bin/bash

echo "========================================"
echo "前端打包和部署脚本"
echo "========================================"

echo "1. 进入前端目录..."
cd src/main/resources/admin/admin

echo "2. 安装依赖..."
npm install

echo "3. 执行前端打包..."
npm run build

echo "4. 清理旧的静态文件..."
cd ../../../../../
rm -rf src/main/resources/static/*

echo "5. 复制新的打包文件到static目录..."
cp -r src/main/resources/admin/admin/dist/* src/main/resources/static/

echo "6. 清理dist目录..."
rm -rf src/main/resources/admin/admin/dist

echo "========================================"
echo "前端打包完成！"
echo "现在可以使用Maven打包整个项目了"
echo "========================================"
