{"remainingRequest": "E:\\java系统\\公司财务系统\\src\\main\\resources\\admin\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\java系统\\公司财务系统\\src\\main\\resources\\admin\\admin\\src\\views\\modules\\yuangonggongzi\\add-or-update.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\java系统\\公司财务系统\\src\\main\\resources\\admin\\admin\\src\\views\\modules\\yuangonggongzi\\add-or-update.vue", "mtime": 1754637973541}, {"path": "E:\\java系统\\公司财务系统\\src\\main\\resources\\admin\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754805254356}, {"path": "E:\\java系统\\公司财务系统\\src\\main\\resources\\admin\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754805257322}, {"path": "E:\\java系统\\公司财务系统\\src\\main\\resources\\admin\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754805254356}, {"path": "E:\\java系统\\公司财务系统\\src\\main\\resources\\admin\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754628160828}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQovLyDmlbDlrZfvvIzpgq7ku7bvvIzmiYvmnLrvvIx1cmzvvIzouqvku73or4HmoKHpqowNCmltcG9ydCB7IGlzTnVtYmVyLGlzSW50TnVtZXIsaXNFbWFpbCxpc1Bob25lLCBpc01vYmlsZSxpc1VSTCxjaGVja0lkQ2FyZCB9IGZyb20gIkAvdXRpbHMvdmFsaWRhdGUiOw0KZXhwb3J0IGRlZmF1bHQgew0KCWRhdGEoKSB7DQoJCWxldCBzZWxmID0gdGhpcw0KCQl2YXIgdmFsaWRhdGVJZENhcmQgPSAocnVsZSwgdmFsdWUsIGNhbGxiYWNrKSA9PiB7DQoJCQlpZighdmFsdWUpew0KCQkJCWNhbGxiYWNrKCk7DQoJCQl9IGVsc2UgaWYgKCFjaGVja0lkQ2FyZCh2YWx1ZSkpIHsNCgkJCQljYWxsYmFjayhuZXcgRXJyb3IoIuivt+i+k+WFpeato+ehrueahOi6q+S7veivgeWPtyIpKTsNCgkJCX0gZWxzZSB7DQoJCQkJY2FsbGJhY2soKTsNCgkJCX0NCgkJfTsNCgkJdmFyIHZhbGlkYXRlVXJsID0gKHJ1bGUsIHZhbHVlLCBjYWxsYmFjaykgPT4gew0KCQkJaWYoIXZhbHVlKXsNCgkJCQljYWxsYmFjaygpOw0KCQkJfSBlbHNlIGlmICghaXNVUkwodmFsdWUpKSB7DQoJCQkJY2FsbGJhY2sobmV3IEVycm9yKCLor7fovpPlhaXmraPnoa7nmoRVUkzlnLDlnYAiKSk7DQoJCQl9IGVsc2Ugew0KCQkJCWNhbGxiYWNrKCk7DQoJCQl9DQoJCX07DQoJCXZhciB2YWxpZGF0ZU1vYmlsZSA9IChydWxlLCB2YWx1ZSwgY2FsbGJhY2spID0+IHsNCgkJCWlmKCF2YWx1ZSl7DQoJCQkJY2FsbGJhY2soKTsNCgkJCX0gZWxzZSBpZiAoIWlzTW9iaWxlKHZhbHVlKSkgew0KCQkJCWNhbGxiYWNrKG5ldyBFcnJvcigi6K+36L6T5YWl5q2j56Gu55qE5omL5py65Y+356CBIikpOw0KCQkJfSBlbHNlIHsNCgkJCQljYWxsYmFjaygpOw0KCQkJfQ0KCQl9Ow0KCQl2YXIgdmFsaWRhdGVQaG9uZSA9IChydWxlLCB2YWx1ZSwgY2FsbGJhY2spID0+IHsNCgkJCWlmKCF2YWx1ZSl7DQoJCQkJY2FsbGJhY2soKTsNCgkJCX0gZWxzZSBpZiAoIWlzUGhvbmUodmFsdWUpKSB7DQoJCQkJY2FsbGJhY2sobmV3IEVycm9yKCLor7fovpPlhaXmraPnoa7nmoTnlLXor53lj7fnoIEiKSk7DQoJCQl9IGVsc2Ugew0KCQkJCWNhbGxiYWNrKCk7DQoJCQl9DQoJCX07DQoJCXZhciB2YWxpZGF0ZUVtYWlsID0gKHJ1bGUsIHZhbHVlLCBjYWxsYmFjaykgPT4gew0KCQkJaWYoIXZhbHVlKXsNCgkJCQljYWxsYmFjaygpOw0KCQkJfSBlbHNlIGlmICghaXNFbWFpbCh2YWx1ZSkpIHsNCgkJCQljYWxsYmFjayhuZXcgRXJyb3IoIuivt+i+k+WFpeato+ehrueahOmCrueuseWcsOWdgCIpKTsNCgkJCX0gZWxzZSB7DQoJCQkJY2FsbGJhY2soKTsNCgkJCX0NCgkJfTsNCgkJdmFyIHZhbGlkYXRlTnVtYmVyID0gKHJ1bGUsIHZhbHVlLCBjYWxsYmFjaykgPT4gew0KCQkJaWYoIXZhbHVlKXsNCgkJCQljYWxsYmFjaygpOw0KCQkJfSBlbHNlIGlmICghaXNOdW1iZXIodmFsdWUpKSB7DQoJCQkJY2FsbGJhY2sobmV3IEVycm9yKCLor7fovpPlhaXmlbDlrZciKSk7DQoJCQl9IGVsc2Ugew0KCQkJCWNhbGxiYWNrKCk7DQoJCQl9DQoJCX07DQoJCXZhciB2YWxpZGF0ZUludE51bWJlciA9IChydWxlLCB2YWx1ZSwgY2FsbGJhY2spID0+IHsNCgkJCWlmKCF2YWx1ZSl7DQoJCQkJY2FsbGJhY2soKTsNCgkJCX0gZWxzZSBpZiAoIWlzSW50TnVtZXIodmFsdWUpKSB7DQoJCQkJY2FsbGJhY2sobmV3IEVycm9yKCLor7fovpPlhaXmlbTmlbAiKSk7DQoJCQl9IGVsc2Ugew0KCQkJCWNhbGxiYWNrKCk7DQoJCQl9DQoJCX07DQoJCXJldHVybiB7DQoJCQlpZDogJycsDQoJCQl0eXBlOiAnJywNCgkJCQ0KCQkJDQoJCQlybzp7DQoJCQkJeXVlZmVuIDogZmFsc2UsDQoJCQkJZ29uZ2hhbyA6IGZhbHNlLA0KCQkJCXhpbmdtaW5nIDogZmFsc2UsDQoJCQkJYnVtZW4gOiBmYWxzZSwNCgkJCQl6aGl3ZWkgOiBmYWxzZSwNCgkJCQlqaWJlbmdvbmd6aSA6IGZhbHNlLA0KCQkJCWppYWJhbmdvbmd6aSA6IGZhbHNlLA0KCQkJCWppeGlhb2ppbmUgOiBmYWxzZSwNCgkJCQlrb3VrdWFuamluZSA6IGZhbHNlLA0KCQkJCXFpdGFidXpodSA6IGZhbHNlLA0KCQkJCWtvdWt1YW55dWFueWluIDogZmFsc2UsDQoJCQkJc2hpZmFnb25nemkgOiBmYWxzZSwNCgkJCQlmYWJ1cmlxaSA6IGZhbHNlLA0KCQkJCWZ1bGkgOiBmYWxzZSwNCgkJCQlpc3BheSA6IGZhbHNlLA0KCQkJfSwNCgkJCQ0KCQkJDQoJCQlydWxlRm9ybTogew0KCQkJCXl1ZWZlbjogJycsDQoJCQkJZ29uZ2hhbzogJycsDQoJCQkJeGluZ21pbmc6ICcnLA0KCQkJCWJ1bWVuOiAnJywNCgkJCQl6aGl3ZWk6ICcnLA0KCQkJCWppYmVuZ29uZ3ppOiAnJywNCgkJCQlqaWFiYW5nb25nemk6ICcnLA0KCQkJCWppeGlhb2ppbmU6ICcnLA0KCQkJCWtvdWt1YW5qaW5lOiAnJywNCgkJCQlxaXRhYnV6aHU6ICcnLA0KCQkJCWtvdWt1YW55dWFueWluOiAnJywNCgkJCQlzaGlmYWdvbmd6aTogJycsDQoJCQkJZmFidXJpcWk6ICcnLA0KCQkJCWZ1bGk6ICcnLA0KCQkJfSwNCgkJDQoJCQl5dWVmZW5PcHRpb25zOiBbXSwNCgkJCWdvbmdoYW9PcHRpb25zOiBbXSwNCg0KCQkJDQoJCQlydWxlczogew0KCQkJCXl1ZWZlbjogWw0KCQkJCQl7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAn5pyI5Lu95LiN6IO95Li656m6JywgdHJpZ2dlcjogJ2JsdXInIH0sDQoJCQkJXSwNCgkJCQlnb25naGFvOiBbDQoJCQkJCXsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICflt6Xlj7fkuI3og73kuLrnqbonLCB0cmlnZ2VyOiAnYmx1cicgfSwNCgkJCQldLA0KCQkJCXhpbmdtaW5nOiBbDQoJCQkJCXsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICflp5PlkI3kuI3og73kuLrnqbonLCB0cmlnZ2VyOiAnYmx1cicgfSwNCgkJCQldLA0KCQkJCWJ1bWVuOiBbDQoJCQkJCXsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICfpg6jpl6jkuI3og73kuLrnqbonLCB0cmlnZ2VyOiAnYmx1cicgfSwNCgkJCQldLA0KCQkJCXpoaXdlaTogWw0KCQkJCV0sDQoJCQkJamliZW5nb25nemk6IFsNCgkJCQkJeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogJ+WfuuacrOW3pei1hOS4jeiDveS4uuepuicsIHRyaWdnZXI6ICdibHVyJyB9LA0KCQkJCQl7IHZhbGlkYXRvcjogdmFsaWRhdGVOdW1iZXIsIHRyaWdnZXI6ICdibHVyJyB9LA0KCQkJCV0sDQoJCQkJamlhYmFuZ29uZ3ppOiBbDQoJCQkJCXsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICfliqDnj63lt6XotYTkuI3og73kuLrnqbonLCB0cmlnZ2VyOiAnYmx1cicgfSwNCgkJCQkJeyB2YWxpZGF0b3I6IHZhbGlkYXRlTnVtYmVyLCB0cmlnZ2VyOiAnYmx1cicgfSwNCgkJCQldLA0KCQkJCWppeGlhb2ppbmU6IFsNCgkJCQkJeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogJ+e7qeaViOmHkemineS4jeiDveS4uuepuicsIHRyaWdnZXI6ICdibHVyJyB9LA0KCQkJCQl7IHZhbGlkYXRvcjogdmFsaWRhdGVOdW1iZXIsIHRyaWdnZXI6ICdibHVyJyB9LA0KCQkJCV0sDQoJCQkJa291a3VhbmppbmU6IFsNCgkJCQkJeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogJ+aJo+asvumHkemineS4jeiDveS4uuepuicsIHRyaWdnZXI6ICdibHVyJyB9LA0KCQkJCQl7IHZhbGlkYXRvcjogdmFsaWRhdGVOdW1iZXIsIHRyaWdnZXI6ICdibHVyJyB9LA0KCQkJCV0sDQoJCQkJcWl0YWJ1emh1OiBbDQoJCQkJCXsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICflhbbku5booaXliqnkuI3og73kuLrnqbonLCB0cmlnZ2VyOiAnYmx1cicgfSwNCgkJCQkJeyB2YWxpZGF0b3I6IHZhbGlkYXRlTnVtYmVyLCB0cmlnZ2VyOiAnYmx1cicgfSwNCgkJCQldLA0KCQkJCWtvdWt1YW55dWFueWluOiBbDQoJCQkJCXsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICfmiaPmrL7ljp/lm6DkuI3og73kuLrnqbonLCB0cmlnZ2VyOiAnYmx1cicgfSwNCgkJCQldLA0KCQkJCXNoaWZhZ29uZ3ppOiBbDQoJCQkJCXsgdmFsaWRhdG9yOiB2YWxpZGF0ZU51bWJlciwgdHJpZ2dlcjogJ2JsdXInIH0sDQoJCQkJXSwNCgkJCQlmYWJ1cmlxaTogWw0KCQkJCV0sDQoJCQkJZnVsaTogWw0KCQkJCV0sDQoJCQkJaXNwYXk6IFsNCgkJCQldLA0KCQkJfQ0KCQl9Ow0KCX0sDQoJcHJvcHM6IFsicGFyZW50Il0sDQoJY29tcHV0ZWQ6IHsNCgkJc2hpZmFnb25nemk6IHsNCgkJCWdldDogZnVuY3Rpb24gKCkgew0KCQkJCXJldHVybiAwK3BhcnNlRmxvYXQodGhpcy5ydWxlRm9ybS5qaWJlbmdvbmd6aT09IiI/MDp0aGlzLnJ1bGVGb3JtLmppYmVuZ29uZ3ppKStwYXJzZUZsb2F0KHRoaXMucnVsZUZvcm0uamlhYmFuZ29uZ3ppPT0iIj8wOnRoaXMucnVsZUZvcm0uamlhYmFuZ29uZ3ppKStwYXJzZUZsb2F0KHRoaXMucnVsZUZvcm0uaml4aWFvamluZT09IiI/MDp0aGlzLnJ1bGVGb3JtLmppeGlhb2ppbmUpLXBhcnNlRmxvYXQodGhpcy5ydWxlRm9ybS5rb3VrdWFuamluZT09IiI/MDp0aGlzLnJ1bGVGb3JtLmtvdWt1YW5qaW5lKStwYXJzZUZsb2F0KHRoaXMucnVsZUZvcm0ucWl0YWJ1emh1PT0iIj8wOnRoaXMucnVsZUZvcm0ucWl0YWJ1emh1KSB8fCAwDQoJCQl9DQoJCX0sDQoNCg0KDQoJfSwNCiAgICBjb21wb25lbnRzOiB7DQogICAgfSwNCgljcmVhdGVkKCkgew0KCQl0aGlzLnJ1bGVGb3JtLmZhYnVyaXFpID0gdGhpcy5nZXRDdXJEYXRlKCkNCgl9LA0KCW1ldGhvZHM6IHsNCgkJDQoJCS8vIOS4i+i9vQ0KCQlkb3dubG9hZChmaWxlKXsNCgkJCXdpbmRvdy5vcGVuKGAke2ZpbGV9YCkNCgkJfSwNCgkJLy8g5Yid5aeL77+9Pw0KCQlpbml0KGlkLHR5cGUpIHsNCgkJCWlmIChpZCkgew0KCQkJCXRoaXMuaWQgPSBpZDsNCgkJCQl0aGlzLnR5cGUgPSB0eXBlOw0KCQkJfQ0KCQkJaWYodGhpcy50eXBlPT0naW5mbyd8fHRoaXMudHlwZT09J2Vsc2UnKXsNCgkJCQl0aGlzLmluZm8oaWQpOw0KCQkJfWVsc2UgaWYodGhpcy50eXBlPT0nbG9naXN0aWNzJyl7DQoJCQkJdGhpcy5sb2dpc3RpY3M9ZmFsc2U7DQoJCQkJdGhpcy5pbmZvKGlkKTsNCgkJCX1lbHNlIGlmKHRoaXMudHlwZT09J2Nyb3NzJyl7DQoJCQkJdmFyIG9iaiA9IHRoaXMuJHN0b3JhZ2UuZ2V0T2JqKCdjcm9zc09iaicpOw0KCQkJCWZvciAodmFyIG8gaW4gb2JqKXsNCgkJCQkJCWlmKG89PSd5dWVmZW4nKXsNCgkJCQkJCQl0aGlzLnJ1bGVGb3JtLnl1ZWZlbiA9IG9ialtvXTsNCgkJCQkJCQl0aGlzLnJvLnl1ZWZlbiA9IHRydWU7DQoJCQkJCQkJY29udGludWU7DQoJCQkJCQl9DQoJCQkJCQlpZihvPT0nZ29uZ2hhbycpew0KCQkJCQkJCXRoaXMucnVsZUZvcm0uZ29uZ2hhbyA9IG9ialtvXTsNCgkJCQkJCQl0aGlzLnJvLmdvbmdoYW8gPSB0cnVlOw0KCQkJCQkJCWNvbnRpbnVlOw0KCQkJCQkJfQ0KCQkJCQkJaWYobz09J3hpbmdtaW5nJyl7DQoJCQkJCQkJdGhpcy5ydWxlRm9ybS54aW5nbWluZyA9IG9ialtvXTsNCgkJCQkJCQl0aGlzLnJvLnhpbmdtaW5nID0gdHJ1ZTsNCgkJCQkJCQljb250aW51ZTsNCgkJCQkJCX0NCgkJCQkJCWlmKG89PSdidW1lbicpew0KCQkJCQkJCXRoaXMucnVsZUZvcm0uYnVtZW4gPSBvYmpbb107DQoJCQkJCQkJdGhpcy5yby5idW1lbiA9IHRydWU7DQoJCQkJCQkJY29udGludWU7DQoJCQkJCQl9DQoJCQkJCQlpZihvPT0nemhpd2VpJyl7DQoJCQkJCQkJdGhpcy5ydWxlRm9ybS56aGl3ZWkgPSBvYmpbb107DQoJCQkJCQkJdGhpcy5yby56aGl3ZWkgPSB0cnVlOw0KCQkJCQkJCWNvbnRpbnVlOw0KCQkJCQkJfQ0KCQkJCQkJaWYobz09J2ppYmVuZ29uZ3ppJyl7DQoJCQkJCQkJdGhpcy5ydWxlRm9ybS5qaWJlbmdvbmd6aSA9IG9ialtvXTsNCgkJCQkJCQl0aGlzLnJvLmppYmVuZ29uZ3ppID0gdHJ1ZTsNCgkJCQkJCQljb250aW51ZTsNCgkJCQkJCX0NCgkJCQkJCWlmKG89PSdqaWFiYW5nb25nemknKXsNCgkJCQkJCQl0aGlzLnJ1bGVGb3JtLmppYWJhbmdvbmd6aSA9IG9ialtvXTsNCgkJCQkJCQl0aGlzLnJvLmppYWJhbmdvbmd6aSA9IHRydWU7DQoJCQkJCQkJY29udGludWU7DQoJCQkJCQl9DQoJCQkJCQlpZihvPT0naml4aWFvamluZScpew0KCQkJCQkJCXRoaXMucnVsZUZvcm0uaml4aWFvamluZSA9IG9ialtvXTsNCgkJCQkJCQl0aGlzLnJvLmppeGlhb2ppbmUgPSB0cnVlOw0KCQkJCQkJCWNvbnRpbnVlOw0KCQkJCQkJfQ0KCQkJCQkJaWYobz09J2tvdWt1YW5qaW5lJyl7DQoJCQkJCQkJdGhpcy5ydWxlRm9ybS5rb3VrdWFuamluZSA9IG9ialtvXTsNCgkJCQkJCQl0aGlzLnJvLmtvdWt1YW5qaW5lID0gdHJ1ZTsNCgkJCQkJCQljb250aW51ZTsNCgkJCQkJCX0NCgkJCQkJCWlmKG89PSdxaXRhYnV6aHUnKXsNCgkJCQkJCQl0aGlzLnJ1bGVGb3JtLnFpdGFidXpodSA9IG9ialtvXTsNCgkJCQkJCQl0aGlzLnJvLnFpdGFidXpodSA9IHRydWU7DQoJCQkJCQkJY29udGludWU7DQoJCQkJCQl9DQoJCQkJCQlpZihvPT0na291a3Vhbnl1YW55aW4nKXsNCgkJCQkJCQl0aGlzLnJ1bGVGb3JtLmtvdWt1YW55dWFueWluID0gb2JqW29dOw0KCQkJCQkJCXRoaXMucm8ua291a3Vhbnl1YW55aW4gPSB0cnVlOw0KCQkJCQkJCWNvbnRpbnVlOw0KCQkJCQkJfQ0KCQkJCQkJaWYobz09J3NoaWZhZ29uZ3ppJyl7DQoJCQkJCQkJdGhpcy5ydWxlRm9ybS5zaGlmYWdvbmd6aSA9IG9ialtvXTsNCgkJCQkJCQl0aGlzLnJvLnNoaWZhZ29uZ3ppID0gdHJ1ZTsNCgkJCQkJCQljb250aW51ZTsNCgkJCQkJCX0NCgkJCQkJCWlmKG89PSdmYWJ1cmlxaScpew0KCQkJCQkJCXRoaXMucnVsZUZvcm0uZmFidXJpcWkgPSBvYmpbb107DQoJCQkJCQkJdGhpcy5yby5mYWJ1cmlxaSA9IHRydWU7DQoJCQkJCQkJY29udGludWU7DQoJCQkJCQl9DQoJCQkJCQlpZihvPT0nZnVsaScpew0KCQkJCQkJCXRoaXMucnVsZUZvcm0uZnVsaSA9IG9ialtvXTsNCgkJCQkJCQl0aGlzLnJvLmZ1bGkgPSB0cnVlOw0KCQkJCQkJCWNvbnRpbnVlOw0KCQkJCQkJfQ0KCQkJCX0NCgkJCQkNCg0KDQoNCg0KDQoNCg0KDQoNCg0KDQoNCg0KDQoNCgkJCX0NCgkJCQ0KCQkJLy8g6I635Y+W55So5oi35L+h5oGvDQoJCQl0aGlzLiRodHRwKHsNCgkJCQl1cmw6IGAke3RoaXMuJHN0b3JhZ2UuZ2V0KCdzZXNzaW9uVGFibGUnKX0vc2Vzc2lvbmAsDQoJCQkJbWV0aG9kOiAiZ2V0Ig0KCQkJfSkudGhlbigoeyBkYXRhIH0pID0+IHsNCgkJCQlpZiAoZGF0YSAmJiBkYXRhLmNvZGUgPT09IDApIHsNCgkJCQkJDQoJCQkJCXZhciBqc29uID0gZGF0YS5kYXRhOw0KCQkJCQlpZigoKGpzb24uZ29uZ2hhbyE9JycmJmpzb24uZ29uZ2hhbykgfHwganNvbi5nb25naGFvPT0wKSAmJiB0aGlzLiRzdG9yYWdlLmdldCgicm9sZSIpIT0i566h55CG5ZGYIil7DQoJCQkJCQl0aGlzLnJ1bGVGb3JtLmdvbmdoYW8gPSBqc29uLmdvbmdoYW8NCgkJCQkJCXRoaXMucm8uZ29uZ2hhbyA9IHRydWU7DQoJCQkJCX0NCgkJCQkJaWYoKChqc29uLnhpbmdtaW5nIT0nJyYmanNvbi54aW5nbWluZykgfHwganNvbi54aW5nbWluZz09MCkgJiYgdGhpcy4kc3RvcmFnZS5nZXQoInJvbGUiKSE9IueuoeeQhuWRmCIpew0KCQkJCQkJdGhpcy5ydWxlRm9ybS54aW5nbWluZyA9IGpzb24ueGluZ21pbmcNCgkJCQkJCXRoaXMucm8ueGluZ21pbmcgPSB0cnVlOw0KCQkJCQl9DQoJCQkJCWlmKCgoanNvbi5idW1lbiE9JycmJmpzb24uYnVtZW4pIHx8IGpzb24uYnVtZW49PTApICYmIHRoaXMuJHN0b3JhZ2UuZ2V0KCJyb2xlIikhPSLnrqHnkIblkZgiKXsNCgkJCQkJCXRoaXMucnVsZUZvcm0uYnVtZW4gPSBqc29uLmJ1bWVuDQoJCQkJCQl0aGlzLnJvLmJ1bWVuID0gdHJ1ZTsNCgkJCQkJfQ0KCQkJCX0gZWxzZSB7DQoJCQkJCXRoaXMuJG1lc3NhZ2UuZXJyb3IoZGF0YS5tc2cpOw0KCQkJCX0NCgkJCX0pOw0KCQkJDQogICAgICAgICAgICB0aGlzLnl1ZWZlbk9wdGlvbnMgPSAi5LiA5pyILOS6jOaciCzkuInmnIgs5Zub5pyILOS6lOaciCzlha3mnIgs5LiD5pyILOWFq+aciCzkuZ3mnIgs5Y2B5pyILOWNgeS4gOaciCzljYHkuozmnIgiLnNwbGl0KCcsJykNCiAgICAgICAgICAgIHRoaXMuJGh0dHAoew0KCQkJCXVybDogYG9wdGlvbi95dWFuZ29uZy9nb25naGFvYCwNCgkJCQltZXRob2Q6ICJnZXQiDQogICAgICAgICAgICB9KS50aGVuKCh7IGRhdGEgfSkgPT4gew0KCQkJCWlmIChkYXRhICYmIGRhdGEuY29kZSA9PT0gMCkgew0KCQkJCQl0aGlzLmdvbmdoYW9PcHRpb25zID0gZGF0YS5kYXRhOw0KCQkJCX0gZWxzZSB7DQoJCQkJCXRoaXMuJG1lc3NhZ2UuZXJyb3IoZGF0YS5tc2cpOw0KCQkJCX0NCiAgICAgICAgICAgIH0pOw0KCQkJDQoJCX0sDQoJCQkvLyDkuIvkuozvv70/DQoJCQlnb25naGFvQ2hhbmdlICgpIHsNCgkJCQl0aGlzLiRodHRwKHsNCgkJCQkJdXJsOiBgZm9sbG93L3l1YW5nb25nL2dvbmdoYW8/Y29sdW1uVmFsdWU9YCsgdGhpcy5ydWxlRm9ybS5nb25naGFvLA0KCQkJCQltZXRob2Q6ICJnZXQiDQoJCQkJfSkudGhlbigoeyBkYXRhIH0pID0+IHsNCgkJCQkJaWYgKGRhdGEgJiYgZGF0YS5jb2RlID09PSAwKSB7DQoJCQkJCQlpZihkYXRhLmRhdGEueGluZ21pbmcpew0KCQkJCQkJCXRoaXMucnVsZUZvcm0ueGluZ21pbmcgPSBkYXRhLmRhdGEueGluZ21pbmcNCgkJCQkJCX0NCgkJCQkJCWlmKGRhdGEuZGF0YS5idW1lbil7DQoJCQkJCQkJdGhpcy5ydWxlRm9ybS5idW1lbiA9IGRhdGEuZGF0YS5idW1lbg0KCQkJCQkJfQ0KCQkJCQkJaWYoZGF0YS5kYXRhLnpoaXdlaSl7DQoJCQkJCQkJdGhpcy5ydWxlRm9ybS56aGl3ZWkgPSBkYXRhLmRhdGEuemhpd2VpDQoJCQkJCQl9DQoJCQkJCX0gZWxzZSB7DQoJCQkJCQl0aGlzLiRtZXNzYWdlLmVycm9yKGRhdGEubXNnKTsNCgkJCQkJfQ0KCQkJCX0pOw0KCQkJfSwNCiAgICAvLyDlpJrnuqfogZTliqjlj4LmlbANCg0KICAgIGluZm8oaWQpIHsNCiAgICAgIHRoaXMuJGh0dHAoew0KICAgICAgICB1cmw6IGB5dWFuZ29uZ2dvbmd6aS9pbmZvLyR7aWR9YCwNCiAgICAgICAgbWV0aG9kOiAiZ2V0Ig0KICAgICAgfSkudGhlbigoeyBkYXRhIH0pID0+IHsNCiAgICAgICAgaWYgKGRhdGEgJiYgZGF0YS5jb2RlID09PSAwKSB7DQogICAgICAgIHRoaXMucnVsZUZvcm0gPSBkYXRhLmRhdGE7DQogICAgICAgIC8v6Kej5Yaz5YmN5Y+w5LiK5Lyg5Zu+54mH5ZCO5Y+w5LiN5pi+56S655qE6Zeu6aKYDQogICAgICAgIGxldCByZWc9bmV3IFJlZ0V4cCgnLi4vLi4vLi4vdXBsb2FkJywnZycpLy9n5Luj6KGo5YWo6YOoDQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcihkYXRhLm1zZyk7DQogICAgICAgIH0NCiAgICAgIH0pOw0KICAgIH0sDQoNCg0KICAgIC8vIOaPkOS6pA0KICAgIG9uU3VibWl0KCkgew0KICAgICAgICB0aGlzLnJ1bGVGb3JtLnNoaWZhZ29uZ3ppID0gdGhpcy5zaGlmYWdvbmd6aQ0KDQoNCg0KDQoNCg0KDQoNCg0KDQoNCg0KDQoNCg0KDQp2YXIgb2JqY3Jvc3MgPSB0aGlzLiRzdG9yYWdlLmdldE9iaignY3Jvc3NPYmonKTsNCiAgICAgIC8v5pu05paw6Leo6KGo5bGe77+9Pw0KICAgICAgIHZhciBjcm9zc3VzZXJpZDsNCiAgICAgICB2YXIgY3Jvc3NyZWZpZDsNCiAgICAgICB2YXIgY3Jvc3NvcHRudW07DQogICAgICAgaWYodGhpcy50eXBlPT0nY3Jvc3MnKXsNCiAgICAgICAgICAgICAgICB2YXIgc3RhdHVzQ29sdW1uTmFtZSA9IHRoaXMuJHN0b3JhZ2UuZ2V0KCdzdGF0dXNDb2x1bW5OYW1lJyk7DQogICAgICAgICAgICAgICAgdmFyIHN0YXR1c0NvbHVtblZhbHVlID0gdGhpcy4kc3RvcmFnZS5nZXQoJ3N0YXR1c0NvbHVtblZhbHVlJyk7DQogICAgICAgICAgICAgICAgaWYoc3RhdHVzQ29sdW1uTmFtZSE9JycpIHsNCiAgICAgICAgICAgICAgICAgICAgICAgIHZhciBvYmogPSB0aGlzLiRzdG9yYWdlLmdldE9iaignY3Jvc3NPYmonKTsNCiAgICAgICAgICAgICAgICAgICAgICAgaWYoc3RhdHVzQ29sdW1uTmFtZSAmJiAhc3RhdHVzQ29sdW1uTmFtZS5zdGFydHNXaXRoKCJbIikpIHsNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBmb3IgKHZhciBvIGluIG9iail7DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBpZihvPT1zdGF0dXNDb2x1bW5OYW1lKXsNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgb2JqW29dID0gc3RhdHVzQ29sdW1uVmFsdWU7DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZhciB0YWJsZSA9IHRoaXMuJHN0b3JhZ2UuZ2V0KCdjcm9zc1RhYmxlJyk7DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMuJGh0dHAoew0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdXJsOiBgJHt0YWJsZX0vdXBkYXRlYCwNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG1ldGhvZDogInBvc3QiLA0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZGF0YTogb2JqDQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfSkudGhlbigoeyBkYXRhIH0pID0+IHt9KTsNCiAgICAgICAgICAgICAgICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjcm9zc3VzZXJpZD10aGlzLiRzdG9yYWdlLmdldCgndXNlcmlkJyk7DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY3Jvc3NyZWZpZD1vYmpbJ2lkJ107DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY3Jvc3NvcHRudW09dGhpcy4kc3RvcmFnZS5nZXQoJ3N0YXR1c0NvbHVtbk5hbWUnKTsNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjcm9zc29wdG51bT1jcm9zc29wdG51bS5yZXBsYWNlKC9cWy8sIiIpLnJlcGxhY2UoL1xdLywiIik7DQogICAgICAgICAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgICAgfQ0KICAgICAgICB9DQoJCXRoaXMuJHJlZnNbInJ1bGVGb3JtIl0udmFsaWRhdGUodmFsaWQgPT4gew0KCQkJaWYgKHZhbGlkKSB7DQoJCQkJaWYoY3Jvc3NyZWZpZCAmJiBjcm9zc3VzZXJpZCkgew0KCQkJCQl0aGlzLnJ1bGVGb3JtLmNyb3NzdXNlcmlkID0gY3Jvc3N1c2VyaWQ7DQoJCQkJCXRoaXMucnVsZUZvcm0uY3Jvc3NyZWZpZCA9IGNyb3NzcmVmaWQ7DQoJCQkJCWxldCBwYXJhbXMgPSB7IA0KCQkJCQkJcGFnZTogMSwgDQoJCQkJCQlsaW1pdDogMTAsIA0KCQkJCQkJY3Jvc3N1c2VyaWQ6dGhpcy5ydWxlRm9ybS5jcm9zc3VzZXJpZCwNCgkJCQkJCWNyb3NzcmVmaWQ6dGhpcy5ydWxlRm9ybS5jcm9zc3JlZmlkLA0KCQkJCQl9IA0KCQkJCXRoaXMuJGh0dHAoeyANCgkJCQkJdXJsOiAieXVhbmdvbmdnb25nemkvcGFnZSIsIA0KCQkJCQltZXRob2Q6ICJnZXQiLCANCgkJCQkJcGFyYW1zOiBwYXJhbXMgDQoJCQkJfSkudGhlbigoeyANCgkJCQkJZGF0YSANCgkJCQl9KSA9PiB7IA0KCQkJCQlpZiAoZGF0YSAmJiBkYXRhLmNvZGUgPT09IDApIHsgDQoJCQkJCQlpZihkYXRhLmRhdGEudG90YWw+PWNyb3Nzb3B0bnVtKSB7DQoJCQkJCQkJdGhpcy4kbWVzc2FnZS5lcnJvcih0aGlzLiRzdG9yYWdlLmdldCgndGlwcycpKTsNCgkJCQkJCQlyZXR1cm4gZmFsc2U7DQoJCQkJCQl9IGVsc2Ugew0KCQkJCQkJCXRoaXMuJGh0dHAoew0KCQkJCQkJCQl1cmw6IGB5dWFuZ29uZ2dvbmd6aS8keyF0aGlzLnJ1bGVGb3JtLmlkID8gInNhdmUiIDogInVwZGF0ZSJ9YCwNCgkJCQkJCQkJbWV0aG9kOiAicG9zdCIsDQoJCQkJCQkJCWRhdGE6IHRoaXMucnVsZUZvcm0NCgkJCQkJCQl9KS50aGVuKCh7IGRhdGEgfSkgPT4gew0KCQkJCQkJCQlpZiAoZGF0YSAmJiBkYXRhLmNvZGUgPT09IDApIHsNCgkJCQkJCQkJCXRoaXMuJG1lc3NhZ2Uoew0KCQkJCQkJCQkJCW1lc3NhZ2U6ICLmk43kvZzmiJDlip8iLA0KCQkJCQkJCQkJCXR5cGU6ICJzdWNjZXNzIiwNCgkJCQkJCQkJCQlkdXJhdGlvbjogMTUwMCwNCgkJCQkJCQkJCQlvbkNsb3NlOiAoKSA9PiB7DQoJCQkJCQkJCQkJCXRoaXMucGFyZW50LnNob3dGbGFnID0gdHJ1ZTsNCgkJCQkJCQkJCQkJdGhpcy5wYXJlbnQuYWRkT3JVcGRhdGVGbGFnID0gZmFsc2U7DQoJCQkJCQkJCQkJCXRoaXMucGFyZW50Lnl1YW5nb25nZ29uZ3ppQ3Jvc3NBZGRPclVwZGF0ZUZsYWcgPSBmYWxzZTsNCgkJCQkJCQkJCQkJdGhpcy5wYXJlbnQuc2VhcmNoKCk7DQoJCQkJCQkJCQkJCXRoaXMucGFyZW50LmNvbnRlbnRTdHlsZUNoYW5nZSgpOw0KCQkJCQkJCQkJCX0NCgkJCQkJCQkJCX0pOw0KCQkJCQkJCQl9IGVsc2Ugew0KCQkJCQkJCQkJdGhpcy4kbWVzc2FnZS5lcnJvcihkYXRhLm1zZyk7DQoJCQkJCQkJCX0NCgkJCQkJCQl9KTsNCg0KCQkJCQkJfQ0KCQkJCQl9IGVsc2UgeyANCgkJCQl9IA0KCQkJfSk7DQoJCX0gZWxzZSB7DQoJCQl0aGlzLiRodHRwKHsNCgkJCQl1cmw6IGB5dWFuZ29uZ2dvbmd6aS8keyF0aGlzLnJ1bGVGb3JtLmlkID8gInNhdmUiIDogInVwZGF0ZSJ9YCwNCgkJCQltZXRob2Q6ICJwb3N0IiwNCgkJCSAgIGRhdGE6IHRoaXMucnVsZUZvcm0NCgkJCX0pLnRoZW4oKHsgZGF0YSB9KSA9PiB7DQoJCQkJaWYgKGRhdGEgJiYgZGF0YS5jb2RlID09PSAwKSB7DQoJCQkJCXRoaXMuJG1lc3NhZ2Uoew0KCQkJCQkJbWVzc2FnZTogIuaTjeS9nOaIkOWKnyIsDQoJCQkJCQl0eXBlOiAic3VjY2VzcyIsDQoJCQkJCQlkdXJhdGlvbjogMTUwMCwNCgkJCQkJCW9uQ2xvc2U6ICgpID0+IHsNCgkJCQkJCQl0aGlzLnBhcmVudC5zaG93RmxhZyA9IHRydWU7DQoJCQkJCQkJdGhpcy5wYXJlbnQuYWRkT3JVcGRhdGVGbGFnID0gZmFsc2U7DQoJCQkJCQkJdGhpcy5wYXJlbnQueXVhbmdvbmdnb25nemlDcm9zc0FkZE9yVXBkYXRlRmxhZyA9IGZhbHNlOw0KCQkJCQkJCXRoaXMucGFyZW50LnNlYXJjaCgpOw0KCQkJCQkJCXRoaXMucGFyZW50LmNvbnRlbnRTdHlsZUNoYW5nZSgpOw0KCQkJCQkJfQ0KCQkJCQl9KTsNCgkJCQl9IGVsc2Ugew0KCQkJCQl0aGlzLiRtZXNzYWdlLmVycm9yKGRhdGEubXNnKTsNCgkJCSAgIH0NCgkJCX0pOw0KCQkgfQ0KICAgICAgICAgfQ0KICAgICAgIH0pOw0KICAgIH0sDQogICAgLy8g6I635Y+WdXVpZA0KICAgIGdldFVVSUQgKCkgew0KICAgICAgcmV0dXJuIG5ldyBEYXRlKCkuZ2V0VGltZSgpOw0KICAgIH0sDQogICAgLy8g6L+U5ZueDQogICAgYmFjaygpIHsNCiAgICAgIHRoaXMucGFyZW50LnNob3dGbGFnID0gdHJ1ZTsNCiAgICAgIHRoaXMucGFyZW50LmFkZE9yVXBkYXRlRmxhZyA9IGZhbHNlOw0KICAgICAgdGhpcy5wYXJlbnQueXVhbmdvbmdnb25nemlDcm9zc0FkZE9yVXBkYXRlRmxhZyA9IGZhbHNlOw0KICAgICAgdGhpcy5wYXJlbnQuY29udGVudFN0eWxlQ2hhbmdlKCk7DQogICAgfSwNCiAgfQ0KfTsNCg=="}, {"version": 3, "sources": ["add-or-update.vue"], "names": [], "mappings": ";AAsJA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;AAiBA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;;;;;;;;;;;;;;;;;AAiBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "add-or-update.vue", "sourceRoot": "src/views/modules/yuangonggongzi", "sourcesContent": ["<template>\r\n\t<div class=\"addEdit-block\" :style='{\"padding\":\"30px\",\"margin\":\"0\"}'>\r\n\t\t<el-form\r\n\t\t\t:style='{\"border\":\"1px solid rgba(167, 180, 201,.3)  \",\"padding\":\"30px\",\"borderRadius\":\"6px\",\"background\":\"#fff\"}'\r\n\t\t\tclass=\"add-update-preview\"\r\n\t\t\tref=\"ruleForm\"\r\n\t\t\t:model=\"ruleForm\"\r\n\t\t\t:rules=\"rules\"\r\n\t\t\tlabel-width=\"80px\"\r\n\t\t>\r\n\t\t\t<template >\r\n\t\t\t\t<el-form-item :style='{\"margin\":\"0 0 20px 0\"}' class=\"select\" v-if=\"type!='info'\"  label=\"月份\" prop=\"yuefen\">\r\n\t\t\t\t\t<el-select :disabled=\"ro.yuefen\" v-model=\"ruleForm.yuefen\" placeholder=\"请选择月份\" >\r\n\t\t\t\t\t\t<el-option\r\n\t\t\t\t\t\t\tv-for=\"(item,index) in yuefenOptions\"\r\n\t\t\t\t\t\t\tv-bind:key=\"index\"\r\n\t\t\t\t\t\t\t:label=\"item\"\r\n\t\t\t\t\t\t\t:value=\"item\">\r\n\t\t\t\t\t\t</el-option>\r\n\t\t\t\t\t</el-select>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<el-form-item :style='{\"margin\":\"0 0 20px 0\"}' v-else class=\"input\" label=\"月份\" prop=\"yuefen\">\r\n\t\t\t\t\t<el-input v-model=\"ruleForm.yuefen\"\r\n\t\t\t\t\t\tplaceholder=\"月份\" readonly></el-input>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<el-form-item :style='{\"margin\":\"0 0 20px 0\"}' class=\"select\" v-if=\"type!='info'\" label=\"工号\" prop=\"gonghao\">\r\n\t\t\t\t\t<el-select :disabled=\"ro.gonghao\" @change=\"gonghaoChange\" v-model=\"ruleForm.gonghao\" placeholder=\"请选择工号\">\r\n\t\t\t\t\t\t<el-option\r\n\t\t\t\t\t\t\tv-for=\"(item,index) in gonghaoOptions\"\r\n\t\t\t\t\t\t\tv-bind:key=\"index\"\r\n\t\t\t\t\t\t\t:label=\"item\"\r\n\t\t\t\t\t\t\t:value=\"item\">\r\n\t\t\t\t\t\t</el-option>\r\n\t\t\t\t\t</el-select>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<el-form-item :style='{\"margin\":\"0 0 20px 0\"}' class=\"input\" v-else-if=\"ruleForm.gonghao\" label=\"工号\" prop=\"gonghao\">\r\n\t\t\t\t\t<el-input v-model=\"ruleForm.gonghao\" placeholder=\"工号\" readonly></el-input>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<el-form-item :style='{\"margin\":\"0 0 20px 0\"}' class=\"input\" v-if=\"type!='info'\"  label=\"姓名\" prop=\"xingming\">\r\n\t\t\t\t\t<el-input v-model=\"ruleForm.xingming\" placeholder=\"姓名\" clearable  :readonly=\"ro.xingming\"></el-input>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<el-form-item :style='{\"margin\":\"0 0 20px 0\"}' v-else class=\"input\" label=\"姓名\" prop=\"xingming\">\r\n\t\t\t\t\t<el-input v-model=\"ruleForm.xingming\" placeholder=\"姓名\" readonly></el-input>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<el-form-item :style='{\"margin\":\"0 0 20px 0\"}' class=\"input\" v-if=\"type!='info'\"  label=\"部门\" prop=\"bumen\">\r\n\t\t\t\t\t<el-input v-model=\"ruleForm.bumen\" placeholder=\"部门\" clearable  :readonly=\"ro.bumen\"></el-input>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<el-form-item :style='{\"margin\":\"0 0 20px 0\"}' v-else class=\"input\" label=\"部门\" prop=\"bumen\">\r\n\t\t\t\t\t<el-input v-model=\"ruleForm.bumen\" placeholder=\"部门\" readonly></el-input>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<el-form-item :style='{\"margin\":\"0 0 20px 0\"}' class=\"input\" v-if=\"type!='info'\"  label=\"职位\" prop=\"zhiwei\">\r\n\t\t\t\t\t<el-input v-model=\"ruleForm.zhiwei\" placeholder=\"职位\" clearable  :readonly=\"ro.zhiwei\"></el-input>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<el-form-item :style='{\"margin\":\"0 0 20px 0\"}' v-else class=\"input\" label=\"职位\" prop=\"zhiwei\">\r\n\t\t\t\t\t<el-input v-model=\"ruleForm.zhiwei\" placeholder=\"职位\" readonly></el-input>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<el-form-item :style='{\"margin\":\"0 0 20px 0\"}' class=\"input\" v-if=\"type!='info'\"  label=\"基本工资\" prop=\"jibengongzi\">\r\n\t\t\t\t\t<el-input-number v-model=\"ruleForm.jibengongzi\" placeholder=\"基本工资\" :readonly=\"ro.jibengongzi\"></el-input-number>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<el-form-item :style='{\"margin\":\"0 0 20px 0\"}' v-else class=\"input\" label=\"基本工资\" prop=\"jibengongzi\">\r\n\t\t\t\t\t<el-input v-model=\"ruleForm.jibengongzi\" placeholder=\"基本工资\" readonly></el-input>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<el-form-item :style='{\"margin\":\"0 0 20px 0\"}' class=\"input\" v-if=\"type!='info'\"  label=\"加班工资\" prop=\"jiabangongzi\">\r\n\t\t\t\t\t<el-input-number v-model=\"ruleForm.jiabangongzi\" placeholder=\"加班工资\" :readonly=\"ro.jiabangongzi\"></el-input-number>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<el-form-item :style='{\"margin\":\"0 0 20px 0\"}' v-else class=\"input\" label=\"加班工资\" prop=\"jiabangongzi\">\r\n\t\t\t\t\t<el-input v-model=\"ruleForm.jiabangongzi\" placeholder=\"加班工资\" readonly></el-input>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<el-form-item :style='{\"margin\":\"0 0 20px 0\"}' class=\"input\" v-if=\"type!='info'\"  label=\"绩效金额\" prop=\"jixiaojine\">\r\n\t\t\t\t\t<el-input-number v-model=\"ruleForm.jixiaojine\" placeholder=\"绩效金额\" :readonly=\"ro.jixiaojine\"></el-input-number>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<el-form-item :style='{\"margin\":\"0 0 20px 0\"}' v-else class=\"input\" label=\"绩效金额\" prop=\"jixiaojine\">\r\n\t\t\t\t\t<el-input v-model=\"ruleForm.jixiaojine\" placeholder=\"绩效金额\" readonly></el-input>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<el-form-item :style='{\"margin\":\"0 0 20px 0\"}' class=\"input\" v-if=\"type!='info'\"  label=\"扣款金额\" prop=\"koukuanjine\">\r\n\t\t\t\t\t<el-input-number v-model=\"ruleForm.koukuanjine\" placeholder=\"扣款金额\" :readonly=\"ro.koukuanjine\"></el-input-number>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<el-form-item :style='{\"margin\":\"0 0 20px 0\"}' v-else class=\"input\" label=\"扣款金额\" prop=\"koukuanjine\">\r\n\t\t\t\t\t<el-input v-model=\"ruleForm.koukuanjine\" placeholder=\"扣款金额\" readonly></el-input>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<el-form-item :style='{\"margin\":\"0 0 20px 0\"}' class=\"input\" v-if=\"type!='info'\"  label=\"其他补助\" prop=\"qitabuzhu\">\r\n\t\t\t\t\t<el-input-number v-model=\"ruleForm.qitabuzhu\" placeholder=\"其他补助\" :readonly=\"ro.qitabuzhu\"></el-input-number>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<el-form-item :style='{\"margin\":\"0 0 20px 0\"}' v-else class=\"input\" label=\"其他补助\" prop=\"qitabuzhu\">\r\n\t\t\t\t\t<el-input v-model=\"ruleForm.qitabuzhu\" placeholder=\"其他补助\" readonly></el-input>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<el-form-item :style='{\"margin\":\"0 0 20px 0\"}' class=\"input\" v-if=\"type!='info'\" label=\"实发工资\" prop=\"shifagongzi\">\r\n\t\t\t\t\t<el-input v-model=\"shifagongzi\" placeholder=\"实发工资\" readonly></el-input>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<el-form-item :style='{\"margin\":\"0 0 20px 0\"}' class=\"input\" v-else-if=\"ruleForm.shifagongzi\" label=\"实发工资\" prop=\"shifagongzi\">\r\n\t\t\t\t\t<el-input v-model=\"ruleForm.shifagongzi\" placeholder=\"实发工资\" readonly></el-input>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<el-form-item :style='{\"margin\":\"0 0 20px 0\"}' class=\"date\" v-if=\"type!='info'\" label=\"发布日期\" prop=\"faburiqi\">\r\n\t\t\t\t\t<el-date-picker\r\n\t\t\t\t\t\tformat=\"yyyy �?MM �?dd �?\r\n\t\t\t\t\t\tvalue-format=\"yyyy-MM-dd\"\r\n\t\t\t\t\t\tv-model=\"ruleForm.faburiqi\" \r\n\t\t\t\t\t\ttype=\"date\"\r\n\t\t\t\t\t\t:readonly=\"ro.faburiqi\"\r\n\t\t\t\t\t\tplaceholder=\"发布日期\"\r\n\t\t\t\t\t></el-date-picker> \r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<el-form-item :style='{\"margin\":\"0 0 20px 0\"}' class=\"input\" v-else-if=\"ruleForm.faburiqi\" label=\"发布日期\" prop=\"faburiqi\">\r\n\t\t\t\t\t<el-input v-model=\"ruleForm.faburiqi\" placeholder=\"发布日期\" readonly></el-input>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t</template>\r\n\t\t\t\t<el-form-item :style='{\"margin\":\"0 0 20px 0\"}' class=\"textarea\" v-if=\"type!='info'\" label=\"扣款原因\" prop=\"koukuanyuanyin\">\r\n\t\t\t\t\t<el-input\r\n\t\t\t\t\t  style=\"min-width: 200px; max-width: 600px;\"\r\n\t\t\t\t\t  type=\"textarea\"\r\n\t\t\t\t\t  :rows=\"8\"\r\n\t\t\t\t\t  placeholder=\"扣款原因\"\r\n\t\t\t\t\t  v-model=\"ruleForm.koukuanyuanyin\" >\r\n\t\t\t\t\t</el-input>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<el-form-item :style='{\"margin\":\"0 0 20px 0\"}' v-else-if=\"ruleForm.koukuanyuanyin\" label=\"扣款原因\" prop=\"koukuanyuanyin\">\r\n\t\t\t\t\t<span :style='{\"fontSize\":\"14px\",\"lineHeight\":\"40px\",\"color\":\"#333\",\"fontWeight\":\"500\",\"display\":\"inline-block\"}'>{{ruleForm.koukuanyuanyin}}</span>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<el-form-item :style='{\"margin\":\"0 0 20px 0\"}' class=\"textarea\" v-if=\"type!='info'\" label=\"福利\" prop=\"fuli\">\r\n\t\t\t\t\t<el-input\r\n\t\t\t\t\t  style=\"min-width: 200px; max-width: 600px;\"\r\n\t\t\t\t\t  type=\"textarea\"\r\n\t\t\t\t\t  :rows=\"8\"\r\n\t\t\t\t\t  placeholder=\"福利\"\r\n\t\t\t\t\t  v-model=\"ruleForm.fuli\" >\r\n\t\t\t\t\t</el-input>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<el-form-item :style='{\"margin\":\"0 0 20px 0\"}' v-else-if=\"ruleForm.fuli\" label=\"福利\" prop=\"fuli\">\r\n\t\t\t\t\t<span :style='{\"fontSize\":\"14px\",\"lineHeight\":\"40px\",\"color\":\"#333\",\"fontWeight\":\"500\",\"display\":\"inline-block\"}'>{{ruleForm.fuli}}</span>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t<el-form-item :style='{\"padding\":\"0\",\"margin\":\"0\"}' class=\"btn\">\r\n\t\t\t\t<el-button class=\"btn3\"  v-if=\"type!='info'\" type=\"success\" @click=\"onSubmit\">\r\n\t\t\t\t\t<span class=\"icon iconfont icon-xihuan\" :style='{\"margin\":\"0 2px\",\"fontSize\":\"14px\",\"color\":\"#fff\",\"height\":\"40px\"}'></span>\r\n\t\t\t\t\t提交\r\n\t\t\t\t</el-button>\r\n\t\t\t\t<el-button class=\"btn4\" v-if=\"type!='info'\" type=\"success\" @click=\"back()\">\r\n\t\t\t\t\t<span class=\"icon iconfont icon-xihuan\" :style='{\"margin\":\"0 2px\",\"fontSize\":\"14px\",\"color\":\"#fff\",\"height\":\"40px\"}'></span>\r\n\t\t\t\t\t取消\r\n\t\t\t\t</el-button>\r\n\t\t\t\t<el-button class=\"btn5\" v-if=\"type=='info'\" type=\"success\" @click=\"back()\">\r\n\t\t\t\t\t<span class=\"icon iconfont icon-xihuan\" :style='{\"margin\":\"0 2px\",\"fontSize\":\"14px\",\"color\":\"#fff\",\"height\":\"40px\"}'></span>\r\n\t\t\t\t\t返回\r\n\t\t\t\t</el-button>\r\n\t\t\t</el-form-item>\r\n\t\t</el-form>\r\n    \r\n\r\n  </div>\r\n</template>\r\n<script>\r\n// 数字，邮件，手机，url，身份证校验\r\nimport { isNumber,isIntNumer,isEmail,isPhone, isMobile,isURL,checkIdCard } from \"@/utils/validate\";\r\nexport default {\r\n\tdata() {\r\n\t\tlet self = this\r\n\t\tvar validateIdCard = (rule, value, callback) => {\r\n\t\t\tif(!value){\r\n\t\t\t\tcallback();\r\n\t\t\t} else if (!checkIdCard(value)) {\r\n\t\t\t\tcallback(new Error(\"请输入正确的身份证号\"));\r\n\t\t\t} else {\r\n\t\t\t\tcallback();\r\n\t\t\t}\r\n\t\t};\r\n\t\tvar validateUrl = (rule, value, callback) => {\r\n\t\t\tif(!value){\r\n\t\t\t\tcallback();\r\n\t\t\t} else if (!isURL(value)) {\r\n\t\t\t\tcallback(new Error(\"请输入正确的URL地址\"));\r\n\t\t\t} else {\r\n\t\t\t\tcallback();\r\n\t\t\t}\r\n\t\t};\r\n\t\tvar validateMobile = (rule, value, callback) => {\r\n\t\t\tif(!value){\r\n\t\t\t\tcallback();\r\n\t\t\t} else if (!isMobile(value)) {\r\n\t\t\t\tcallback(new Error(\"请输入正确的手机号码\"));\r\n\t\t\t} else {\r\n\t\t\t\tcallback();\r\n\t\t\t}\r\n\t\t};\r\n\t\tvar validatePhone = (rule, value, callback) => {\r\n\t\t\tif(!value){\r\n\t\t\t\tcallback();\r\n\t\t\t} else if (!isPhone(value)) {\r\n\t\t\t\tcallback(new Error(\"请输入正确的电话号码\"));\r\n\t\t\t} else {\r\n\t\t\t\tcallback();\r\n\t\t\t}\r\n\t\t};\r\n\t\tvar validateEmail = (rule, value, callback) => {\r\n\t\t\tif(!value){\r\n\t\t\t\tcallback();\r\n\t\t\t} else if (!isEmail(value)) {\r\n\t\t\t\tcallback(new Error(\"请输入正确的邮箱地址\"));\r\n\t\t\t} else {\r\n\t\t\t\tcallback();\r\n\t\t\t}\r\n\t\t};\r\n\t\tvar validateNumber = (rule, value, callback) => {\r\n\t\t\tif(!value){\r\n\t\t\t\tcallback();\r\n\t\t\t} else if (!isNumber(value)) {\r\n\t\t\t\tcallback(new Error(\"请输入数字\"));\r\n\t\t\t} else {\r\n\t\t\t\tcallback();\r\n\t\t\t}\r\n\t\t};\r\n\t\tvar validateIntNumber = (rule, value, callback) => {\r\n\t\t\tif(!value){\r\n\t\t\t\tcallback();\r\n\t\t\t} else if (!isIntNumer(value)) {\r\n\t\t\t\tcallback(new Error(\"请输入整数\"));\r\n\t\t\t} else {\r\n\t\t\t\tcallback();\r\n\t\t\t}\r\n\t\t};\r\n\t\treturn {\r\n\t\t\tid: '',\r\n\t\t\ttype: '',\r\n\t\t\t\r\n\t\t\t\r\n\t\t\tro:{\r\n\t\t\t\tyuefen : false,\r\n\t\t\t\tgonghao : false,\r\n\t\t\t\txingming : false,\r\n\t\t\t\tbumen : false,\r\n\t\t\t\tzhiwei : false,\r\n\t\t\t\tjibengongzi : false,\r\n\t\t\t\tjiabangongzi : false,\r\n\t\t\t\tjixiaojine : false,\r\n\t\t\t\tkoukuanjine : false,\r\n\t\t\t\tqitabuzhu : false,\r\n\t\t\t\tkoukuanyuanyin : false,\r\n\t\t\t\tshifagongzi : false,\r\n\t\t\t\tfaburiqi : false,\r\n\t\t\t\tfuli : false,\r\n\t\t\t\tispay : false,\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t\r\n\t\t\truleForm: {\r\n\t\t\t\tyuefen: '',\r\n\t\t\t\tgonghao: '',\r\n\t\t\t\txingming: '',\r\n\t\t\t\tbumen: '',\r\n\t\t\t\tzhiwei: '',\r\n\t\t\t\tjibengongzi: '',\r\n\t\t\t\tjiabangongzi: '',\r\n\t\t\t\tjixiaojine: '',\r\n\t\t\t\tkoukuanjine: '',\r\n\t\t\t\tqitabuzhu: '',\r\n\t\t\t\tkoukuanyuanyin: '',\r\n\t\t\t\tshifagongzi: '',\r\n\t\t\t\tfaburiqi: '',\r\n\t\t\t\tfuli: '',\r\n\t\t\t},\r\n\t\t\r\n\t\t\tyuefenOptions: [],\r\n\t\t\tgonghaoOptions: [],\r\n\r\n\t\t\t\r\n\t\t\trules: {\r\n\t\t\t\tyuefen: [\r\n\t\t\t\t\t{ required: true, message: '月份不能为空', trigger: 'blur' },\r\n\t\t\t\t],\r\n\t\t\t\tgonghao: [\r\n\t\t\t\t\t{ required: true, message: '工号不能为空', trigger: 'blur' },\r\n\t\t\t\t],\r\n\t\t\t\txingming: [\r\n\t\t\t\t\t{ required: true, message: '姓名不能为空', trigger: 'blur' },\r\n\t\t\t\t],\r\n\t\t\t\tbumen: [\r\n\t\t\t\t\t{ required: true, message: '部门不能为空', trigger: 'blur' },\r\n\t\t\t\t],\r\n\t\t\t\tzhiwei: [\r\n\t\t\t\t],\r\n\t\t\t\tjibengongzi: [\r\n\t\t\t\t\t{ required: true, message: '基本工资不能为空', trigger: 'blur' },\r\n\t\t\t\t\t{ validator: validateNumber, trigger: 'blur' },\r\n\t\t\t\t],\r\n\t\t\t\tjiabangongzi: [\r\n\t\t\t\t\t{ required: true, message: '加班工资不能为空', trigger: 'blur' },\r\n\t\t\t\t\t{ validator: validateNumber, trigger: 'blur' },\r\n\t\t\t\t],\r\n\t\t\t\tjixiaojine: [\r\n\t\t\t\t\t{ required: true, message: '绩效金额不能为空', trigger: 'blur' },\r\n\t\t\t\t\t{ validator: validateNumber, trigger: 'blur' },\r\n\t\t\t\t],\r\n\t\t\t\tkoukuanjine: [\r\n\t\t\t\t\t{ required: true, message: '扣款金额不能为空', trigger: 'blur' },\r\n\t\t\t\t\t{ validator: validateNumber, trigger: 'blur' },\r\n\t\t\t\t],\r\n\t\t\t\tqitabuzhu: [\r\n\t\t\t\t\t{ required: true, message: '其他补助不能为空', trigger: 'blur' },\r\n\t\t\t\t\t{ validator: validateNumber, trigger: 'blur' },\r\n\t\t\t\t],\r\n\t\t\t\tkoukuanyuanyin: [\r\n\t\t\t\t\t{ required: true, message: '扣款原因不能为空', trigger: 'blur' },\r\n\t\t\t\t],\r\n\t\t\t\tshifagongzi: [\r\n\t\t\t\t\t{ validator: validateNumber, trigger: 'blur' },\r\n\t\t\t\t],\r\n\t\t\t\tfaburiqi: [\r\n\t\t\t\t],\r\n\t\t\t\tfuli: [\r\n\t\t\t\t],\r\n\t\t\t\tispay: [\r\n\t\t\t\t],\r\n\t\t\t}\r\n\t\t};\r\n\t},\r\n\tprops: [\"parent\"],\r\n\tcomputed: {\r\n\t\tshifagongzi: {\r\n\t\t\tget: function () {\r\n\t\t\t\treturn 0+parseFloat(this.ruleForm.jibengongzi==\"\"?0:this.ruleForm.jibengongzi)+parseFloat(this.ruleForm.jiabangongzi==\"\"?0:this.ruleForm.jiabangongzi)+parseFloat(this.ruleForm.jixiaojine==\"\"?0:this.ruleForm.jixiaojine)-parseFloat(this.ruleForm.koukuanjine==\"\"?0:this.ruleForm.koukuanjine)+parseFloat(this.ruleForm.qitabuzhu==\"\"?0:this.ruleForm.qitabuzhu) || 0\r\n\t\t\t}\r\n\t\t},\r\n\r\n\r\n\r\n\t},\r\n    components: {\r\n    },\r\n\tcreated() {\r\n\t\tthis.ruleForm.faburiqi = this.getCurDate()\r\n\t},\r\n\tmethods: {\r\n\t\t\r\n\t\t// 下载\r\n\t\tdownload(file){\r\n\t\t\twindow.open(`${file}`)\r\n\t\t},\r\n\t\t// 初始�?\r\n\t\tinit(id,type) {\r\n\t\t\tif (id) {\r\n\t\t\t\tthis.id = id;\r\n\t\t\t\tthis.type = type;\r\n\t\t\t}\r\n\t\t\tif(this.type=='info'||this.type=='else'){\r\n\t\t\t\tthis.info(id);\r\n\t\t\t}else if(this.type=='logistics'){\r\n\t\t\t\tthis.logistics=false;\r\n\t\t\t\tthis.info(id);\r\n\t\t\t}else if(this.type=='cross'){\r\n\t\t\t\tvar obj = this.$storage.getObj('crossObj');\r\n\t\t\t\tfor (var o in obj){\r\n\t\t\t\t\t\tif(o=='yuefen'){\r\n\t\t\t\t\t\t\tthis.ruleForm.yuefen = obj[o];\r\n\t\t\t\t\t\t\tthis.ro.yuefen = true;\r\n\t\t\t\t\t\t\tcontinue;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif(o=='gonghao'){\r\n\t\t\t\t\t\t\tthis.ruleForm.gonghao = obj[o];\r\n\t\t\t\t\t\t\tthis.ro.gonghao = true;\r\n\t\t\t\t\t\t\tcontinue;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif(o=='xingming'){\r\n\t\t\t\t\t\t\tthis.ruleForm.xingming = obj[o];\r\n\t\t\t\t\t\t\tthis.ro.xingming = true;\r\n\t\t\t\t\t\t\tcontinue;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif(o=='bumen'){\r\n\t\t\t\t\t\t\tthis.ruleForm.bumen = obj[o];\r\n\t\t\t\t\t\t\tthis.ro.bumen = true;\r\n\t\t\t\t\t\t\tcontinue;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif(o=='zhiwei'){\r\n\t\t\t\t\t\t\tthis.ruleForm.zhiwei = obj[o];\r\n\t\t\t\t\t\t\tthis.ro.zhiwei = true;\r\n\t\t\t\t\t\t\tcontinue;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif(o=='jibengongzi'){\r\n\t\t\t\t\t\t\tthis.ruleForm.jibengongzi = obj[o];\r\n\t\t\t\t\t\t\tthis.ro.jibengongzi = true;\r\n\t\t\t\t\t\t\tcontinue;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif(o=='jiabangongzi'){\r\n\t\t\t\t\t\t\tthis.ruleForm.jiabangongzi = obj[o];\r\n\t\t\t\t\t\t\tthis.ro.jiabangongzi = true;\r\n\t\t\t\t\t\t\tcontinue;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif(o=='jixiaojine'){\r\n\t\t\t\t\t\t\tthis.ruleForm.jixiaojine = obj[o];\r\n\t\t\t\t\t\t\tthis.ro.jixiaojine = true;\r\n\t\t\t\t\t\t\tcontinue;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif(o=='koukuanjine'){\r\n\t\t\t\t\t\t\tthis.ruleForm.koukuanjine = obj[o];\r\n\t\t\t\t\t\t\tthis.ro.koukuanjine = true;\r\n\t\t\t\t\t\t\tcontinue;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif(o=='qitabuzhu'){\r\n\t\t\t\t\t\t\tthis.ruleForm.qitabuzhu = obj[o];\r\n\t\t\t\t\t\t\tthis.ro.qitabuzhu = true;\r\n\t\t\t\t\t\t\tcontinue;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif(o=='koukuanyuanyin'){\r\n\t\t\t\t\t\t\tthis.ruleForm.koukuanyuanyin = obj[o];\r\n\t\t\t\t\t\t\tthis.ro.koukuanyuanyin = true;\r\n\t\t\t\t\t\t\tcontinue;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif(o=='shifagongzi'){\r\n\t\t\t\t\t\t\tthis.ruleForm.shifagongzi = obj[o];\r\n\t\t\t\t\t\t\tthis.ro.shifagongzi = true;\r\n\t\t\t\t\t\t\tcontinue;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif(o=='faburiqi'){\r\n\t\t\t\t\t\t\tthis.ruleForm.faburiqi = obj[o];\r\n\t\t\t\t\t\t\tthis.ro.faburiqi = true;\r\n\t\t\t\t\t\t\tcontinue;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif(o=='fuli'){\r\n\t\t\t\t\t\t\tthis.ruleForm.fuli = obj[o];\r\n\t\t\t\t\t\t\tthis.ro.fuli = true;\r\n\t\t\t\t\t\t\tcontinue;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t// 获取用户信息\r\n\t\t\tthis.$http({\r\n\t\t\t\turl: `${this.$storage.get('sessionTable')}/session`,\r\n\t\t\t\tmethod: \"get\"\r\n\t\t\t}).then(({ data }) => {\r\n\t\t\t\tif (data && data.code === 0) {\r\n\t\t\t\t\t\r\n\t\t\t\t\tvar json = data.data;\r\n\t\t\t\t\tif(((json.gonghao!=''&&json.gonghao) || json.gonghao==0) && this.$storage.get(\"role\")!=\"管理员\"){\r\n\t\t\t\t\t\tthis.ruleForm.gonghao = json.gonghao\r\n\t\t\t\t\t\tthis.ro.gonghao = true;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif(((json.xingming!=''&&json.xingming) || json.xingming==0) && this.$storage.get(\"role\")!=\"管理员\"){\r\n\t\t\t\t\t\tthis.ruleForm.xingming = json.xingming\r\n\t\t\t\t\t\tthis.ro.xingming = true;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif(((json.bumen!=''&&json.bumen) || json.bumen==0) && this.$storage.get(\"role\")!=\"管理员\"){\r\n\t\t\t\t\t\tthis.ruleForm.bumen = json.bumen\r\n\t\t\t\t\t\tthis.ro.bumen = true;\r\n\t\t\t\t\t}\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.$message.error(data.msg);\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t\t\r\n            this.yuefenOptions = \"一月,二月,三月,四月,五月,六月,七月,八月,九月,十月,十一月,十二月\".split(',')\r\n            this.$http({\r\n\t\t\t\turl: `option/yuangong/gonghao`,\r\n\t\t\t\tmethod: \"get\"\r\n            }).then(({ data }) => {\r\n\t\t\t\tif (data && data.code === 0) {\r\n\t\t\t\t\tthis.gonghaoOptions = data.data;\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.$message.error(data.msg);\r\n\t\t\t\t}\r\n            });\r\n\t\t\t\r\n\t\t},\r\n\t\t\t// 下二�?\r\n\t\t\tgonghaoChange () {\r\n\t\t\t\tthis.$http({\r\n\t\t\t\t\turl: `follow/yuangong/gonghao?columnValue=`+ this.ruleForm.gonghao,\r\n\t\t\t\t\tmethod: \"get\"\r\n\t\t\t\t}).then(({ data }) => {\r\n\t\t\t\t\tif (data && data.code === 0) {\r\n\t\t\t\t\t\tif(data.data.xingming){\r\n\t\t\t\t\t\t\tthis.ruleForm.xingming = data.data.xingming\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif(data.data.bumen){\r\n\t\t\t\t\t\t\tthis.ruleForm.bumen = data.data.bumen\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif(data.data.zhiwei){\r\n\t\t\t\t\t\t\tthis.ruleForm.zhiwei = data.data.zhiwei\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tthis.$message.error(data.msg);\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n    // 多级联动参数\r\n\r\n    info(id) {\r\n      this.$http({\r\n        url: `yuangonggongzi/info/${id}`,\r\n        method: \"get\"\r\n      }).then(({ data }) => {\r\n        if (data && data.code === 0) {\r\n        this.ruleForm = data.data;\r\n        //解决前台上传图片后台不显示的问题\r\n        let reg=new RegExp('../../../upload','g')//g代表全部\r\n        } else {\r\n          this.$message.error(data.msg);\r\n        }\r\n      });\r\n    },\r\n\r\n\r\n    // 提交\r\n    onSubmit() {\r\n        this.ruleForm.shifagongzi = this.shifagongzi\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\nvar objcross = this.$storage.getObj('crossObj');\r\n      //更新跨表属�?\r\n       var crossuserid;\r\n       var crossrefid;\r\n       var crossoptnum;\r\n       if(this.type=='cross'){\r\n                var statusColumnName = this.$storage.get('statusColumnName');\r\n                var statusColumnValue = this.$storage.get('statusColumnValue');\r\n                if(statusColumnName!='') {\r\n                        var obj = this.$storage.getObj('crossObj');\r\n                       if(statusColumnName && !statusColumnName.startsWith(\"[\")) {\r\n                               for (var o in obj){\r\n                                 if(o==statusColumnName){\r\n                                   obj[o] = statusColumnValue;\r\n                                 }\r\n                               }\r\n                               var table = this.$storage.get('crossTable');\r\n                             this.$http({\r\n                                 url: `${table}/update`,\r\n                                 method: \"post\",\r\n                                 data: obj\r\n                               }).then(({ data }) => {});\r\n                       } else {\r\n                               crossuserid=this.$storage.get('userid');\r\n                               crossrefid=obj['id'];\r\n                               crossoptnum=this.$storage.get('statusColumnName');\r\n                               crossoptnum=crossoptnum.replace(/\\[/,\"\").replace(/\\]/,\"\");\r\n                        }\r\n                }\r\n        }\r\n\t\tthis.$refs[\"ruleForm\"].validate(valid => {\r\n\t\t\tif (valid) {\r\n\t\t\t\tif(crossrefid && crossuserid) {\r\n\t\t\t\t\tthis.ruleForm.crossuserid = crossuserid;\r\n\t\t\t\t\tthis.ruleForm.crossrefid = crossrefid;\r\n\t\t\t\t\tlet params = { \r\n\t\t\t\t\t\tpage: 1, \r\n\t\t\t\t\t\tlimit: 10, \r\n\t\t\t\t\t\tcrossuserid:this.ruleForm.crossuserid,\r\n\t\t\t\t\t\tcrossrefid:this.ruleForm.crossrefid,\r\n\t\t\t\t\t} \r\n\t\t\t\tthis.$http({ \r\n\t\t\t\t\turl: \"yuangonggongzi/page\", \r\n\t\t\t\t\tmethod: \"get\", \r\n\t\t\t\t\tparams: params \r\n\t\t\t\t}).then(({ \r\n\t\t\t\t\tdata \r\n\t\t\t\t}) => { \r\n\t\t\t\t\tif (data && data.code === 0) { \r\n\t\t\t\t\t\tif(data.data.total>=crossoptnum) {\r\n\t\t\t\t\t\t\tthis.$message.error(this.$storage.get('tips'));\r\n\t\t\t\t\t\t\treturn false;\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tthis.$http({\r\n\t\t\t\t\t\t\t\turl: `yuangonggongzi/${!this.ruleForm.id ? \"save\" : \"update\"}`,\r\n\t\t\t\t\t\t\t\tmethod: \"post\",\r\n\t\t\t\t\t\t\t\tdata: this.ruleForm\r\n\t\t\t\t\t\t\t}).then(({ data }) => {\r\n\t\t\t\t\t\t\t\tif (data && data.code === 0) {\r\n\t\t\t\t\t\t\t\t\tthis.$message({\r\n\t\t\t\t\t\t\t\t\t\tmessage: \"操作成功\",\r\n\t\t\t\t\t\t\t\t\t\ttype: \"success\",\r\n\t\t\t\t\t\t\t\t\t\tduration: 1500,\r\n\t\t\t\t\t\t\t\t\t\tonClose: () => {\r\n\t\t\t\t\t\t\t\t\t\t\tthis.parent.showFlag = true;\r\n\t\t\t\t\t\t\t\t\t\t\tthis.parent.addOrUpdateFlag = false;\r\n\t\t\t\t\t\t\t\t\t\t\tthis.parent.yuangonggongziCrossAddOrUpdateFlag = false;\r\n\t\t\t\t\t\t\t\t\t\t\tthis.parent.search();\r\n\t\t\t\t\t\t\t\t\t\t\tthis.parent.contentStyleChange();\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\tthis.$message.error(data.msg);\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t});\r\n\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t} else { \r\n\t\t\t\t} \r\n\t\t\t});\r\n\t\t} else {\r\n\t\t\tthis.$http({\r\n\t\t\t\turl: `yuangonggongzi/${!this.ruleForm.id ? \"save\" : \"update\"}`,\r\n\t\t\t\tmethod: \"post\",\r\n\t\t\t   data: this.ruleForm\r\n\t\t\t}).then(({ data }) => {\r\n\t\t\t\tif (data && data.code === 0) {\r\n\t\t\t\t\tthis.$message({\r\n\t\t\t\t\t\tmessage: \"操作成功\",\r\n\t\t\t\t\t\ttype: \"success\",\r\n\t\t\t\t\t\tduration: 1500,\r\n\t\t\t\t\t\tonClose: () => {\r\n\t\t\t\t\t\t\tthis.parent.showFlag = true;\r\n\t\t\t\t\t\t\tthis.parent.addOrUpdateFlag = false;\r\n\t\t\t\t\t\t\tthis.parent.yuangonggongziCrossAddOrUpdateFlag = false;\r\n\t\t\t\t\t\t\tthis.parent.search();\r\n\t\t\t\t\t\t\tthis.parent.contentStyleChange();\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t});\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.$message.error(data.msg);\r\n\t\t\t   }\r\n\t\t\t});\r\n\t\t }\r\n         }\r\n       });\r\n    },\r\n    // 获取uuid\r\n    getUUID () {\r\n      return new Date().getTime();\r\n    },\r\n    // 返回\r\n    back() {\r\n      this.parent.showFlag = true;\r\n      this.parent.addOrUpdateFlag = false;\r\n      this.parent.yuangonggongziCrossAddOrUpdateFlag = false;\r\n      this.parent.contentStyleChange();\r\n    },\r\n  }\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n\t.amap-wrapper {\r\n\t\twidth: 100%;\r\n\t\theight: 500px;\r\n\t}\r\n\t\r\n\t.search-box {\r\n\t\tposition: absolute;\r\n\t}\r\n\t\r\n\t.el-date-editor.el-input {\r\n\t\twidth: auto;\r\n\t}\r\n\t\r\n\t.add-update-preview .el-form-item :deep .el-form-item__label {\r\n\t  \t  padding: 0 10px 0 0;\r\n\t  \t  color: #374254;\r\n\t  \t  font-weight: 600;\r\n\t  \t  width: 80px;\r\n\t  \t  font-size: 14px;\r\n\t  \t  line-height: 40px;\r\n\t  \t  text-align: right;\r\n\t  \t}\r\n\t\r\n\t.add-update-preview .el-form-item :deep .el-form-item__content {\r\n\t  margin-left: 80px;\r\n\t}\r\n\t\r\n\t.add-update-preview .el-input :deep .el-input__inner {\r\n\t  \t  border: 1px solid rgba(167, 180, 201,.3);\r\n\t  \t  border-radius: 4px;\r\n\t  \t  padding: 0 12px;\r\n\t  \t  outline: none;\r\n\t  \t  color: #a7b4c9;\r\n\t  \t  width: 400px;\r\n\t  \t  font-size: 14px;\r\n\t  \t  height: 40px;\r\n\t  \t}\r\n\t.add-update-preview .el-input-number :deep .el-input__inner {\r\n\t\ttext-align: left;\r\n\t  \t  border: 1px solid rgba(167, 180, 201,.3);\r\n\t  \t  border-radius: 4px;\r\n\t  \t  padding: 0 12px;\r\n\t  \t  outline: none;\r\n\t  \t  color: #a7b4c9;\r\n\t  \t  width: 400px;\r\n\t  \t  font-size: 14px;\r\n\t  \t  height: 40px;\r\n\t  \t}\r\n\t.add-update-preview .el-input-number :deep .el-input-number__decrease {\r\n\t\tdisplay: none;\r\n\t}\r\n\t.add-update-preview .el-input-number :deep .el-input-number__increase {\r\n\t\tdisplay: none;\r\n\t}\r\n\t\r\n\t.add-update-preview .el-select :deep .el-input__inner {\r\n\t  \t  border:  1px solid rgba(167, 180, 201,.3) ;\r\n\t  \t  border-radius: 4px;\r\n\t  \t  padding: 0 10px;\r\n\t  \t  outline: none;\r\n\t  \t  color: #a7b4c9;\r\n\t  \t  width: 400px;\r\n\t  \t  font-size: 14px;\r\n\t  \t  height: 40px;\r\n\t  \t}\r\n\t\r\n\t.add-update-preview .el-date-editor :deep .el-input__inner {\r\n\t  \t  border:  1px solid rgba(167, 180, 201,.3) ;\r\n\t  \t  border-radius: 4px;\r\n\t  \t  padding: 0 10px 0 30px;\r\n\t  \t  outline: none;\r\n\t  \t  color: #a7b4c9;\r\n\t  \t  width: 400px;\r\n\t  \t  font-size: 14px;\r\n\t  \t  height: 40px;\r\n\t  \t}\r\n\t\r\n\t.add-update-preview :deep .el-upload--picture-card {\r\n\t\tbackground: transparent;\r\n\t\tborder: 0;\r\n\t\tborder-radius: 0;\r\n\t\twidth: auto;\r\n\t\theight: auto;\r\n\t\tline-height: initial;\r\n\t\tvertical-align: middle;\r\n\t}\r\n\t\r\n\t.add-update-preview :deep .upload .upload-img {\r\n\t  \t  border: 2px dashed rgba(167, 180, 201,.3) ;\r\n\t  \t  cursor: pointer;\r\n\t  \t  border-radius: 6px;\r\n\t  \t  color: #a7b4c9  ;\r\n\t  \t  width: 200px;\r\n\t  \t  font-size: 32px;\r\n\t  \t  line-height: 200px;\r\n\t  \t  text-align: center;\r\n\t  \t  height: 200px;\r\n\t  \t}\r\n\t\r\n\t.add-update-preview :deep .el-upload-list .el-upload-list__item {\r\n\t  \t  border: 2px dashed rgba(167, 180, 201,.3) ;\r\n\t  \t  cursor: pointer;\r\n\t  \t  border-radius: 6px;\r\n\t  \t  color: #a7b4c9  ;\r\n\t  \t  width: 200px;\r\n\t  \t  font-size: 32px;\r\n\t  \t  line-height: 200px;\r\n\t  \t  text-align: center;\r\n\t  \t  height: 200px;\r\n\t  \t}\r\n\t\r\n\t.add-update-preview :deep .el-upload .el-icon-plus {\r\n\t  \t  border: 2px dashed rgba(167, 180, 201,.3) ;\r\n\t  \t  cursor: pointer;\r\n\t  \t  border-radius: 6px;\r\n\t  \t  color: #a7b4c9  ;\r\n\t  \t  width: 200px;\r\n\t  \t  font-size: 32px;\r\n\t  \t  line-height: 200px;\r\n\t  \t  text-align: center;\r\n\t  \t  height: 200px;\r\n\t  \t}\r\n\t\r\n\t.add-update-preview .el-textarea :deep .el-textarea__inner {\r\n\t  \t  border: 1px solid rgba(167, 180, 201,.3)     ;\r\n\t  \t  border-radius: 4px;\r\n\t  \t  padding: 12px;\r\n\t  \t  outline: none;\r\n\t  \t  color: #a7b4c9 ;\r\n\t  \t  width: 400px;\r\n\t  \t  font-size: 14px;\r\n\t  \t  height: 120px;\r\n\t  \t}\r\n\t\r\n\t.add-update-preview .btn .btn1 {\r\n\t\t\t\tborder: 0;\r\n\t\t\t\tcursor: pointer;\r\n\t\t\t\tborder-radius: 4px;\r\n\t\t\t\tpadding: 0 24px;\r\n\t\t\t\tmargin: 4px;\r\n\t\t\t\toutline: none;\r\n\t\t\t\tcolor: #fff;\r\n\t\t\t\tbackground: #6574cd;\r\n\t\t\t\twidth: auto;\r\n\t\t\t\tfont-size: 14px;\r\n\t\t\t\theight: 40px;\r\n\t\t\t}\r\n\t\r\n\t.add-update-preview .btn .btn1:hover {\r\n\t\t\t\topacity: 0.8;\r\n\t\t\t}\r\n\t\r\n\t.add-update-preview .btn .btn2 {\r\n\t\t\t\tborder: 0;\r\n\t\t\t\tcursor: pointer;\r\n\t\t\t\tborder-radius: 4px;\r\n\t\t\t\tpadding: 0 24px;\r\n\t\t\t\tmargin: 4px;\r\n\t\t\t\toutline: none;\r\n\t\t\t\tcolor: #fff;\r\n\t\t\t\tbackground: #6574cd;\r\n\t\t\t\twidth: auto;\r\n\t\t\t\tfont-size: 14px;\r\n\t\t\t\theight: 40px;\r\n\t\t\t}\r\n\t\r\n\t.add-update-preview .btn .btn2:hover {\r\n\t\t\t\topacity: 0.8;\r\n\t\t\t}\r\n\t\r\n\t.add-update-preview .btn .btn3 {\r\n\t\t\t\tborder: 0;\r\n\t\t\t\tcursor: pointer;\r\n\t\t\t\tborder-radius: 4px;\r\n\t\t\t\tpadding: 0 24px;\r\n\t\t\t\tmargin: 4px;\r\n\t\t\t\toutline: none;\r\n\t\t\t\tcolor: #fff;\r\n\t\t\t\tbackground: #ff2b88;\r\n\t\t\t\twidth: auto;\r\n\t\t\t\tfont-size: 14px;\r\n\t\t\t\theight: 40px;\r\n\t\t\t}\r\n\t\r\n\t.add-update-preview .btn .btn3:hover {\r\n\t\t\t\topacity: 0.8;\r\n\t\t\t}\r\n\t\r\n\t.add-update-preview .btn .btn4 {\r\n\t\t\t\tborder: 0;\r\n\t\t\t\tcursor: pointer;\r\n\t\t\t\tborder-radius: 4px;\r\n\t\t\t\tpadding: 0 24px;\r\n\t\t\t\tmargin: 4px;\r\n\t\t\t\toutline: none;\r\n\t\t\t\tcolor: #fff;\r\n\t\t\t\tbackground: #ff2b88;\r\n\t\t\t\twidth: auto;\r\n\t\t\t\tfont-size: 14px;\r\n\t\t\t\theight: 40px;\r\n\t\t\t}\r\n\t\r\n\t.add-update-preview .btn .btn4:hover {\r\n\t\t\t\topacity: 0.8;\r\n\t\t\t}\r\n\t\r\n\t.add-update-preview .btn .btn5 {\r\n\t\t\t\tborder: 0;\r\n\t\t\t\tcursor: pointer;\r\n\t\t\t\tborder-radius: 4px;\r\n\t\t\t\tpadding: 0 24px;\r\n\t\t\t\tmargin: 4px;\r\n\t\t\t\toutline: none;\r\n\t\t\t\tcolor: #fff;\r\n\t\t\t\tbackground: #ff2b88;\r\n\t\t\t\twidth: auto;\r\n\t\t\t\tfont-size: 14px;\r\n\t\t\t\theight: 40px;\r\n\t\t\t}\r\n\t\r\n\t.add-update-preview .btn .btn5:hover {\r\n\t\t\t\topacity: 0.8;\r\n\t\t\t}\r\n</style>\r\n"]}]}