{"remainingRequest": "E:\\java系统\\公司财务系统\\src\\main\\resources\\admin\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\java系统\\公司财务系统\\src\\main\\resources\\admin\\admin\\src\\views\\register.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\java系统\\公司财务系统\\src\\main\\resources\\admin\\admin\\src\\views\\register.vue", "mtime": 1754805481715}, {"path": "E:\\java系统\\公司财务系统\\src\\main\\resources\\admin\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754805254356}, {"path": "E:\\java系统\\公司财务系统\\src\\main\\resources\\admin\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754805257322}, {"path": "E:\\java系统\\公司财务系统\\src\\main\\resources\\admin\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754805254356}, {"path": "E:\\java系统\\公司财务系统\\src\\main\\resources\\admin\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754628160828}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQpleHBvcnQgZGVmYXVsdCB7DQoJZGF0YSgpIHsNCgkJcmV0dXJuIHsNCgkJCXJ1bGVGb3JtOiB7DQoJCQl9LA0KCQkJZm9yZ2V0Rm9ybToge30sDQogICAgICAgICAgICBwYWdlRmxhZyA6ICcnLA0KCQkJdGFibGVOYW1lOiIiLA0KCQkJcnVsZXM6IHt9LA0KICAgICAgICAgICAgeXVhbmdvbmd4aW5nYmllT3B0aW9uczogW10sDQogICAgICAgICAgICB5dWFuZ29uZ2J1bWVuT3B0aW9uczogW10sDQogICAgICAgICAgICB5dWFuZ29uZ3poaXdlaU9wdGlvbnM6IFtdLA0KCQl9Ow0KCX0sDQoJbW91bnRlZCgpew0KCQl0aGlzLnBhZ2VGbGFnID0gdGhpcy4kcm91dGUucXVlcnkucGFnZUZsYWcNCgkJaWYodGhpcy4kcm91dGUucXVlcnkucGFnZUZsYWc9PSdyZWdpc3Rlcicpew0KCQkJDQoJCQlsZXQgdGFibGUgPSB0aGlzLiRzdG9yYWdlLmdldCgibG9naW5UYWJsZSIpOw0KCQkJdGhpcy50YWJsZU5hbWUgPSB0YWJsZTsNCgkJCWlmKHRoaXMudGFibGVOYW1lPT0neXVhbmdvbmcnKXsNCgkJCQl0aGlzLnJ1bGVGb3JtID0gew0KCQkJCQlnb25naGFvOiAnJywNCgkJCQkJbWltYTogJycsDQoJCQkJCXhpbmdtaW5nOiAnJywNCgkJCQkJdG91eGlhbmc6ICcnLA0KCQkJCQl4aW5nYmllOiAnJywNCgkJCQkJbGlhbnhpZGlhbmh1YTogJycsDQoJCQkJCWJ1bWVuOiAnJywNCgkJCQkJemhpd2VpOiAnJywNCgkJCQl9DQoJCQl9DQoJCQlpZiAoJ3l1YW5nb25nJyA9PSB0aGlzLnRhYmxlTmFtZSkgew0KCQkJCXRoaXMucnVsZXMuZ29uZ2hhbyA9IFt7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAn6K+36L6T5YWl5bel5Y+3JywgdHJpZ2dlcjogJ2JsdXInIH1dDQoJCQl9DQoJCQlpZiAoJ3l1YW5nb25nJyA9PSB0aGlzLnRhYmxlTmFtZSkgew0KCQkJCXRoaXMucnVsZXMubWltYSA9IFt7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAn6K+36L6T5YWl5a+G56CBJywgdHJpZ2dlcjogJ2JsdXInIH1dDQoJCQl9DQoJCQlpZiAoJ3l1YW5nb25nJyA9PSB0aGlzLnRhYmxlTmFtZSkgew0KCQkJCXRoaXMucnVsZXMueGluZ21pbmcgPSBbeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogJ+ivt+i+k+WFpeWnk+WQjScsIHRyaWdnZXI6ICdibHVyJyB9XQ0KCQkJfQ0KCQkJdGhpcy55dWFuZ29uZ3hpbmdiaWVPcHRpb25zID0gIueUtyzlpbMiLnNwbGl0KCcsJykNCgkJCXRoaXMuJGh0dHAoew0KCQkJCXVybDogYG9wdGlvbi9idW1lbi9idW1lbmAsDQoJCQkJbWV0aG9kOiAiZ2V0Ig0KCQkJfSkudGhlbigoeyBkYXRhIH0pID0+IHsNCgkJCQlpZiAoZGF0YSAmJiBkYXRhLmNvZGUgPT09IDApIHsNCgkJCQkJdGhpcy55dWFuZ29uZ2J1bWVuT3B0aW9ucyA9IGRhdGEuZGF0YTsNCgkJCQl9IGVsc2Ugew0KCQkJCQl0aGlzLiRtZXNzYWdlLmVycm9yKGRhdGEubXNnKTsNCgkJCQl9DQoJCQl9KTsNCgkJfQ0KCX0sDQoJY3JlYXRlZCgpIHsNCgl9LA0KCWRlc3Ryb3llZCgpIHsNCgkJICAJfSwNCgltZXRob2RzOiB7DQoJCWNoYW5nZVJ1bGVzKG5hbWUpew0KCQkJaWYodGhpcy5ydWxlc1tuYW1lXSl7DQoJCQkJcmV0dXJuIHRydWUNCgkJCX0NCgkJCXJldHVybiBmYWxzZQ0KCQl9LA0KCQkvLyDojrflj5Z1dWlkDQoJCWdldFVVSUQgKCkgew0KCQkJcmV0dXJuIG5ldyBEYXRlKCkuZ2V0VGltZSgpOw0KCQl9LA0KCQljbG9zZSgpew0KCQkJdGhpcy4kcm91dGVyLnB1c2goeyBwYXRoOiAiL2xvZ2luIiB9KTsNCgkJfSwNCiAgICAgICAgeXVhbmdvbmd0b3V4aWFuZ1VwbG9hZENoYW5nZShmaWxlVXJscykgew0KICAgICAgICAgICAgdGhpcy5ydWxlRm9ybS50b3V4aWFuZyA9IGZpbGVVcmxzOw0KICAgICAgICB9LA0KDQogICAgICAgIC8vIOWkmue6p+iBlOWKqOWPguaVsA0KICAgICAgICB5dWFuZ29uZ2NoYW5nZTEoZSxjb25kaXRpb25Db2x1bW4pew0KICAgICAgICAgICAgdGhpcy5ydWxlRm9ybS56aGl3ZWkgPSAnJzsNCiAgICAgICAgICAgIHRoaXMueXVhbmdvbmd6aGl3ZWlPcHRpb25zID0gW107DQogICAgICAgICAgICB0aGlzLiRodHRwKHsNCiAgICAgICAgICAgICAgICB1cmw6IGBvcHRpb24vemhpd2VpL3poaXdlaT9jb25kaXRpb25Db2x1bW49JHtjb25kaXRpb25Db2x1bW59JmNvbmRpdGlvblZhbHVlPSR7ZX1gLA0KICAgICAgICAgICAgICAgIG1ldGhvZDogImdldCINCiAgICAgICAgICAgIH0pLnRoZW4oKHsgZGF0YSB9KSA9PiB7DQogICAgICAgICAgICAgICAgaWYgKGRhdGEgJiYgZGF0YS5jb2RlID09PSAwKSB7DQogICAgICAgICAgICAgICAgICAgIHRoaXMueXVhbmdvbmd6aGl3ZWlPcHRpb25zID0gZGF0YS5kYXRhOw0KICAgICAgICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoZGF0YS5tc2cpOw0KICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgIH0pOw0KICAgICAgICB9LA0KDQoNCgkJLy8g5rOo5YaMDQoJCWxvZ2luKCkgew0KCQkJdmFyIHVybD10aGlzLnRhYmxlTmFtZSsiL3JlZ2lzdGVyIjsNCgkJCQkJaWYoKCF0aGlzLnJ1bGVGb3JtLmdvbmdoYW8pICYmIGB5dWFuZ29uZ2AgPT0gdGhpcy50YWJsZU5hbWUpew0KCQkJCQkJdGhpcy4kbWVzc2FnZS5lcnJvcihg5bel5Y+35LiN6IO95Li656m6YCk7DQoJCQkJCQlyZXR1cm4NCgkJCQkJfQ0KCQkJCQlpZigoIXRoaXMucnVsZUZvcm0ubWltYSkgJiYgYHl1YW5nb25nYCA9PSB0aGlzLnRhYmxlTmFtZSl7DQoJCQkJCQl0aGlzLiRtZXNzYWdlLmVycm9yKGDlr4bnoIHkuI3og73kuLrnqbpgKTsNCgkJCQkJCXJldHVybg0KCQkJCQl9DQoJCQkJCWlmKCh0aGlzLnJ1bGVGb3JtLm1pbWEhPXRoaXMucnVsZUZvcm0ubWltYTIpICYmIGB5dWFuZ29uZ2AgPT0gdGhpcy50YWJsZU5hbWUpew0KCQkJCQkJdGhpcy4kbWVzc2FnZS5lcnJvcihg5Lik5qyh5a+G56CB6L6T5YWl5LiN5LiA6Ie0YCk7DQoJCQkJCQlyZXR1cm4NCgkJCQkJfQ0KCQkJCQlpZigoIXRoaXMucnVsZUZvcm0ueGluZ21pbmcpICYmIGB5dWFuZ29uZ2AgPT0gdGhpcy50YWJsZU5hbWUpew0KCQkJCQkJdGhpcy4kbWVzc2FnZS5lcnJvcihg5aeT5ZCN5LiN6IO95Li656m6YCk7DQoJCQkJCQlyZXR1cm4NCgkJCQkJfQ0KICAgICAgICAgICAgaWYodGhpcy5ydWxlRm9ybS50b3V4aWFuZyE9bnVsbCkgew0KICAgICAgICAgICAgICAgIHRoaXMucnVsZUZvcm0udG91eGlhbmcgPSB0aGlzLnJ1bGVGb3JtLnRvdXhpYW5nLnJlcGxhY2UobmV3IFJlZ0V4cCh0aGlzLiRiYXNlLnVybCwiZyIpLCIiKTsNCiAgICAgICAgICAgIH0NCgkJCQkJLy8g5bey56e76Zmk5ZGY5bel5rOo5YaM5pe26IGU57O755S16K+d55qE5omL5py65qC85byP6aqM6K+B77yM5YWB6K645Lu75oSP5qC85byP6L6T5YWlDQoJCQkJCS8vIGlmKGB5dWFuZ29uZ2AgPT0gdGhpcy50YWJsZU5hbWUgJiYgdGhpcy5ydWxlRm9ybS5saWFueGlkaWFuaHVhICYmKCF0aGlzLiR2YWxpZGF0ZS5pc01vYmlsZSh0aGlzLnJ1bGVGb3JtLmxpYW54aWRpYW5odWEpKSl7DQoJCQkJCS8vCXRoaXMuJG1lc3NhZ2UuZXJyb3IoYOiBlOezu+eUteivneW6lOi+k+WFpeaJi+acuuagvOW8j2ApOw0KCQkJCQkvLwlyZXR1cm4NCgkJCQkJLy8gfQ0KCQkJdGhpcy4kaHR0cCh7DQoJCQkJdXJsOiB1cmwsDQoJCQkJbWV0aG9kOiAicG9zdCIsDQoJCQkJZGF0YTp0aGlzLnJ1bGVGb3JtDQoJCQl9KS50aGVuKCh7IGRhdGEgfSkgPT4gew0KCQkJCWlmIChkYXRhICYmIGRhdGEuY29kZSA9PT0gMCkgew0KCQkJCQl0aGlzLiRtZXNzYWdlKHsNCgkJCQkJCW1lc3NhZ2U6ICLms6jlhozmiJDlip8iLA0KCQkJCQkJdHlwZTogInN1Y2Nlc3MiLA0KCQkJCQkJZHVyYXRpb246IDE1MDAsDQoJCQkJCQlvbkNsb3NlOiAoKSA9PiB7DQoJCQkJCQkJdGhpcy4kcm91dGVyLnJlcGxhY2UoeyBwYXRoOiAiL2xvZ2luIiB9KTsNCgkJCQkJCX0NCgkJCQkJfSk7DQoJCQkJfSBlbHNlIHsNCgkJCQkJdGhpcy4kbWVzc2FnZS5lcnJvcihkYXRhLm1zZyk7DQoJCQkJfQ0KCQkJfSk7DQoJCX0NCgl9DQp9Ow0K"}, {"version": 3, "sources": ["register.vue"], "names": [], "mappings": ";AAwDA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "register.vue", "sourceRoot": "src/views", "sourcesContent": ["<template>\r\n\t<div>\r\n\t\t<div class=\"container\" :style='{\"minHeight\":\"100vh\",\"padding\":\"0px 180px 0px 0px\",\"alignItems\":\"center\",\"background\":\"url(http://codegen.caihongy.cn/20240129/17e5d014970b46b0a6da2629973d8845.png) no-repeat\",\"display\":\"flex\",\"width\":\"100%\",\"backgroundSize\":\"cover\",\"justifyContent\":\"flex-end\"}'>\r\n\t\t\t<el-form v-if=\"pageFlag=='register'\" :style='{\"padding\":\"20px\",\"boxShadow\":\"0 1px 20px rgba( 255,  255, 255, .8)\",\"margin\":\"20px 0\",\"borderRadius\":\"4px\",\"background\":\"#fff\",\"width\":\"400px\",\"height\":\"auto\"}' ref=\"rgsForm\" class=\"rgs-form\" :model=\"rgsForm\" :rules=\"rules\">\r\n\t\t\t\t<div v-if=\"true\" :style='{\"width\":\"100%\",\"margin\":\"0 0 10px 0\",\"lineHeight\":\"44px\",\"fontSize\":\"20px\",\"color\":\"#374254\",\"textAlign\":\"center\"}' class=\"title\">公司财务管理系统注册</div>\r\n\t\t\t\t<el-form-item :style='{\"width\":\"80%\",\"padding\":\"0\",\"margin\":\"0 auto 15px\",\"height\":\"auto\"}' class=\"list-item\" v-if=\"tableName=='yuangong'\">\r\n\t\t\t\t\t<div v-if=\"false\" :style='{\"width\":\"64px\",\"lineHeight\":\"44px\",\"fontSize\":\"14px\",\"position\":\"relative\",\"color\":\"rgba(64, 158, 255, 1)\"}' class=\"lable\" :class=\"changeRules('gonghao')?'required':''\">工号</div>\r\n\t\t\t\t\t<el-input  v-model=\"ruleForm.gonghao\"  autocomplete=\"off\" placeholder=\"工号\"  type=\"text\"  />\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<el-form-item :style='{\"width\":\"80%\",\"padding\":\"0\",\"margin\":\"0 auto 15px\",\"height\":\"auto\"}' class=\"list-item\" v-if=\"tableName=='yuangong'\">\r\n\t\t\t\t\t<div v-if=\"false\" :style='{\"width\":\"64px\",\"lineHeight\":\"44px\",\"fontSize\":\"14px\",\"position\":\"relative\",\"color\":\"rgba(64, 158, 255, 1)\"}' class=\"lable\" :class=\"changeRules('mima')?'required':''\">密码</div>\r\n\t\t\t\t\t<el-input  v-model=\"ruleForm.mima\"  autocomplete=\"off\" placeholder=\"密码\"  type=\"password\"  />\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<el-form-item :style='{\"width\":\"80%\",\"padding\":\"0\",\"margin\":\"0 auto 15px\",\"height\":\"auto\"}' class=\"list-item\" v-if=\"tableName=='yuangong'\">\r\n\t\t\t\t\t<div v-if=\"false\" :style='{\"width\":\"64px\",\"lineHeight\":\"44px\",\"fontSize\":\"14px\",\"position\":\"relative\",\"color\":\"rgba(64, 158, 255, 1)\"}' class=\"lable\" :class=\"changeRules('mima')?'required':''\">确认密码</div>\r\n\t\t\t\t\t<el-input  v-model=\"ruleForm.mima2\" autocomplete=\"off\" placeholder=\"确认密码\" type=\"password\" />\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<el-form-item :style='{\"width\":\"80%\",\"padding\":\"0\",\"margin\":\"0 auto 15px\",\"height\":\"auto\"}' class=\"list-item\" v-if=\"tableName=='yuangong'\">\r\n\t\t\t\t\t<div v-if=\"false\" :style='{\"width\":\"64px\",\"lineHeight\":\"44px\",\"fontSize\":\"14px\",\"position\":\"relative\",\"color\":\"rgba(64, 158, 255, 1)\"}' class=\"lable\" :class=\"changeRules('xingming')?'required':''\">姓名</div>\r\n\t\t\t\t\t<el-input  v-model=\"ruleForm.xingming\"  autocomplete=\"off\" placeholder=\"姓名\"  type=\"text\"  />\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<el-form-item :style='{\"width\":\"80%\",\"padding\":\"0\",\"margin\":\"0 auto 15px\",\"height\":\"auto\"}' class=\"list-item\" v-if=\"tableName=='yuangong'\">\r\n\t\t\t\t\t<div v-if=\"false\" :style='{\"width\":\"64px\",\"lineHeight\":\"44px\",\"fontSize\":\"14px\",\"position\":\"relative\",\"color\":\"rgba(64, 158, 255, 1)\"}' class=\"lable\" :class=\"changeRules('touxiang')?'required':''\">头像�?/div>\r\n                    <file-upload\r\n                        tip=\"点击上传头像\"\r\n                        action=\"file/upload\"\r\n                        :limit=\"3\"\r\n                        :multiple=\"true\"\r\n                        :fileUrls=\"ruleForm.touxiang?ruleForm.touxiang:''\"\r\n                        @change=\"yuangongtouxiangUploadChange\"\r\n                    ></file-upload>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<el-form-item :style='{\"width\":\"80%\",\"padding\":\"0\",\"margin\":\"0 auto 15px\",\"height\":\"auto\"}' class=\"list-item\" v-if=\"tableName=='yuangong'\">\r\n\t\t\t\t\t<div v-if=\"false\" :style='{\"width\":\"64px\",\"lineHeight\":\"44px\",\"fontSize\":\"14px\",\"position\":\"relative\",\"color\":\"rgba(64, 158, 255, 1)\"}' class=\"lable\" :class=\"changeRules('xingbie')?'required':''\">性别�?/div>\r\n                    <el-select v-model=\"ruleForm.xingbie\" placeholder=\"请选择性别\" >\r\n                        <el-option\r\n                            v-for=\"(item,index) in yuangongxingbieOptions\"\r\n                            v-bind:key=\"index\"\r\n                            :label=\"item\"\r\n                            :value=\"item\">\r\n                        </el-option>\r\n                    </el-select>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<el-form-item :style='{\"width\":\"80%\",\"padding\":\"0\",\"margin\":\"0 auto 15px\",\"height\":\"auto\"}' class=\"list-item\" v-if=\"tableName=='yuangong'\">\r\n\t\t\t\t\t<div v-if=\"false\" :style='{\"width\":\"64px\",\"lineHeight\":\"44px\",\"fontSize\":\"14px\",\"position\":\"relative\",\"color\":\"rgba(64, 158, 255, 1)\"}' class=\"lable\" :class=\"changeRules('lianxidianhua')?'required':''\">联系电话�?/div>\r\n\t\t\t\t\t<el-input  v-model=\"ruleForm.lianxidianhua\"  autocomplete=\"off\" placeholder=\"联系电话\"  type=\"text\"  />\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<button :style='{\"border\":\"0\",\"cursor\":\"pointer\",\"padding\":\"0 10px\",\"margin\":\"20px auto 5px\",\"outline\":\"none\",\"color\":\"#fff\",\"borderRadius\":\"4px\",\"background\":\"#dc4e41\",\"display\":\"block\",\"width\":\"80%\",\"fontSize\":\"16px\",\"height\":\"44px\"}' type=\"button\" class=\"r-btn\" @click=\"login()\">注册</button>\r\n\t\t\t\t<div :style='{\"cursor\":\"pointer\",\"padding\":\"0 10%\",\"color\":\"rgba(159, 159, 159, 1)\",\"display\":\"inline-block\",\"lineHeight\":\"1\",\"fontSize\":\"12px\",\"textDecoration\":\"underline\"}' class=\"r-login\" @click=\"close()\">已有账号，直接登�?/div>\r\n\t\t\t</el-form>\r\n\t\t\t\r\n\t\t</div>\r\n\t</div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\truleForm: {\r\n\t\t\t},\r\n\t\t\tforgetForm: {},\r\n            pageFlag : '',\r\n\t\t\ttableName:\"\",\r\n\t\t\trules: {},\r\n            yuangongxingbieOptions: [],\r\n            yuangongbumenOptions: [],\r\n            yuangongzhiweiOptions: [],\r\n\t\t};\r\n\t},\r\n\tmounted(){\r\n\t\tthis.pageFlag = this.$route.query.pageFlag\r\n\t\tif(this.$route.query.pageFlag=='register'){\r\n\t\t\t\r\n\t\t\tlet table = this.$storage.get(\"loginTable\");\r\n\t\t\tthis.tableName = table;\r\n\t\t\tif(this.tableName=='yuangong'){\r\n\t\t\t\tthis.ruleForm = {\r\n\t\t\t\t\tgonghao: '',\r\n\t\t\t\t\tmima: '',\r\n\t\t\t\t\txingming: '',\r\n\t\t\t\t\ttouxiang: '',\r\n\t\t\t\t\txingbie: '',\r\n\t\t\t\t\tlianxidianhua: '',\r\n\t\t\t\t\tbumen: '',\r\n\t\t\t\t\tzhiwei: '',\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\tif ('yuangong' == this.tableName) {\r\n\t\t\t\tthis.rules.gonghao = [{ required: true, message: '请输入工号', trigger: 'blur' }]\r\n\t\t\t}\r\n\t\t\tif ('yuangong' == this.tableName) {\r\n\t\t\t\tthis.rules.mima = [{ required: true, message: '请输入密码', trigger: 'blur' }]\r\n\t\t\t}\r\n\t\t\tif ('yuangong' == this.tableName) {\r\n\t\t\t\tthis.rules.xingming = [{ required: true, message: '请输入姓名', trigger: 'blur' }]\r\n\t\t\t}\r\n\t\t\tthis.yuangongxingbieOptions = \"男,女\".split(',')\r\n\t\t\tthis.$http({\r\n\t\t\t\turl: `option/bumen/bumen`,\r\n\t\t\t\tmethod: \"get\"\r\n\t\t\t}).then(({ data }) => {\r\n\t\t\t\tif (data && data.code === 0) {\r\n\t\t\t\t\tthis.yuangongbumenOptions = data.data;\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.$message.error(data.msg);\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t}\r\n\t},\r\n\tcreated() {\r\n\t},\r\n\tdestroyed() {\r\n\t\t  \t},\r\n\tmethods: {\r\n\t\tchangeRules(name){\r\n\t\t\tif(this.rules[name]){\r\n\t\t\t\treturn true\r\n\t\t\t}\r\n\t\t\treturn false\r\n\t\t},\r\n\t\t// 获取uuid\r\n\t\tgetUUID () {\r\n\t\t\treturn new Date().getTime();\r\n\t\t},\r\n\t\tclose(){\r\n\t\t\tthis.$router.push({ path: \"/login\" });\r\n\t\t},\r\n        yuangongtouxiangUploadChange(fileUrls) {\r\n            this.ruleForm.touxiang = fileUrls;\r\n        },\r\n\r\n        // 多级联动参数\r\n        yuangongchange1(e,conditionColumn){\r\n            this.ruleForm.zhiwei = '';\r\n            this.yuangongzhiweiOptions = [];\r\n            this.$http({\r\n                url: `option/zhiwei/zhiwei?conditionColumn=${conditionColumn}&conditionValue=${e}`,\r\n                method: \"get\"\r\n            }).then(({ data }) => {\r\n                if (data && data.code === 0) {\r\n                    this.yuangongzhiweiOptions = data.data;\r\n                } else {\r\n                    this.$message.error(data.msg);\r\n                }\r\n            });\r\n        },\r\n\r\n\r\n\t\t// 注册\r\n\t\tlogin() {\r\n\t\t\tvar url=this.tableName+\"/register\";\r\n\t\t\t\t\tif((!this.ruleForm.gonghao) && `yuangong` == this.tableName){\r\n\t\t\t\t\t\tthis.$message.error(`工号不能为空`);\r\n\t\t\t\t\t\treturn\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif((!this.ruleForm.mima) && `yuangong` == this.tableName){\r\n\t\t\t\t\t\tthis.$message.error(`密码不能为空`);\r\n\t\t\t\t\t\treturn\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif((this.ruleForm.mima!=this.ruleForm.mima2) && `yuangong` == this.tableName){\r\n\t\t\t\t\t\tthis.$message.error(`两次密码输入不一致`);\r\n\t\t\t\t\t\treturn\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif((!this.ruleForm.xingming) && `yuangong` == this.tableName){\r\n\t\t\t\t\t\tthis.$message.error(`姓名不能为空`);\r\n\t\t\t\t\t\treturn\r\n\t\t\t\t\t}\r\n            if(this.ruleForm.touxiang!=null) {\r\n                this.ruleForm.touxiang = this.ruleForm.touxiang.replace(new RegExp(this.$base.url,\"g\"),\"\");\r\n            }\r\n\t\t\t\t\t// 已移除员工注册时联系电话的手机格式验证，允许任意格式输入\r\n\t\t\t\t\t// if(`yuangong` == this.tableName && this.ruleForm.lianxidianhua &&(!this.$validate.isMobile(this.ruleForm.lianxidianhua))){\r\n\t\t\t\t\t//\tthis.$message.error(`联系电话应输入手机格式`);\r\n\t\t\t\t\t//\treturn\r\n\t\t\t\t\t// }\r\n\t\t\tthis.$http({\r\n\t\t\t\turl: url,\r\n\t\t\t\tmethod: \"post\",\r\n\t\t\t\tdata:this.ruleForm\r\n\t\t\t}).then(({ data }) => {\r\n\t\t\t\tif (data && data.code === 0) {\r\n\t\t\t\t\tthis.$message({\r\n\t\t\t\t\t\tmessage: \"注册成功\",\r\n\t\t\t\t\t\ttype: \"success\",\r\n\t\t\t\t\t\tduration: 1500,\r\n\t\t\t\t\t\tonClose: () => {\r\n\t\t\t\t\t\t\tthis.$router.replace({ path: \"/login\" });\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t});\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.$message.error(data.msg);\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t}\r\n\t}\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n\t.container {\r\n\t  position: relative;\r\n\t  background: url(http://codegen.caihongy.cn/20240129/17e5d014970b46b0a6da2629973d8845.png) no-repeat;\r\n\r\n\t\t.el-date-editor.el-input {\r\n\t\t  width: 100%;\r\n\t\t}\r\n\t\t\r\n\t\t.rgs-form .el-input :deep .el-input__inner {\r\n\t\t\t\t\t\tborder: 1px solid rgba(167, 180, 201,.3)    ;\r\n\t\t\t\t\t\tborder-radius: 0;\r\n\t\t\t\t\t\tpadding: 0 10px;\r\n\t\t\t\t\t\toutline: none;\r\n\t\t\t\t\t\tcolor: #a7b4c9 ;\r\n\t\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\t\tfont-size: 14px;\r\n\t\t\t\t\t\theight: 44px;\r\n\t\t\t\t\t}\r\n\t\t\r\n\t\t.rgs-form .el-select :deep .el-input__inner {\r\n\t\t\t\t\t\tborder: 1px solid rgba(167, 180, 201,.3)    ;\r\n\t\t\t\t\t\tborder-radius: 0;\r\n\t\t\t\t\t\tpadding: 0 10px;\r\n\t\t\t\t\t\toutline: none;\r\n\t\t\t\t\t\tcolor: #a7b4c9 ;\r\n\t\t\t\t\t\twidth: 288px;\r\n\t\t\t\t\t\tfont-size: 14px;\r\n\t\t\t\t\t\theight: 44px;\r\n\t\t\t\t\t}\r\n\t\t\r\n\t\t.rgs-form .el-date-editor :deep .el-input__inner {\r\n\t\t\t\t\t\tborder: 1px solid rgba(167, 180, 201,.3)    ;\r\n\t\t\t\t\t\tborder-radius: 0;\r\n\t\t\t\t\t\tpadding: 0 10px 0 30px;\r\n\t\t\t\t\t\toutline: none;\r\n\t\t\t\t\t\tcolor: #a7b4c9 ;\r\n\t\t\t\t\t\twidth: 288px;\r\n\t\t\t\t\t\tfont-size: 14px;\r\n\t\t\t\t\t\theight: 44px;\r\n\t\t\t\t\t}\r\n\t\t\r\n\t\t.rgs-form .el-date-editor :deep .el-input__inner {\r\n\t\t\t\t\t\tborder: 1px solid rgba(167, 180, 201,.3)    ;\r\n\t\t\t\t\t\tborder-radius: 0;\r\n\t\t\t\t\t\tpadding: 0 10px 0 30px;\r\n\t\t\t\t\t\toutline: none;\r\n\t\t\t\t\t\tcolor: #a7b4c9 ;\r\n\t\t\t\t\t\twidth: 288px;\r\n\t\t\t\t\t\tfont-size: 14px;\r\n\t\t\t\t\t\theight: 44px;\r\n\t\t\t\t\t}\r\n\t\t\r\n\t\t.rgs-form :deep .el-upload--picture-card {\r\n\t\t\tbackground: transparent;\r\n\t\t\tborder: 0;\r\n\t\t\tborder-radius: 0;\r\n\t\t\twidth: auto;\r\n\t\t\theight: auto;\r\n\t\t\tline-height: initial;\r\n\t\t\tvertical-align: middle;\r\n\t\t}\r\n\t\t\r\n\t\t.rgs-form :deep .upload .upload-img {\r\n\t\t  \t\t  border: 1px dashed  rgba(167, 180, 201,.3)    ;\r\n\t\t  \t\t  cursor: pointer;\r\n\t\t  \t\t  border-radius: 8px;\r\n\t\t  \t\t  color: rgba(167, 180, 201,.3);\r\n\t\t  \t\t  width: 160px;\r\n\t\t  \t\t  font-size: 32px;\r\n\t\t  \t\t  line-height: 160px;\r\n\t\t  \t\t  text-align: center;\r\n\t\t  \t\t  height: 160px;\r\n\t\t  \t\t}\r\n\t\t\r\n\t\t.rgs-form :deep .el-upload-list .el-upload-list__item {\r\n\t\t  \t\t  border: 1px dashed  rgba(167, 180, 201,.3)    ;\r\n\t\t  \t\t  cursor: pointer;\r\n\t\t  \t\t  border-radius: 8px;\r\n\t\t  \t\t  color: rgba(167, 180, 201,.3);\r\n\t\t  \t\t  width: 160px;\r\n\t\t  \t\t  font-size: 32px;\r\n\t\t  \t\t  line-height: 160px;\r\n\t\t  \t\t  text-align: center;\r\n\t\t  \t\t  height: 160px;\r\n\t\t  \t\t}\r\n\t\t\r\n\t\t.rgs-form :deep .el-upload .el-icon-plus {\r\n\t\t  \t\t  border: 1px dashed  rgba(167, 180, 201,.3)    ;\r\n\t\t  \t\t  cursor: pointer;\r\n\t\t  \t\t  border-radius: 8px;\r\n\t\t  \t\t  color: rgba(167, 180, 201,.3);\r\n\t\t  \t\t  width: 160px;\r\n\t\t  \t\t  font-size: 32px;\r\n\t\t  \t\t  line-height: 160px;\r\n\t\t  \t\t  text-align: center;\r\n\t\t  \t\t  height: 160px;\r\n\t\t  \t\t}\r\n\t}\r\n\t.required {\r\n\t\tposition: relative;\r\n\t}\r\n\t.required::after{\r\n\t\t\t\tcolor: red;\r\n\t\t\t\tleft: -10px;\r\n\t\t\t\tposition: absolute;\r\n\t\t\t\tcontent: \"*\";\r\n\t\t\t}\r\n\t.editor>.avatar-uploader {\r\n\t\tline-height: 0;\r\n\t\theight: 0;\r\n\t}\r\n</style>\r\n"]}]}