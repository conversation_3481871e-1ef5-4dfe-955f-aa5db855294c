{"remainingRequest": "E:\\java系统\\公司财务系统\\src\\main\\resources\\admin\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\java系统\\公司财务系统\\src\\main\\resources\\admin\\admin\\src\\views\\modules\\zichancaigou\\add-or-update.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\java系统\\公司财务系统\\src\\main\\resources\\admin\\admin\\src\\views\\modules\\zichancaigou\\add-or-update.vue", "mtime": 1754635553255}, {"path": "E:\\java系统\\公司财务系统\\src\\main\\resources\\admin\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754805254356}, {"path": "E:\\java系统\\公司财务系统\\src\\main\\resources\\admin\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754805257322}, {"path": "E:\\java系统\\公司财务系统\\src\\main\\resources\\admin\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754805254356}, {"path": "E:\\java系统\\公司财务系统\\src\\main\\resources\\admin\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754628160828}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["add-or-update.vue"], "names": [], "mappings": ";AAuHA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;AAOA;AACA;;AAEA;;;;;;;;AAQA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;;;;;AAKA;AACA;AACA;;;;;;;;;;;;AAYA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "add-or-update.vue", "sourceRoot": "src/views/modules/zichancaigou", "sourcesContent": ["<template>\r\n\t<div class=\"addEdit-block\" :style='{\"padding\":\"30px\",\"margin\":\"0\"}'>\r\n\t\t<el-form\r\n\t\t\t:style='{\"border\":\"1px solid rgba(167, 180, 201,.3)  \",\"padding\":\"30px\",\"borderRadius\":\"6px\",\"background\":\"#fff\"}'\r\n\t\t\tclass=\"add-update-preview\"\r\n\t\t\tref=\"ruleForm\"\r\n\t\t\t:model=\"ruleForm\"\r\n\t\t\t:rules=\"rules\"\r\n\t\t\tlabel-width=\"80px\"\r\n\t\t>\r\n\t\t\t<template >\r\n\t\t\t\t<el-form-item :style='{\"margin\":\"0 0 20px 0\"}' class=\"input\" v-if=\"type!='info'\"  label=\"资产编码\" prop=\"zichanbianma\">\r\n\t\t\t\t\t<el-input v-model=\"ruleForm.zichanbianma\" placeholder=\"资产编码\" clearable  :readonly=\"ro.zichanbianma\"></el-input>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<el-form-item :style='{\"margin\":\"0 0 20px 0\"}' v-else class=\"input\" label=\"资产编码\" prop=\"zichanbianma\">\r\n\t\t\t\t\t<el-input v-model=\"ruleForm.zichanbianma\" placeholder=\"资产编码\" readonly></el-input>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<el-form-item :style='{\"margin\":\"0 0 20px 0\"}' class=\"input\" v-if=\"type!='info'\"  label=\"资产名称\" prop=\"zichanmingcheng\">\r\n\t\t\t\t\t<el-input v-model=\"ruleForm.zichanmingcheng\" placeholder=\"资产名称\" clearable  :readonly=\"ro.zichanmingcheng\"></el-input>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<el-form-item :style='{\"margin\":\"0 0 20px 0\"}' v-else class=\"input\" label=\"资产名称\" prop=\"zichanmingcheng\">\r\n\t\t\t\t\t<el-input v-model=\"ruleForm.zichanmingcheng\" placeholder=\"资产名称\" readonly></el-input>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<el-form-item :style='{\"margin\":\"0 0 20px 0\"}' class=\"input\" v-if=\"type!='info'\"  label=\"资产类型\" prop=\"zichanleixing\">\r\n\t\t\t\t\t<el-input v-model=\"ruleForm.zichanleixing\" placeholder=\"资产类型\" clearable  :readonly=\"ro.zichanleixing\"></el-input>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<el-form-item :style='{\"margin\":\"0 0 20px 0\"}' v-else class=\"input\" label=\"资产类型\" prop=\"zichanleixing\">\r\n\t\t\t\t\t<el-input v-model=\"ruleForm.zichanleixing\" placeholder=\"资产类型\" readonly></el-input>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<el-form-item :style='{\"margin\":\"0 0 20px 0\"}' class=\"upload\" v-if=\"type!='info' && !ro.zichantupian\" label=\"资产图片\" prop=\"zichantupian\">\r\n\t\t\t\t\t<file-upload\r\n\t\t\t\t\t\ttip=\"点击上传资产图片\"\r\n\t\t\t\t\t\taction=\"file/upload\"\r\n\t\t\t\t\t\t:limit=\"3\"\r\n\t\t\t\t\t\t:multiple=\"true\"\r\n\t\t\t\t\t\t:fileUrls=\"ruleForm.zichantupian?ruleForm.zichantupian:''\"\r\n\t\t\t\t\t\t@change=\"zichantupianUploadChange\"\r\n\t\t\t\t\t></file-upload>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<el-form-item :style='{\"margin\":\"0 0 20px 0\"}' class=\"upload\" v-else-if=\"ruleForm.zichantupian\" label=\"资产图片\" prop=\"zichantupian\">\r\n\t\t\t\t\t<img v-if=\"ruleForm.zichantupian.substring(0,4)=='http'\" class=\"upload-img\" style=\"margin-right:20px;\" v-bind:key=\"index\" :src=\"ruleForm.zichantupian.split(',')[0]\" width=\"100\" height=\"100\">\r\n\t\t\t\t\t<img v-else class=\"upload-img\" style=\"margin-right:20px;\" v-bind:key=\"index\" v-for=\"(item,index) in ruleForm.zichantupian.split(',')\" :src=\"$base.url+item\" width=\"100\" height=\"100\">\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<el-form-item :style='{\"margin\":\"0 0 20px 0\"}' class=\"input\" v-if=\"type!='info'\"  label=\"资产单价\" prop=\"zichandanjia\">\r\n\t\t\t\t\t<el-input v-model.number=\"ruleForm.zichandanjia\" placeholder=\"资产单价\" clearable  :readonly=\"ro.zichandanjia\"></el-input>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<el-form-item :style='{\"margin\":\"0 0 20px 0\"}' v-else class=\"input\" label=\"资产单价\" prop=\"zichandanjia\">\r\n\t\t\t\t\t<el-input v-model=\"ruleForm.zichandanjia\" placeholder=\"资产单价\" readonly></el-input>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<el-form-item :style='{\"margin\":\"0 0 20px 0\"}' class=\"input\" v-if=\"type!='info'\"  label=\"采购数量\" prop=\"zichanshuliang\">\r\n\t\t\t\t\t<el-input v-model.number=\"ruleForm.zichanshuliang\" placeholder=\"采购数量\" clearable  :readonly=\"ro.zichanshuliang\"></el-input>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<el-form-item :style='{\"margin\":\"0 0 20px 0\"}' v-else class=\"input\" label=\"采购数量\" prop=\"zichanshuliang\">\r\n\t\t\t\t\t<el-input v-model=\"ruleForm.zichanshuliang\" placeholder=\"采购数量\" readonly></el-input>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<el-form-item :style='{\"margin\":\"0 0 20px 0\"}' class=\"input\" v-if=\"type!='info'\"  label=\"采购总价\" prop=\"zichanzongjia\">\r\n\t\t\t\t\t<el-input v-model=\"zichanzongjia\" placeholder=\"采购总价\" readonly></el-input>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<el-form-item :style='{\"margin\":\"0 0 20px 0\"}' class=\"input\" v-else-if=\"ruleForm.zichanzongjia\" label=\"采购总价\" prop=\"zichanzongjia\">\r\n\t\t\t\t\t<el-input v-model=\"ruleForm.zichanzongjia\" placeholder=\"采购总价\" readonly></el-input>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<el-form-item :style='{\"margin\":\"0 0 20px 0\"}' class=\"date\" v-if=\"type!='info'\" label=\"入库时间\" prop=\"rukushijian\">\r\n\t\t\t\t\t<el-date-picker\r\n\t\t\t\t\t\tformat=\"yyyy �?MM �?dd �?\r\n\t\t\t\t\t\tvalue-format=\"yyyy-MM-dd\"\r\n\t\t\t\t\t\tv-model=\"ruleForm.rukushijian\" \r\n\t\t\t\t\t\ttype=\"date\"\r\n\t\t\t\t\t\t:readonly=\"ro.rukushijian\"\r\n\t\t\t\t\t\tplaceholder=\"入库时间\"\r\n\t\t\t\t\t></el-date-picker> \r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<el-form-item :style='{\"margin\":\"0 0 20px 0\"}' class=\"input\" v-else-if=\"ruleForm.rukushijian\" label=\"入库时间\" prop=\"rukushijian\">\r\n\t\t\t\t\t<el-input v-model=\"ruleForm.rukushijian\" placeholder=\"入库时间\" readonly></el-input>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<el-form-item :style='{\"margin\":\"0 0 20px 0\"}' class=\"input\" v-if=\"type!='info'\"  label=\"工号\" prop=\"gonghao\">\r\n\t\t\t\t\t<el-input v-model=\"ruleForm.gonghao\" placeholder=\"工号\" clearable  :readonly=\"ro.gonghao\"></el-input>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<el-form-item :style='{\"margin\":\"0 0 20px 0\"}' v-else class=\"input\" label=\"工号\" prop=\"gonghao\">\r\n\t\t\t\t\t<el-input v-model=\"ruleForm.gonghao\" placeholder=\"工号\" readonly></el-input>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<el-form-item :style='{\"margin\":\"0 0 20px 0\"}' class=\"input\" v-if=\"type!='info'\"  label=\"姓名\" prop=\"xingming\">\r\n\t\t\t\t\t<el-input v-model=\"ruleForm.xingming\" placeholder=\"姓名\" clearable  :readonly=\"ro.xingming\"></el-input>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<el-form-item :style='{\"margin\":\"0 0 20px 0\"}' v-else class=\"input\" label=\"姓名\" prop=\"xingming\">\r\n\t\t\t\t\t<el-input v-model=\"ruleForm.xingming\" placeholder=\"姓名\" readonly></el-input>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t</template>\r\n\t\t\t\t<el-form-item :style='{\"margin\":\"0 0 20px 0\"}' class=\"textarea\" v-if=\"type!='info'\" label=\"入库原因\" prop=\"rukuyuanyin\">\r\n\t\t\t\t\t<el-input\r\n\t\t\t\t\t  style=\"min-width: 200px; max-width: 600px;\"\r\n\t\t\t\t\t  type=\"textarea\"\r\n\t\t\t\t\t  :rows=\"8\"\r\n\t\t\t\t\t  placeholder=\"入库原因\"\r\n\t\t\t\t\t  v-model=\"ruleForm.rukuyuanyin\" >\r\n\t\t\t\t\t</el-input>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<el-form-item :style='{\"margin\":\"0 0 20px 0\"}' v-else-if=\"ruleForm.rukuyuanyin\" label=\"入库原因\" prop=\"rukuyuanyin\">\r\n\t\t\t\t\t<span :style='{\"fontSize\":\"14px\",\"lineHeight\":\"40px\",\"color\":\"#333\",\"fontWeight\":\"500\",\"display\":\"inline-block\"}'>{{ruleForm.rukuyuanyin}}</span>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t<el-form-item :style='{\"padding\":\"0\",\"margin\":\"0\"}' class=\"btn\">\r\n\t\t\t\t<el-button class=\"btn3\"  v-if=\"type!='info'\" type=\"success\" @click=\"onSubmit\">\r\n\t\t\t\t\t<span class=\"icon iconfont icon-xihuan\" :style='{\"margin\":\"0 2px\",\"fontSize\":\"14px\",\"color\":\"#fff\",\"height\":\"40px\"}'></span>\r\n\t\t\t\t\t提交\r\n\t\t\t\t</el-button>\r\n\t\t\t\t<el-button class=\"btn4\" v-if=\"type!='info'\" type=\"success\" @click=\"back()\">\r\n\t\t\t\t\t<span class=\"icon iconfont icon-xihuan\" :style='{\"margin\":\"0 2px\",\"fontSize\":\"14px\",\"color\":\"#fff\",\"height\":\"40px\"}'></span>\r\n\t\t\t\t\t取消\r\n\t\t\t\t</el-button>\r\n\t\t\t\t<el-button class=\"btn5\" v-if=\"type=='info'\" type=\"success\" @click=\"back()\">\r\n\t\t\t\t\t<span class=\"icon iconfont icon-xihuan\" :style='{\"margin\":\"0 2px\",\"fontSize\":\"14px\",\"color\":\"#fff\",\"height\":\"40px\"}'></span>\r\n\t\t\t\t\t返回\r\n\t\t\t\t</el-button>\r\n\t\t\t</el-form-item>\r\n\t\t</el-form>\r\n    \r\n\r\n  </div>\r\n</template>\r\n<script>\r\n// 数字，邮件，手机，url，身份证校验\r\nimport { isNumber,isIntNumer,isEmail,isPhone, isMobile,isURL,checkIdCard } from \"@/utils/validate\";\r\nexport default {\r\n\tdata() {\r\n\t\tlet self = this\r\n\t\tvar validateIdCard = (rule, value, callback) => {\r\n\t\t\tif(!value){\r\n\t\t\t\tcallback();\r\n\t\t\t} else if (!checkIdCard(value)) {\r\n\t\t\t\tcallback(new Error(\"请输入正确的身份证号\"));\r\n\t\t\t} else {\r\n\t\t\t\tcallback();\r\n\t\t\t}\r\n\t\t};\r\n\t\tvar validateUrl = (rule, value, callback) => {\r\n\t\t\tif(!value){\r\n\t\t\t\tcallback();\r\n\t\t\t} else if (!isURL(value)) {\r\n\t\t\t\tcallback(new Error(\"请输入正确的URL地址\"));\r\n\t\t\t} else {\r\n\t\t\t\tcallback();\r\n\t\t\t}\r\n\t\t};\r\n\t\tvar validateMobile = (rule, value, callback) => {\r\n\t\t\tif(!value){\r\n\t\t\t\tcallback();\r\n\t\t\t} else if (!isMobile(value)) {\r\n\t\t\t\tcallback(new Error(\"请输入正确的手机号码\"));\r\n\t\t\t} else {\r\n\t\t\t\tcallback();\r\n\t\t\t}\r\n\t\t};\r\n\t\tvar validatePhone = (rule, value, callback) => {\r\n\t\t\tif(!value){\r\n\t\t\t\tcallback();\r\n\t\t\t} else if (!isPhone(value)) {\r\n\t\t\t\tcallback(new Error(\"请输入正确的电话号码\"));\r\n\t\t\t} else {\r\n\t\t\t\tcallback();\r\n\t\t\t}\r\n\t\t};\r\n\t\tvar validateEmail = (rule, value, callback) => {\r\n\t\t\tif(!value){\r\n\t\t\t\tcallback();\r\n\t\t\t} else if (!isEmail(value)) {\r\n\t\t\t\tcallback(new Error(\"请输入正确的邮箱地址\"));\r\n\t\t\t} else {\r\n\t\t\t\tcallback();\r\n\t\t\t}\r\n\t\t};\r\n\t\tvar validateNumber = (rule, value, callback) => {\r\n\t\t\tif(!value){\r\n\t\t\t\tcallback();\r\n\t\t\t} else if (!isNumber(value)) {\r\n\t\t\t\tcallback(new Error(\"请输入数字\"));\r\n\t\t\t} else {\r\n\t\t\t\tcallback();\r\n\t\t\t}\r\n\t\t};\r\n\t\tvar validateIntNumber = (rule, value, callback) => {\r\n\t\t\tif(!value){\r\n\t\t\t\tcallback();\r\n\t\t\t} else if (!isIntNumer(value)) {\r\n\t\t\t\tcallback(new Error(\"请输入整数\"));\r\n\t\t\t} else {\r\n\t\t\t\tcallback();\r\n\t\t\t}\r\n\t\t};\r\n\t\treturn {\r\n\t\t\tid: '',\r\n\t\t\ttype: '',\r\n\t\t\t\r\n\t\t\t\r\n\t\t\tro:{\r\n\t\t\t\tzichanbianma : false,\r\n\t\t\t\tzichanmingcheng : false,\r\n\t\t\t\tzichanleixing : false,\r\n\t\t\t\tzichantupian : false,\r\n\t\t\t\tzichandanjia : false,\r\n\t\t\t\tzichanshuliang : false,\r\n\t\t\t\tzichanzongjia : false,\r\n\t\t\t\trukuyuanyin : false,\r\n\t\t\t\trukushijian : false,\r\n\t\t\t\tgonghao : false,\r\n\t\t\t\txingming : false,\r\n\t\t\t\tsfsh : false,\r\n\t\t\t\tshhf : false,\r\n\t\t\t\tispay : false,\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t\r\n\t\t\truleForm: {\r\n\t\t\t\tzichanbianma: '',\r\n\t\t\t\tzichanmingcheng: '',\r\n\t\t\t\tzichanleixing: '',\r\n\t\t\t\tzichantupian: '',\r\n\t\t\t\tzichandanjia: '',\r\n\t\t\t\tzichanshuliang: '0',\r\n\t\t\t\tzichanzongjia: '',\r\n\t\t\t\trukuyuanyin: '',\r\n\t\t\t\trukushijian: '',\r\n\t\t\t\tgonghao: '',\r\n\t\t\t\txingming: '',\r\n\t\t\t\tshhf: '',\r\n\t\t\t},\r\n\t\t\r\n\r\n\t\t\t\r\n\t\t\trules: {\r\n\t\t\t\tzichanbianma: [\r\n\t\t\t\t],\r\n\t\t\t\tzichanmingcheng: [\r\n\t\t\t\t\t{ required: true, message: '资产名称不能为空', trigger: 'blur' },\r\n\t\t\t\t],\r\n\t\t\t\tzichanleixing: [\r\n\t\t\t\t],\r\n\t\t\t\tzichantupian: [\r\n\t\t\t\t\t{ required: true, message: '资产图片不能为空', trigger: 'blur' },\r\n\t\t\t\t],\r\n\t\t\t\tzichandanjia: [\r\n\t\t\t\t\t{ required: true, message: '资产单价不能为空', trigger: 'blur' },\r\n\t\t\t\t\t{ validator: validateIntNumber, trigger: 'blur' },\r\n\t\t\t\t],\r\n\t\t\t\tzichanshuliang: [\r\n\t\t\t\t\t{ validator: validateIntNumber, trigger: 'blur' },\r\n\t\t\t\t],\r\n\t\t\t\tzichanzongjia: [\r\n\t\t\t\t],\r\n\t\t\t\trukuyuanyin: [\r\n\t\t\t\t],\r\n\t\t\t\trukushijian: [\r\n\t\t\t\t],\r\n\t\t\t\tgonghao: [\r\n\t\t\t\t],\r\n\t\t\t\txingming: [\r\n\t\t\t\t],\r\n\t\t\t\tsfsh: [\r\n\t\t\t\t],\r\n\t\t\t\tshhf: [\r\n\t\t\t\t],\r\n\t\t\t\tispay: [\r\n\t\t\t\t],\r\n\t\t\t}\r\n\t\t};\r\n\t},\r\n\tprops: [\"parent\"],\r\n\tcomputed: {\r\n\r\n\r\n\t\tzichanzongjia:{\r\n\t\t\tget: function () {\r\n\t\t\t\treturn 1*this.ruleForm.zichandanjia*this.ruleForm.zichanshuliang\r\n\t\t\t}\r\n\t\t},\r\n\r\n\t},\r\n    components: {\r\n    },\r\n\tcreated() {\r\n\t\tthis.ruleForm.rukushijian = this.getCurDate()\r\n\t},\r\n\tmethods: {\r\n\t\t\r\n\t\t// 下载\r\n\t\tdownload(file){\r\n\t\t\twindow.open(`${file}`)\r\n\t\t},\r\n\t\t// 初始�?\r\n\t\tinit(id,type) {\r\n\t\t\tif (id) {\r\n\t\t\t\tthis.id = id;\r\n\t\t\t\tthis.type = type;\r\n\t\t\t}\r\n\t\t\tif(this.type=='info'||this.type=='else'){\r\n\t\t\t\tthis.info(id);\r\n\t\t\t}else if(this.type=='logistics'){\r\n\t\t\t\tthis.logistics=false;\r\n\t\t\t\tthis.info(id);\r\n\t\t\t}else if(this.type=='cross'){\r\n\t\t\t\tvar obj = this.$storage.getObj('crossObj');\r\n\t\t\t\tfor (var o in obj){\r\n\t\t\t\t\t\tif(o=='zichanbianma'){\r\n\t\t\t\t\t\t\tthis.ruleForm.zichanbianma = obj[o];\r\n\t\t\t\t\t\t\tthis.ro.zichanbianma = true;\r\n\t\t\t\t\t\t\tcontinue;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif(o=='zichanmingcheng'){\r\n\t\t\t\t\t\t\tthis.ruleForm.zichanmingcheng = obj[o];\r\n\t\t\t\t\t\t\tthis.ro.zichanmingcheng = true;\r\n\t\t\t\t\t\t\tcontinue;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif(o=='zichanleixing'){\r\n\t\t\t\t\t\t\tthis.ruleForm.zichanleixing = obj[o];\r\n\t\t\t\t\t\t\tthis.ro.zichanleixing = true;\r\n\t\t\t\t\t\t\tcontinue;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif(o=='zichantupian'){\r\n\t\t\t\t\t\t\tthis.ruleForm.zichantupian = obj[o];\r\n\t\t\t\t\t\t\tthis.ro.zichantupian = true;\r\n\t\t\t\t\t\t\tcontinue;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif(o=='zichandanjia'){\r\n\t\t\t\t\t\t\tthis.ruleForm.zichandanjia = obj[o];\r\n\t\t\t\t\t\t\tthis.ro.zichandanjia = true;\r\n\t\t\t\t\t\t\tcontinue;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif(o=='zichanshuliang'){\r\n\t\t\t\t\t\t\tthis.ruleForm.zichanshuliang = obj[o];\r\n\t\t\t\t\t\t\tthis.ro.zichanshuliang = true;\r\n\t\t\t\t\t\t\tcontinue;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif(o=='zichanzongjia'){\r\n\t\t\t\t\t\t\tthis.ruleForm.zichanzongjia = obj[o];\r\n\t\t\t\t\t\t\tthis.ro.zichanzongjia = true;\r\n\t\t\t\t\t\t\tcontinue;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif(o=='rukuyuanyin'){\r\n\t\t\t\t\t\t\tthis.ruleForm.rukuyuanyin = obj[o];\r\n\t\t\t\t\t\t\tthis.ro.rukuyuanyin = true;\r\n\t\t\t\t\t\t\tcontinue;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif(o=='rukushijian'){\r\n\t\t\t\t\t\t\tthis.ruleForm.rukushijian = obj[o];\r\n\t\t\t\t\t\t\tthis.ro.rukushijian = true;\r\n\t\t\t\t\t\t\tcontinue;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif(o=='gonghao'){\r\n\t\t\t\t\t\t\tthis.ruleForm.gonghao = obj[o];\r\n\t\t\t\t\t\t\tthis.ro.gonghao = true;\r\n\t\t\t\t\t\t\tcontinue;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif(o=='xingming'){\r\n\t\t\t\t\t\t\tthis.ruleForm.xingming = obj[o];\r\n\t\t\t\t\t\t\tthis.ro.xingming = true;\r\n\t\t\t\t\t\t\tcontinue;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\r\n\r\n\r\n\r\n\r\n\t\t\t\tthis.ruleForm.zichanshuliang = 0\r\n\t\t\t\tthis.ro.zichanshuliang = false;\r\n\r\n\t\t\t\tthis.ruleForm.zichanshuliang = Number('0'); \r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t// 获取用户信息\r\n\t\t\tthis.$http({\r\n\t\t\t\turl: `${this.$storage.get('sessionTable')}/session`,\r\n\t\t\t\tmethod: \"get\"\r\n\t\t\t}).then(({ data }) => {\r\n\t\t\t\tif (data && data.code === 0) {\r\n\t\t\t\t\t\r\n\t\t\t\t\tvar json = data.data;\r\n\t\t\t\t\tif(this.$storage.get(\"role\")!=\"管理员\") {\r\n\t\t\t\t\t\tthis.ro.zichandanjia = true;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif(((json.gonghao!=''&&json.gonghao) || json.gonghao==0) && this.$storage.get(\"role\")!=\"管理员\"){\r\n\t\t\t\t\t\tthis.ruleForm.gonghao = json.gonghao\r\n\t\t\t\t\t\tthis.ro.gonghao = true;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif(((json.xingming!=''&&json.xingming) || json.xingming==0) && this.$storage.get(\"role\")!=\"管理员\"){\r\n\t\t\t\t\t\tthis.ruleForm.xingming = json.xingming\r\n\t\t\t\t\t\tthis.ro.xingming = true;\r\n\t\t\t\t\t}\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.$message.error(data.msg);\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t\t\r\n\t\t\t\r\n\t\t},\r\n    // 多级联动参数\r\n\r\n    info(id) {\r\n      this.$http({\r\n        url: `zichancaigou/info/${id}`,\r\n        method: \"get\"\r\n      }).then(({ data }) => {\r\n        if (data && data.code === 0) {\r\n        this.ruleForm = data.data;\r\n        //解决前台上传图片后台不显示的问题\r\n        let reg=new RegExp('../../../upload','g')//g代表全部\r\n        } else {\r\n          this.$message.error(data.msg);\r\n        }\r\n      });\r\n    },\r\n\r\n\r\n    // 提交\r\n    onSubmit() {\r\n        this.ruleForm.zichanzongjia = this.zichanzongjia\r\n\r\n\r\n\r\n\r\n\tif(this.ruleForm.zichantupian!=null) {\r\n\t\tthis.ruleForm.zichantupian = this.ruleForm.zichantupian.replace(new RegExp(this.$base.url,\"g\"),\"\");\r\n\t}\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\nvar objcross = this.$storage.getObj('crossObj');\r\n      var table = this.$storage.getObj('crossTable');\r\n      if(objcross!=null) {\r\n\t\t  if(!this.ruleForm.zichanshuliang){\r\n\t\t\t  this.$message.error(\"采购数量不能为空\");\r\n\t\t\t  return\r\n\t\t  }\r\n\t      objcross.zichanshuliang = parseFloat(objcross.zichanshuliang) + parseFloat(this.ruleForm.zichanshuliang)\r\n                }\r\n      //更新跨表属�?\r\n       var crossuserid;\r\n       var crossrefid;\r\n       var crossoptnum;\r\n       if(this.type=='cross'){\r\n                var statusColumnName = this.$storage.get('statusColumnName');\r\n                var statusColumnValue = this.$storage.get('statusColumnValue');\r\n                if(statusColumnName!='') {\r\n                        var obj = this.$storage.getObj('crossObj');\r\n                       if(statusColumnName && !statusColumnName.startsWith(\"[\")) {\r\n                               for (var o in obj){\r\n                                 if(o==statusColumnName){\r\n                                   obj[o] = statusColumnValue;\r\n                                 }\r\n                               }\r\n                               var table = this.$storage.get('crossTable');\r\n                             this.$http({\r\n                                 url: `${table}/update`,\r\n                                 method: \"post\",\r\n                                 data: obj\r\n                               }).then(({ data }) => {});\r\n                              this.$http({\r\n                                  url: `${table}/update`,\r\n                                  method: \"post\",\r\n                                  data: objcross\r\n                                }).then(({ data }) => {});\r\n                       } else {\r\n                               crossuserid=this.$storage.get('userid');\r\n                               crossrefid=obj['id'];\r\n                               crossoptnum=this.$storage.get('statusColumnName');\r\n                               crossoptnum=crossoptnum.replace(/\\[/,\"\").replace(/\\]/,\"\");\r\n                        }\r\n                }\r\n        }\r\n\t\tthis.$refs[\"ruleForm\"].validate(valid => {\r\n\t\t\tif (valid) {\r\n\t\t\t\tif(crossrefid && crossuserid) {\r\n\t\t\t\t\tthis.ruleForm.crossuserid = crossuserid;\r\n\t\t\t\t\tthis.ruleForm.crossrefid = crossrefid;\r\n\t\t\t\t\tlet params = { \r\n\t\t\t\t\t\tpage: 1, \r\n\t\t\t\t\t\tlimit: 10, \r\n\t\t\t\t\t\tcrossuserid:this.ruleForm.crossuserid,\r\n\t\t\t\t\t\tcrossrefid:this.ruleForm.crossrefid,\r\n\t\t\t\t\t} \r\n\t\t\t\tthis.$http({ \r\n\t\t\t\t\turl: \"zichancaigou/page\", \r\n\t\t\t\t\tmethod: \"get\", \r\n\t\t\t\t\tparams: params \r\n\t\t\t\t}).then(({ \r\n\t\t\t\t\tdata \r\n\t\t\t\t}) => { \r\n\t\t\t\t\tif (data && data.code === 0) { \r\n\t\t\t\t\t\tif(data.data.total>=crossoptnum) {\r\n\t\t\t\t\t\t\tthis.$message.error(this.$storage.get('tips'));\r\n\t\t\t\t\t\t\treturn false;\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tthis.$http({\r\n\t\t\t\t\t\t\t\turl: `zichancaigou/${!this.ruleForm.id ? \"save\" : \"update\"}`,\r\n\t\t\t\t\t\t\t\tmethod: \"post\",\r\n\t\t\t\t\t\t\t\tdata: this.ruleForm\r\n\t\t\t\t\t\t\t}).then(({ data }) => {\r\n\t\t\t\t\t\t\t\tif (data && data.code === 0) {\r\n\t\t\t\t\t\t\t\t\tthis.$message({\r\n\t\t\t\t\t\t\t\t\t\tmessage: \"操作成功\",\r\n\t\t\t\t\t\t\t\t\t\ttype: \"success\",\r\n\t\t\t\t\t\t\t\t\t\tduration: 1500,\r\n\t\t\t\t\t\t\t\t\t\tonClose: () => {\r\n\t\t\t\t\t\t\t\t\t\t\tthis.parent.showFlag = true;\r\n\t\t\t\t\t\t\t\t\t\t\tthis.parent.addOrUpdateFlag = false;\r\n\t\t\t\t\t\t\t\t\t\t\tthis.parent.zichancaigouCrossAddOrUpdateFlag = false;\r\n\t\t\t\t\t\t\t\t\t\t\tthis.parent.search();\r\n\t\t\t\t\t\t\t\t\t\t\tthis.parent.contentStyleChange();\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\tthis.$http({\r\n\t\t\t\t\t\t\t\t\t\turl: `${table}/update`,\r\n\t\t\t\t\t\t\t\t\t\tmethod: \"post\",\r\n\t\t\t\t\t\t\t\t\t\tdata: objcross\r\n\t\t\t\t\t\t\t\t\t}).then(({ data }) => {});\r\n\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\tthis.$message.error(data.msg);\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t});\r\n\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t} else { \r\n\t\t\t\t} \r\n\t\t\t});\r\n\t\t} else {\r\n\t\t\tthis.$http({\r\n\t\t\t\turl: `zichancaigou/${!this.ruleForm.id ? \"save\" : \"update\"}`,\r\n\t\t\t\tmethod: \"post\",\r\n\t\t\t   data: this.ruleForm\r\n\t\t\t}).then(({ data }) => {\r\n\t\t\t\tif (data && data.code === 0) {\r\n\t\t\t\t\tthis.$http({\r\n\t\t\t\t\t\turl: `${table}/update`,\r\n\t\t\t\t\t\tmethod: \"post\",\r\n\t\t\t\t\t\tdata: objcross\r\n\t\t\t\t\t}).then(({ data }) => {});\r\n\t\t\t\t\tthis.$message({\r\n\t\t\t\t\t\tmessage: \"操作成功\",\r\n\t\t\t\t\t\ttype: \"success\",\r\n\t\t\t\t\t\tduration: 1500,\r\n\t\t\t\t\t\tonClose: () => {\r\n\t\t\t\t\t\t\tthis.parent.showFlag = true;\r\n\t\t\t\t\t\t\tthis.parent.addOrUpdateFlag = false;\r\n\t\t\t\t\t\t\tthis.parent.zichancaigouCrossAddOrUpdateFlag = false;\r\n\t\t\t\t\t\t\tthis.parent.search();\r\n\t\t\t\t\t\t\tthis.parent.contentStyleChange();\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t});\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.$message.error(data.msg);\r\n\t\t\t   }\r\n\t\t\t});\r\n\t\t }\r\n         }\r\n       });\r\n    },\r\n    // 获取uuid\r\n    getUUID () {\r\n      return new Date().getTime();\r\n    },\r\n    // 返回\r\n    back() {\r\n      this.parent.showFlag = true;\r\n      this.parent.addOrUpdateFlag = false;\r\n      this.parent.zichancaigouCrossAddOrUpdateFlag = false;\r\n      this.parent.contentStyleChange();\r\n    },\r\n    zichantupianUploadChange(fileUrls) {\r\n\t    this.ruleForm.zichantupian = fileUrls;\r\n    },\r\n  }\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n\t.amap-wrapper {\r\n\t\twidth: 100%;\r\n\t\theight: 500px;\r\n\t}\r\n\t\r\n\t.search-box {\r\n\t\tposition: absolute;\r\n\t}\r\n\t\r\n\t.el-date-editor.el-input {\r\n\t\twidth: auto;\r\n\t}\r\n\t\r\n\t.add-update-preview .el-form-item :deep .el-form-item__label {\r\n\t  \t  padding: 0 10px 0 0;\r\n\t  \t  color: #374254;\r\n\t  \t  font-weight: 600;\r\n\t  \t  width: 80px;\r\n\t  \t  font-size: 14px;\r\n\t  \t  line-height: 40px;\r\n\t  \t  text-align: right;\r\n\t  \t}\r\n\t\r\n\t.add-update-preview .el-form-item :deep .el-form-item__content {\r\n\t  margin-left: 80px;\r\n\t}\r\n\t\r\n\t.add-update-preview .el-input :deep .el-input__inner {\r\n\t  \t  border: 1px solid rgba(167, 180, 201,.3);\r\n\t  \t  border-radius: 4px;\r\n\t  \t  padding: 0 12px;\r\n\t  \t  outline: none;\r\n\t  \t  color: #a7b4c9;\r\n\t  \t  width: 400px;\r\n\t  \t  font-size: 14px;\r\n\t  \t  height: 40px;\r\n\t  \t}\r\n\t.add-update-preview .el-input-number :deep .el-input__inner {\r\n\t\ttext-align: left;\r\n\t  \t  border: 1px solid rgba(167, 180, 201,.3);\r\n\t  \t  border-radius: 4px;\r\n\t  \t  padding: 0 12px;\r\n\t  \t  outline: none;\r\n\t  \t  color: #a7b4c9;\r\n\t  \t  width: 400px;\r\n\t  \t  font-size: 14px;\r\n\t  \t  height: 40px;\r\n\t  \t}\r\n\t.add-update-preview .el-input-number :deep .el-input-number__decrease {\r\n\t\tdisplay: none;\r\n\t}\r\n\t.add-update-preview .el-input-number :deep .el-input-number__increase {\r\n\t\tdisplay: none;\r\n\t}\r\n\t\r\n\t.add-update-preview .el-select :deep .el-input__inner {\r\n\t  \t  border:  1px solid rgba(167, 180, 201,.3) ;\r\n\t  \t  border-radius: 4px;\r\n\t  \t  padding: 0 10px;\r\n\t  \t  outline: none;\r\n\t  \t  color: #a7b4c9;\r\n\t  \t  width: 400px;\r\n\t  \t  font-size: 14px;\r\n\t  \t  height: 40px;\r\n\t  \t}\r\n\t\r\n\t.add-update-preview .el-date-editor :deep .el-input__inner {\r\n\t  \t  border:  1px solid rgba(167, 180, 201,.3) ;\r\n\t  \t  border-radius: 4px;\r\n\t  \t  padding: 0 10px 0 30px;\r\n\t  \t  outline: none;\r\n\t  \t  color: #a7b4c9;\r\n\t  \t  width: 400px;\r\n\t  \t  font-size: 14px;\r\n\t  \t  height: 40px;\r\n\t  \t}\r\n\t\r\n\t.add-update-preview :deep .el-upload--picture-card {\r\n\t\tbackground: transparent;\r\n\t\tborder: 0;\r\n\t\tborder-radius: 0;\r\n\t\twidth: auto;\r\n\t\theight: auto;\r\n\t\tline-height: initial;\r\n\t\tvertical-align: middle;\r\n\t}\r\n\t\r\n\t.add-update-preview :deep .upload .upload-img {\r\n\t  \t  border: 2px dashed rgba(167, 180, 201,.3) ;\r\n\t  \t  cursor: pointer;\r\n\t  \t  border-radius: 6px;\r\n\t  \t  color: #a7b4c9  ;\r\n\t  \t  width: 200px;\r\n\t  \t  font-size: 32px;\r\n\t  \t  line-height: 200px;\r\n\t  \t  text-align: center;\r\n\t  \t  height: 200px;\r\n\t  \t}\r\n\t\r\n\t.add-update-preview :deep .el-upload-list .el-upload-list__item {\r\n\t  \t  border: 2px dashed rgba(167, 180, 201,.3) ;\r\n\t  \t  cursor: pointer;\r\n\t  \t  border-radius: 6px;\r\n\t  \t  color: #a7b4c9  ;\r\n\t  \t  width: 200px;\r\n\t  \t  font-size: 32px;\r\n\t  \t  line-height: 200px;\r\n\t  \t  text-align: center;\r\n\t  \t  height: 200px;\r\n\t  \t}\r\n\t\r\n\t.add-update-preview :deep .el-upload .el-icon-plus {\r\n\t  \t  border: 2px dashed rgba(167, 180, 201,.3) ;\r\n\t  \t  cursor: pointer;\r\n\t  \t  border-radius: 6px;\r\n\t  \t  color: #a7b4c9  ;\r\n\t  \t  width: 200px;\r\n\t  \t  font-size: 32px;\r\n\t  \t  line-height: 200px;\r\n\t  \t  text-align: center;\r\n\t  \t  height: 200px;\r\n\t  \t}\r\n\t\r\n\t.add-update-preview .el-textarea :deep .el-textarea__inner {\r\n\t  \t  border: 1px solid rgba(167, 180, 201,.3)     ;\r\n\t  \t  border-radius: 4px;\r\n\t  \t  padding: 12px;\r\n\t  \t  outline: none;\r\n\t  \t  color: #a7b4c9 ;\r\n\t  \t  width: 400px;\r\n\t  \t  font-size: 14px;\r\n\t  \t  height: 120px;\r\n\t  \t}\r\n\t\r\n\t.add-update-preview .btn .btn1 {\r\n\t\t\t\tborder: 0;\r\n\t\t\t\tcursor: pointer;\r\n\t\t\t\tborder-radius: 4px;\r\n\t\t\t\tpadding: 0 24px;\r\n\t\t\t\tmargin: 4px;\r\n\t\t\t\toutline: none;\r\n\t\t\t\tcolor: #fff;\r\n\t\t\t\tbackground: #6574cd;\r\n\t\t\t\twidth: auto;\r\n\t\t\t\tfont-size: 14px;\r\n\t\t\t\theight: 40px;\r\n\t\t\t}\r\n\t\r\n\t.add-update-preview .btn .btn1:hover {\r\n\t\t\t\topacity: 0.8;\r\n\t\t\t}\r\n\t\r\n\t.add-update-preview .btn .btn2 {\r\n\t\t\t\tborder: 0;\r\n\t\t\t\tcursor: pointer;\r\n\t\t\t\tborder-radius: 4px;\r\n\t\t\t\tpadding: 0 24px;\r\n\t\t\t\tmargin: 4px;\r\n\t\t\t\toutline: none;\r\n\t\t\t\tcolor: #fff;\r\n\t\t\t\tbackground: #6574cd;\r\n\t\t\t\twidth: auto;\r\n\t\t\t\tfont-size: 14px;\r\n\t\t\t\theight: 40px;\r\n\t\t\t}\r\n\t\r\n\t.add-update-preview .btn .btn2:hover {\r\n\t\t\t\topacity: 0.8;\r\n\t\t\t}\r\n\t\r\n\t.add-update-preview .btn .btn3 {\r\n\t\t\t\tborder: 0;\r\n\t\t\t\tcursor: pointer;\r\n\t\t\t\tborder-radius: 4px;\r\n\t\t\t\tpadding: 0 24px;\r\n\t\t\t\tmargin: 4px;\r\n\t\t\t\toutline: none;\r\n\t\t\t\tcolor: #fff;\r\n\t\t\t\tbackground: #ff2b88;\r\n\t\t\t\twidth: auto;\r\n\t\t\t\tfont-size: 14px;\r\n\t\t\t\theight: 40px;\r\n\t\t\t}\r\n\t\r\n\t.add-update-preview .btn .btn3:hover {\r\n\t\t\t\topacity: 0.8;\r\n\t\t\t}\r\n\t\r\n\t.add-update-preview .btn .btn4 {\r\n\t\t\t\tborder: 0;\r\n\t\t\t\tcursor: pointer;\r\n\t\t\t\tborder-radius: 4px;\r\n\t\t\t\tpadding: 0 24px;\r\n\t\t\t\tmargin: 4px;\r\n\t\t\t\toutline: none;\r\n\t\t\t\tcolor: #fff;\r\n\t\t\t\tbackground: #ff2b88;\r\n\t\t\t\twidth: auto;\r\n\t\t\t\tfont-size: 14px;\r\n\t\t\t\theight: 40px;\r\n\t\t\t}\r\n\t\r\n\t.add-update-preview .btn .btn4:hover {\r\n\t\t\t\topacity: 0.8;\r\n\t\t\t}\r\n\t\r\n\t.add-update-preview .btn .btn5 {\r\n\t\t\t\tborder: 0;\r\n\t\t\t\tcursor: pointer;\r\n\t\t\t\tborder-radius: 4px;\r\n\t\t\t\tpadding: 0 24px;\r\n\t\t\t\tmargin: 4px;\r\n\t\t\t\toutline: none;\r\n\t\t\t\tcolor: #fff;\r\n\t\t\t\tbackground: #ff2b88;\r\n\t\t\t\twidth: auto;\r\n\t\t\t\tfont-size: 14px;\r\n\t\t\t\theight: 40px;\r\n\t\t\t}\r\n\t\r\n\t.add-update-preview .btn .btn5:hover {\r\n\t\t\t\topacity: 0.8;\r\n\t\t\t}\r\n</style>\r\n"]}]}