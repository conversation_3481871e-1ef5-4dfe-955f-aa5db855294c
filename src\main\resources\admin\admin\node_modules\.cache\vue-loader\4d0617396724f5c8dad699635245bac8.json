{"remainingRequest": "E:\\java系统\\公司财务系统\\src\\main\\resources\\admin\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\java系统\\公司财务系统\\src\\main\\resources\\admin\\admin\\src\\views\\modules\\yuangonggongzi\\add-or-update.vue?vue&type=style&index=0&id=48754094&lang=scss&scoped=true", "dependencies": [{"path": "E:\\java系统\\公司财务系统\\src\\main\\resources\\admin\\admin\\src\\views\\modules\\yuangonggongzi\\add-or-update.vue", "mtime": 1754637973541}, {"path": "E:\\java系统\\公司财务系统\\src\\main\\resources\\admin\\admin\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1754805255591}, {"path": "E:\\java系统\\公司财务系统\\src\\main\\resources\\admin\\admin\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1754628163326}, {"path": "E:\\java系统\\公司财务系统\\src\\main\\resources\\admin\\admin\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1754628158457}, {"path": "E:\\java系统\\公司财务系统\\src\\main\\resources\\admin\\admin\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1754628153247}, {"path": "E:\\java系统\\公司财务系统\\src\\main\\resources\\admin\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754805254356}, {"path": "E:\\java系统\\公司财务系统\\src\\main\\resources\\admin\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754628160828}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["add-or-update.vue"], "names": [], "mappings": ";AAi3BA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA", "file": "add-or-update.vue", "sourceRoot": "src/views/modules/yuangonggongzi", "sourcesContent": ["<template>\r\n\t<div class=\"addEdit-block\" :style='{\"padding\":\"30px\",\"margin\":\"0\"}'>\r\n\t\t<el-form\r\n\t\t\t:style='{\"border\":\"1px solid rgba(167, 180, 201,.3)  \",\"padding\":\"30px\",\"borderRadius\":\"6px\",\"background\":\"#fff\"}'\r\n\t\t\tclass=\"add-update-preview\"\r\n\t\t\tref=\"ruleForm\"\r\n\t\t\t:model=\"ruleForm\"\r\n\t\t\t:rules=\"rules\"\r\n\t\t\tlabel-width=\"80px\"\r\n\t\t>\r\n\t\t\t<template >\r\n\t\t\t\t<el-form-item :style='{\"margin\":\"0 0 20px 0\"}' class=\"select\" v-if=\"type!='info'\"  label=\"月份\" prop=\"yuefen\">\r\n\t\t\t\t\t<el-select :disabled=\"ro.yuefen\" v-model=\"ruleForm.yuefen\" placeholder=\"请选择月份\" >\r\n\t\t\t\t\t\t<el-option\r\n\t\t\t\t\t\t\tv-for=\"(item,index) in yuefenOptions\"\r\n\t\t\t\t\t\t\tv-bind:key=\"index\"\r\n\t\t\t\t\t\t\t:label=\"item\"\r\n\t\t\t\t\t\t\t:value=\"item\">\r\n\t\t\t\t\t\t</el-option>\r\n\t\t\t\t\t</el-select>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<el-form-item :style='{\"margin\":\"0 0 20px 0\"}' v-else class=\"input\" label=\"月份\" prop=\"yuefen\">\r\n\t\t\t\t\t<el-input v-model=\"ruleForm.yuefen\"\r\n\t\t\t\t\t\tplaceholder=\"月份\" readonly></el-input>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<el-form-item :style='{\"margin\":\"0 0 20px 0\"}' class=\"select\" v-if=\"type!='info'\" label=\"工号\" prop=\"gonghao\">\r\n\t\t\t\t\t<el-select :disabled=\"ro.gonghao\" @change=\"gonghaoChange\" v-model=\"ruleForm.gonghao\" placeholder=\"请选择工号\">\r\n\t\t\t\t\t\t<el-option\r\n\t\t\t\t\t\t\tv-for=\"(item,index) in gonghaoOptions\"\r\n\t\t\t\t\t\t\tv-bind:key=\"index\"\r\n\t\t\t\t\t\t\t:label=\"item\"\r\n\t\t\t\t\t\t\t:value=\"item\">\r\n\t\t\t\t\t\t</el-option>\r\n\t\t\t\t\t</el-select>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<el-form-item :style='{\"margin\":\"0 0 20px 0\"}' class=\"input\" v-else-if=\"ruleForm.gonghao\" label=\"工号\" prop=\"gonghao\">\r\n\t\t\t\t\t<el-input v-model=\"ruleForm.gonghao\" placeholder=\"工号\" readonly></el-input>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<el-form-item :style='{\"margin\":\"0 0 20px 0\"}' class=\"input\" v-if=\"type!='info'\"  label=\"姓名\" prop=\"xingming\">\r\n\t\t\t\t\t<el-input v-model=\"ruleForm.xingming\" placeholder=\"姓名\" clearable  :readonly=\"ro.xingming\"></el-input>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<el-form-item :style='{\"margin\":\"0 0 20px 0\"}' v-else class=\"input\" label=\"姓名\" prop=\"xingming\">\r\n\t\t\t\t\t<el-input v-model=\"ruleForm.xingming\" placeholder=\"姓名\" readonly></el-input>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<el-form-item :style='{\"margin\":\"0 0 20px 0\"}' class=\"input\" v-if=\"type!='info'\"  label=\"部门\" prop=\"bumen\">\r\n\t\t\t\t\t<el-input v-model=\"ruleForm.bumen\" placeholder=\"部门\" clearable  :readonly=\"ro.bumen\"></el-input>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<el-form-item :style='{\"margin\":\"0 0 20px 0\"}' v-else class=\"input\" label=\"部门\" prop=\"bumen\">\r\n\t\t\t\t\t<el-input v-model=\"ruleForm.bumen\" placeholder=\"部门\" readonly></el-input>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<el-form-item :style='{\"margin\":\"0 0 20px 0\"}' class=\"input\" v-if=\"type!='info'\"  label=\"职位\" prop=\"zhiwei\">\r\n\t\t\t\t\t<el-input v-model=\"ruleForm.zhiwei\" placeholder=\"职位\" clearable  :readonly=\"ro.zhiwei\"></el-input>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<el-form-item :style='{\"margin\":\"0 0 20px 0\"}' v-else class=\"input\" label=\"职位\" prop=\"zhiwei\">\r\n\t\t\t\t\t<el-input v-model=\"ruleForm.zhiwei\" placeholder=\"职位\" readonly></el-input>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<el-form-item :style='{\"margin\":\"0 0 20px 0\"}' class=\"input\" v-if=\"type!='info'\"  label=\"基本工资\" prop=\"jibengongzi\">\r\n\t\t\t\t\t<el-input-number v-model=\"ruleForm.jibengongzi\" placeholder=\"基本工资\" :readonly=\"ro.jibengongzi\"></el-input-number>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<el-form-item :style='{\"margin\":\"0 0 20px 0\"}' v-else class=\"input\" label=\"基本工资\" prop=\"jibengongzi\">\r\n\t\t\t\t\t<el-input v-model=\"ruleForm.jibengongzi\" placeholder=\"基本工资\" readonly></el-input>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<el-form-item :style='{\"margin\":\"0 0 20px 0\"}' class=\"input\" v-if=\"type!='info'\"  label=\"加班工资\" prop=\"jiabangongzi\">\r\n\t\t\t\t\t<el-input-number v-model=\"ruleForm.jiabangongzi\" placeholder=\"加班工资\" :readonly=\"ro.jiabangongzi\"></el-input-number>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<el-form-item :style='{\"margin\":\"0 0 20px 0\"}' v-else class=\"input\" label=\"加班工资\" prop=\"jiabangongzi\">\r\n\t\t\t\t\t<el-input v-model=\"ruleForm.jiabangongzi\" placeholder=\"加班工资\" readonly></el-input>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<el-form-item :style='{\"margin\":\"0 0 20px 0\"}' class=\"input\" v-if=\"type!='info'\"  label=\"绩效金额\" prop=\"jixiaojine\">\r\n\t\t\t\t\t<el-input-number v-model=\"ruleForm.jixiaojine\" placeholder=\"绩效金额\" :readonly=\"ro.jixiaojine\"></el-input-number>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<el-form-item :style='{\"margin\":\"0 0 20px 0\"}' v-else class=\"input\" label=\"绩效金额\" prop=\"jixiaojine\">\r\n\t\t\t\t\t<el-input v-model=\"ruleForm.jixiaojine\" placeholder=\"绩效金额\" readonly></el-input>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<el-form-item :style='{\"margin\":\"0 0 20px 0\"}' class=\"input\" v-if=\"type!='info'\"  label=\"扣款金额\" prop=\"koukuanjine\">\r\n\t\t\t\t\t<el-input-number v-model=\"ruleForm.koukuanjine\" placeholder=\"扣款金额\" :readonly=\"ro.koukuanjine\"></el-input-number>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<el-form-item :style='{\"margin\":\"0 0 20px 0\"}' v-else class=\"input\" label=\"扣款金额\" prop=\"koukuanjine\">\r\n\t\t\t\t\t<el-input v-model=\"ruleForm.koukuanjine\" placeholder=\"扣款金额\" readonly></el-input>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<el-form-item :style='{\"margin\":\"0 0 20px 0\"}' class=\"input\" v-if=\"type!='info'\"  label=\"其他补助\" prop=\"qitabuzhu\">\r\n\t\t\t\t\t<el-input-number v-model=\"ruleForm.qitabuzhu\" placeholder=\"其他补助\" :readonly=\"ro.qitabuzhu\"></el-input-number>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<el-form-item :style='{\"margin\":\"0 0 20px 0\"}' v-else class=\"input\" label=\"其他补助\" prop=\"qitabuzhu\">\r\n\t\t\t\t\t<el-input v-model=\"ruleForm.qitabuzhu\" placeholder=\"其他补助\" readonly></el-input>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<el-form-item :style='{\"margin\":\"0 0 20px 0\"}' class=\"input\" v-if=\"type!='info'\" label=\"实发工资\" prop=\"shifagongzi\">\r\n\t\t\t\t\t<el-input v-model=\"shifagongzi\" placeholder=\"实发工资\" readonly></el-input>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<el-form-item :style='{\"margin\":\"0 0 20px 0\"}' class=\"input\" v-else-if=\"ruleForm.shifagongzi\" label=\"实发工资\" prop=\"shifagongzi\">\r\n\t\t\t\t\t<el-input v-model=\"ruleForm.shifagongzi\" placeholder=\"实发工资\" readonly></el-input>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<el-form-item :style='{\"margin\":\"0 0 20px 0\"}' class=\"date\" v-if=\"type!='info'\" label=\"发布日期\" prop=\"faburiqi\">\r\n\t\t\t\t\t<el-date-picker\r\n\t\t\t\t\t\tformat=\"yyyy �?MM �?dd �?\r\n\t\t\t\t\t\tvalue-format=\"yyyy-MM-dd\"\r\n\t\t\t\t\t\tv-model=\"ruleForm.faburiqi\" \r\n\t\t\t\t\t\ttype=\"date\"\r\n\t\t\t\t\t\t:readonly=\"ro.faburiqi\"\r\n\t\t\t\t\t\tplaceholder=\"发布日期\"\r\n\t\t\t\t\t></el-date-picker> \r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<el-form-item :style='{\"margin\":\"0 0 20px 0\"}' class=\"input\" v-else-if=\"ruleForm.faburiqi\" label=\"发布日期\" prop=\"faburiqi\">\r\n\t\t\t\t\t<el-input v-model=\"ruleForm.faburiqi\" placeholder=\"发布日期\" readonly></el-input>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t</template>\r\n\t\t\t\t<el-form-item :style='{\"margin\":\"0 0 20px 0\"}' class=\"textarea\" v-if=\"type!='info'\" label=\"扣款原因\" prop=\"koukuanyuanyin\">\r\n\t\t\t\t\t<el-input\r\n\t\t\t\t\t  style=\"min-width: 200px; max-width: 600px;\"\r\n\t\t\t\t\t  type=\"textarea\"\r\n\t\t\t\t\t  :rows=\"8\"\r\n\t\t\t\t\t  placeholder=\"扣款原因\"\r\n\t\t\t\t\t  v-model=\"ruleForm.koukuanyuanyin\" >\r\n\t\t\t\t\t</el-input>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<el-form-item :style='{\"margin\":\"0 0 20px 0\"}' v-else-if=\"ruleForm.koukuanyuanyin\" label=\"扣款原因\" prop=\"koukuanyuanyin\">\r\n\t\t\t\t\t<span :style='{\"fontSize\":\"14px\",\"lineHeight\":\"40px\",\"color\":\"#333\",\"fontWeight\":\"500\",\"display\":\"inline-block\"}'>{{ruleForm.koukuanyuanyin}}</span>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<el-form-item :style='{\"margin\":\"0 0 20px 0\"}' class=\"textarea\" v-if=\"type!='info'\" label=\"福利\" prop=\"fuli\">\r\n\t\t\t\t\t<el-input\r\n\t\t\t\t\t  style=\"min-width: 200px; max-width: 600px;\"\r\n\t\t\t\t\t  type=\"textarea\"\r\n\t\t\t\t\t  :rows=\"8\"\r\n\t\t\t\t\t  placeholder=\"福利\"\r\n\t\t\t\t\t  v-model=\"ruleForm.fuli\" >\r\n\t\t\t\t\t</el-input>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<el-form-item :style='{\"margin\":\"0 0 20px 0\"}' v-else-if=\"ruleForm.fuli\" label=\"福利\" prop=\"fuli\">\r\n\t\t\t\t\t<span :style='{\"fontSize\":\"14px\",\"lineHeight\":\"40px\",\"color\":\"#333\",\"fontWeight\":\"500\",\"display\":\"inline-block\"}'>{{ruleForm.fuli}}</span>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t<el-form-item :style='{\"padding\":\"0\",\"margin\":\"0\"}' class=\"btn\">\r\n\t\t\t\t<el-button class=\"btn3\"  v-if=\"type!='info'\" type=\"success\" @click=\"onSubmit\">\r\n\t\t\t\t\t<span class=\"icon iconfont icon-xihuan\" :style='{\"margin\":\"0 2px\",\"fontSize\":\"14px\",\"color\":\"#fff\",\"height\":\"40px\"}'></span>\r\n\t\t\t\t\t提交\r\n\t\t\t\t</el-button>\r\n\t\t\t\t<el-button class=\"btn4\" v-if=\"type!='info'\" type=\"success\" @click=\"back()\">\r\n\t\t\t\t\t<span class=\"icon iconfont icon-xihuan\" :style='{\"margin\":\"0 2px\",\"fontSize\":\"14px\",\"color\":\"#fff\",\"height\":\"40px\"}'></span>\r\n\t\t\t\t\t取消\r\n\t\t\t\t</el-button>\r\n\t\t\t\t<el-button class=\"btn5\" v-if=\"type=='info'\" type=\"success\" @click=\"back()\">\r\n\t\t\t\t\t<span class=\"icon iconfont icon-xihuan\" :style='{\"margin\":\"0 2px\",\"fontSize\":\"14px\",\"color\":\"#fff\",\"height\":\"40px\"}'></span>\r\n\t\t\t\t\t返回\r\n\t\t\t\t</el-button>\r\n\t\t\t</el-form-item>\r\n\t\t</el-form>\r\n    \r\n\r\n  </div>\r\n</template>\r\n<script>\r\n// 数字，邮件，手机，url，身份证校验\r\nimport { isNumber,isIntNumer,isEmail,isPhone, isMobile,isURL,checkIdCard } from \"@/utils/validate\";\r\nexport default {\r\n\tdata() {\r\n\t\tlet self = this\r\n\t\tvar validateIdCard = (rule, value, callback) => {\r\n\t\t\tif(!value){\r\n\t\t\t\tcallback();\r\n\t\t\t} else if (!checkIdCard(value)) {\r\n\t\t\t\tcallback(new Error(\"请输入正确的身份证号\"));\r\n\t\t\t} else {\r\n\t\t\t\tcallback();\r\n\t\t\t}\r\n\t\t};\r\n\t\tvar validateUrl = (rule, value, callback) => {\r\n\t\t\tif(!value){\r\n\t\t\t\tcallback();\r\n\t\t\t} else if (!isURL(value)) {\r\n\t\t\t\tcallback(new Error(\"请输入正确的URL地址\"));\r\n\t\t\t} else {\r\n\t\t\t\tcallback();\r\n\t\t\t}\r\n\t\t};\r\n\t\tvar validateMobile = (rule, value, callback) => {\r\n\t\t\tif(!value){\r\n\t\t\t\tcallback();\r\n\t\t\t} else if (!isMobile(value)) {\r\n\t\t\t\tcallback(new Error(\"请输入正确的手机号码\"));\r\n\t\t\t} else {\r\n\t\t\t\tcallback();\r\n\t\t\t}\r\n\t\t};\r\n\t\tvar validatePhone = (rule, value, callback) => {\r\n\t\t\tif(!value){\r\n\t\t\t\tcallback();\r\n\t\t\t} else if (!isPhone(value)) {\r\n\t\t\t\tcallback(new Error(\"请输入正确的电话号码\"));\r\n\t\t\t} else {\r\n\t\t\t\tcallback();\r\n\t\t\t}\r\n\t\t};\r\n\t\tvar validateEmail = (rule, value, callback) => {\r\n\t\t\tif(!value){\r\n\t\t\t\tcallback();\r\n\t\t\t} else if (!isEmail(value)) {\r\n\t\t\t\tcallback(new Error(\"请输入正确的邮箱地址\"));\r\n\t\t\t} else {\r\n\t\t\t\tcallback();\r\n\t\t\t}\r\n\t\t};\r\n\t\tvar validateNumber = (rule, value, callback) => {\r\n\t\t\tif(!value){\r\n\t\t\t\tcallback();\r\n\t\t\t} else if (!isNumber(value)) {\r\n\t\t\t\tcallback(new Error(\"请输入数字\"));\r\n\t\t\t} else {\r\n\t\t\t\tcallback();\r\n\t\t\t}\r\n\t\t};\r\n\t\tvar validateIntNumber = (rule, value, callback) => {\r\n\t\t\tif(!value){\r\n\t\t\t\tcallback();\r\n\t\t\t} else if (!isIntNumer(value)) {\r\n\t\t\t\tcallback(new Error(\"请输入整数\"));\r\n\t\t\t} else {\r\n\t\t\t\tcallback();\r\n\t\t\t}\r\n\t\t};\r\n\t\treturn {\r\n\t\t\tid: '',\r\n\t\t\ttype: '',\r\n\t\t\t\r\n\t\t\t\r\n\t\t\tro:{\r\n\t\t\t\tyuefen : false,\r\n\t\t\t\tgonghao : false,\r\n\t\t\t\txingming : false,\r\n\t\t\t\tbumen : false,\r\n\t\t\t\tzhiwei : false,\r\n\t\t\t\tjibengongzi : false,\r\n\t\t\t\tjiabangongzi : false,\r\n\t\t\t\tjixiaojine : false,\r\n\t\t\t\tkoukuanjine : false,\r\n\t\t\t\tqitabuzhu : false,\r\n\t\t\t\tkoukuanyuanyin : false,\r\n\t\t\t\tshifagongzi : false,\r\n\t\t\t\tfaburiqi : false,\r\n\t\t\t\tfuli : false,\r\n\t\t\t\tispay : false,\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t\r\n\t\t\truleForm: {\r\n\t\t\t\tyuefen: '',\r\n\t\t\t\tgonghao: '',\r\n\t\t\t\txingming: '',\r\n\t\t\t\tbumen: '',\r\n\t\t\t\tzhiwei: '',\r\n\t\t\t\tjibengongzi: '',\r\n\t\t\t\tjiabangongzi: '',\r\n\t\t\t\tjixiaojine: '',\r\n\t\t\t\tkoukuanjine: '',\r\n\t\t\t\tqitabuzhu: '',\r\n\t\t\t\tkoukuanyuanyin: '',\r\n\t\t\t\tshifagongzi: '',\r\n\t\t\t\tfaburiqi: '',\r\n\t\t\t\tfuli: '',\r\n\t\t\t},\r\n\t\t\r\n\t\t\tyuefenOptions: [],\r\n\t\t\tgonghaoOptions: [],\r\n\r\n\t\t\t\r\n\t\t\trules: {\r\n\t\t\t\tyuefen: [\r\n\t\t\t\t\t{ required: true, message: '月份不能为空', trigger: 'blur' },\r\n\t\t\t\t],\r\n\t\t\t\tgonghao: [\r\n\t\t\t\t\t{ required: true, message: '工号不能为空', trigger: 'blur' },\r\n\t\t\t\t],\r\n\t\t\t\txingming: [\r\n\t\t\t\t\t{ required: true, message: '姓名不能为空', trigger: 'blur' },\r\n\t\t\t\t],\r\n\t\t\t\tbumen: [\r\n\t\t\t\t\t{ required: true, message: '部门不能为空', trigger: 'blur' },\r\n\t\t\t\t],\r\n\t\t\t\tzhiwei: [\r\n\t\t\t\t],\r\n\t\t\t\tjibengongzi: [\r\n\t\t\t\t\t{ required: true, message: '基本工资不能为空', trigger: 'blur' },\r\n\t\t\t\t\t{ validator: validateNumber, trigger: 'blur' },\r\n\t\t\t\t],\r\n\t\t\t\tjiabangongzi: [\r\n\t\t\t\t\t{ required: true, message: '加班工资不能为空', trigger: 'blur' },\r\n\t\t\t\t\t{ validator: validateNumber, trigger: 'blur' },\r\n\t\t\t\t],\r\n\t\t\t\tjixiaojine: [\r\n\t\t\t\t\t{ required: true, message: '绩效金额不能为空', trigger: 'blur' },\r\n\t\t\t\t\t{ validator: validateNumber, trigger: 'blur' },\r\n\t\t\t\t],\r\n\t\t\t\tkoukuanjine: [\r\n\t\t\t\t\t{ required: true, message: '扣款金额不能为空', trigger: 'blur' },\r\n\t\t\t\t\t{ validator: validateNumber, trigger: 'blur' },\r\n\t\t\t\t],\r\n\t\t\t\tqitabuzhu: [\r\n\t\t\t\t\t{ required: true, message: '其他补助不能为空', trigger: 'blur' },\r\n\t\t\t\t\t{ validator: validateNumber, trigger: 'blur' },\r\n\t\t\t\t],\r\n\t\t\t\tkoukuanyuanyin: [\r\n\t\t\t\t\t{ required: true, message: '扣款原因不能为空', trigger: 'blur' },\r\n\t\t\t\t],\r\n\t\t\t\tshifagongzi: [\r\n\t\t\t\t\t{ validator: validateNumber, trigger: 'blur' },\r\n\t\t\t\t],\r\n\t\t\t\tfaburiqi: [\r\n\t\t\t\t],\r\n\t\t\t\tfuli: [\r\n\t\t\t\t],\r\n\t\t\t\tispay: [\r\n\t\t\t\t],\r\n\t\t\t}\r\n\t\t};\r\n\t},\r\n\tprops: [\"parent\"],\r\n\tcomputed: {\r\n\t\tshifagongzi: {\r\n\t\t\tget: function () {\r\n\t\t\t\treturn 0+parseFloat(this.ruleForm.jibengongzi==\"\"?0:this.ruleForm.jibengongzi)+parseFloat(this.ruleForm.jiabangongzi==\"\"?0:this.ruleForm.jiabangongzi)+parseFloat(this.ruleForm.jixiaojine==\"\"?0:this.ruleForm.jixiaojine)-parseFloat(this.ruleForm.koukuanjine==\"\"?0:this.ruleForm.koukuanjine)+parseFloat(this.ruleForm.qitabuzhu==\"\"?0:this.ruleForm.qitabuzhu) || 0\r\n\t\t\t}\r\n\t\t},\r\n\r\n\r\n\r\n\t},\r\n    components: {\r\n    },\r\n\tcreated() {\r\n\t\tthis.ruleForm.faburiqi = this.getCurDate()\r\n\t},\r\n\tmethods: {\r\n\t\t\r\n\t\t// 下载\r\n\t\tdownload(file){\r\n\t\t\twindow.open(`${file}`)\r\n\t\t},\r\n\t\t// 初始�?\r\n\t\tinit(id,type) {\r\n\t\t\tif (id) {\r\n\t\t\t\tthis.id = id;\r\n\t\t\t\tthis.type = type;\r\n\t\t\t}\r\n\t\t\tif(this.type=='info'||this.type=='else'){\r\n\t\t\t\tthis.info(id);\r\n\t\t\t}else if(this.type=='logistics'){\r\n\t\t\t\tthis.logistics=false;\r\n\t\t\t\tthis.info(id);\r\n\t\t\t}else if(this.type=='cross'){\r\n\t\t\t\tvar obj = this.$storage.getObj('crossObj');\r\n\t\t\t\tfor (var o in obj){\r\n\t\t\t\t\t\tif(o=='yuefen'){\r\n\t\t\t\t\t\t\tthis.ruleForm.yuefen = obj[o];\r\n\t\t\t\t\t\t\tthis.ro.yuefen = true;\r\n\t\t\t\t\t\t\tcontinue;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif(o=='gonghao'){\r\n\t\t\t\t\t\t\tthis.ruleForm.gonghao = obj[o];\r\n\t\t\t\t\t\t\tthis.ro.gonghao = true;\r\n\t\t\t\t\t\t\tcontinue;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif(o=='xingming'){\r\n\t\t\t\t\t\t\tthis.ruleForm.xingming = obj[o];\r\n\t\t\t\t\t\t\tthis.ro.xingming = true;\r\n\t\t\t\t\t\t\tcontinue;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif(o=='bumen'){\r\n\t\t\t\t\t\t\tthis.ruleForm.bumen = obj[o];\r\n\t\t\t\t\t\t\tthis.ro.bumen = true;\r\n\t\t\t\t\t\t\tcontinue;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif(o=='zhiwei'){\r\n\t\t\t\t\t\t\tthis.ruleForm.zhiwei = obj[o];\r\n\t\t\t\t\t\t\tthis.ro.zhiwei = true;\r\n\t\t\t\t\t\t\tcontinue;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif(o=='jibengongzi'){\r\n\t\t\t\t\t\t\tthis.ruleForm.jibengongzi = obj[o];\r\n\t\t\t\t\t\t\tthis.ro.jibengongzi = true;\r\n\t\t\t\t\t\t\tcontinue;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif(o=='jiabangongzi'){\r\n\t\t\t\t\t\t\tthis.ruleForm.jiabangongzi = obj[o];\r\n\t\t\t\t\t\t\tthis.ro.jiabangongzi = true;\r\n\t\t\t\t\t\t\tcontinue;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif(o=='jixiaojine'){\r\n\t\t\t\t\t\t\tthis.ruleForm.jixiaojine = obj[o];\r\n\t\t\t\t\t\t\tthis.ro.jixiaojine = true;\r\n\t\t\t\t\t\t\tcontinue;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif(o=='koukuanjine'){\r\n\t\t\t\t\t\t\tthis.ruleForm.koukuanjine = obj[o];\r\n\t\t\t\t\t\t\tthis.ro.koukuanjine = true;\r\n\t\t\t\t\t\t\tcontinue;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif(o=='qitabuzhu'){\r\n\t\t\t\t\t\t\tthis.ruleForm.qitabuzhu = obj[o];\r\n\t\t\t\t\t\t\tthis.ro.qitabuzhu = true;\r\n\t\t\t\t\t\t\tcontinue;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif(o=='koukuanyuanyin'){\r\n\t\t\t\t\t\t\tthis.ruleForm.koukuanyuanyin = obj[o];\r\n\t\t\t\t\t\t\tthis.ro.koukuanyuanyin = true;\r\n\t\t\t\t\t\t\tcontinue;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif(o=='shifagongzi'){\r\n\t\t\t\t\t\t\tthis.ruleForm.shifagongzi = obj[o];\r\n\t\t\t\t\t\t\tthis.ro.shifagongzi = true;\r\n\t\t\t\t\t\t\tcontinue;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif(o=='faburiqi'){\r\n\t\t\t\t\t\t\tthis.ruleForm.faburiqi = obj[o];\r\n\t\t\t\t\t\t\tthis.ro.faburiqi = true;\r\n\t\t\t\t\t\t\tcontinue;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif(o=='fuli'){\r\n\t\t\t\t\t\t\tthis.ruleForm.fuli = obj[o];\r\n\t\t\t\t\t\t\tthis.ro.fuli = true;\r\n\t\t\t\t\t\t\tcontinue;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t// 获取用户信息\r\n\t\t\tthis.$http({\r\n\t\t\t\turl: `${this.$storage.get('sessionTable')}/session`,\r\n\t\t\t\tmethod: \"get\"\r\n\t\t\t}).then(({ data }) => {\r\n\t\t\t\tif (data && data.code === 0) {\r\n\t\t\t\t\t\r\n\t\t\t\t\tvar json = data.data;\r\n\t\t\t\t\tif(((json.gonghao!=''&&json.gonghao) || json.gonghao==0) && this.$storage.get(\"role\")!=\"管理员\"){\r\n\t\t\t\t\t\tthis.ruleForm.gonghao = json.gonghao\r\n\t\t\t\t\t\tthis.ro.gonghao = true;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif(((json.xingming!=''&&json.xingming) || json.xingming==0) && this.$storage.get(\"role\")!=\"管理员\"){\r\n\t\t\t\t\t\tthis.ruleForm.xingming = json.xingming\r\n\t\t\t\t\t\tthis.ro.xingming = true;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif(((json.bumen!=''&&json.bumen) || json.bumen==0) && this.$storage.get(\"role\")!=\"管理员\"){\r\n\t\t\t\t\t\tthis.ruleForm.bumen = json.bumen\r\n\t\t\t\t\t\tthis.ro.bumen = true;\r\n\t\t\t\t\t}\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.$message.error(data.msg);\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t\t\r\n            this.yuefenOptions = \"一月,二月,三月,四月,五月,六月,七月,八月,九月,十月,十一月,十二月\".split(',')\r\n            this.$http({\r\n\t\t\t\turl: `option/yuangong/gonghao`,\r\n\t\t\t\tmethod: \"get\"\r\n            }).then(({ data }) => {\r\n\t\t\t\tif (data && data.code === 0) {\r\n\t\t\t\t\tthis.gonghaoOptions = data.data;\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.$message.error(data.msg);\r\n\t\t\t\t}\r\n            });\r\n\t\t\t\r\n\t\t},\r\n\t\t\t// 下二�?\r\n\t\t\tgonghaoChange () {\r\n\t\t\t\tthis.$http({\r\n\t\t\t\t\turl: `follow/yuangong/gonghao?columnValue=`+ this.ruleForm.gonghao,\r\n\t\t\t\t\tmethod: \"get\"\r\n\t\t\t\t}).then(({ data }) => {\r\n\t\t\t\t\tif (data && data.code === 0) {\r\n\t\t\t\t\t\tif(data.data.xingming){\r\n\t\t\t\t\t\t\tthis.ruleForm.xingming = data.data.xingming\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif(data.data.bumen){\r\n\t\t\t\t\t\t\tthis.ruleForm.bumen = data.data.bumen\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif(data.data.zhiwei){\r\n\t\t\t\t\t\t\tthis.ruleForm.zhiwei = data.data.zhiwei\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tthis.$message.error(data.msg);\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n    // 多级联动参数\r\n\r\n    info(id) {\r\n      this.$http({\r\n        url: `yuangonggongzi/info/${id}`,\r\n        method: \"get\"\r\n      }).then(({ data }) => {\r\n        if (data && data.code === 0) {\r\n        this.ruleForm = data.data;\r\n        //解决前台上传图片后台不显示的问题\r\n        let reg=new RegExp('../../../upload','g')//g代表全部\r\n        } else {\r\n          this.$message.error(data.msg);\r\n        }\r\n      });\r\n    },\r\n\r\n\r\n    // 提交\r\n    onSubmit() {\r\n        this.ruleForm.shifagongzi = this.shifagongzi\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\nvar objcross = this.$storage.getObj('crossObj');\r\n      //更新跨表属�?\r\n       var crossuserid;\r\n       var crossrefid;\r\n       var crossoptnum;\r\n       if(this.type=='cross'){\r\n                var statusColumnName = this.$storage.get('statusColumnName');\r\n                var statusColumnValue = this.$storage.get('statusColumnValue');\r\n                if(statusColumnName!='') {\r\n                        var obj = this.$storage.getObj('crossObj');\r\n                       if(statusColumnName && !statusColumnName.startsWith(\"[\")) {\r\n                               for (var o in obj){\r\n                                 if(o==statusColumnName){\r\n                                   obj[o] = statusColumnValue;\r\n                                 }\r\n                               }\r\n                               var table = this.$storage.get('crossTable');\r\n                             this.$http({\r\n                                 url: `${table}/update`,\r\n                                 method: \"post\",\r\n                                 data: obj\r\n                               }).then(({ data }) => {});\r\n                       } else {\r\n                               crossuserid=this.$storage.get('userid');\r\n                               crossrefid=obj['id'];\r\n                               crossoptnum=this.$storage.get('statusColumnName');\r\n                               crossoptnum=crossoptnum.replace(/\\[/,\"\").replace(/\\]/,\"\");\r\n                        }\r\n                }\r\n        }\r\n\t\tthis.$refs[\"ruleForm\"].validate(valid => {\r\n\t\t\tif (valid) {\r\n\t\t\t\tif(crossrefid && crossuserid) {\r\n\t\t\t\t\tthis.ruleForm.crossuserid = crossuserid;\r\n\t\t\t\t\tthis.ruleForm.crossrefid = crossrefid;\r\n\t\t\t\t\tlet params = { \r\n\t\t\t\t\t\tpage: 1, \r\n\t\t\t\t\t\tlimit: 10, \r\n\t\t\t\t\t\tcrossuserid:this.ruleForm.crossuserid,\r\n\t\t\t\t\t\tcrossrefid:this.ruleForm.crossrefid,\r\n\t\t\t\t\t} \r\n\t\t\t\tthis.$http({ \r\n\t\t\t\t\turl: \"yuangonggongzi/page\", \r\n\t\t\t\t\tmethod: \"get\", \r\n\t\t\t\t\tparams: params \r\n\t\t\t\t}).then(({ \r\n\t\t\t\t\tdata \r\n\t\t\t\t}) => { \r\n\t\t\t\t\tif (data && data.code === 0) { \r\n\t\t\t\t\t\tif(data.data.total>=crossoptnum) {\r\n\t\t\t\t\t\t\tthis.$message.error(this.$storage.get('tips'));\r\n\t\t\t\t\t\t\treturn false;\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tthis.$http({\r\n\t\t\t\t\t\t\t\turl: `yuangonggongzi/${!this.ruleForm.id ? \"save\" : \"update\"}`,\r\n\t\t\t\t\t\t\t\tmethod: \"post\",\r\n\t\t\t\t\t\t\t\tdata: this.ruleForm\r\n\t\t\t\t\t\t\t}).then(({ data }) => {\r\n\t\t\t\t\t\t\t\tif (data && data.code === 0) {\r\n\t\t\t\t\t\t\t\t\tthis.$message({\r\n\t\t\t\t\t\t\t\t\t\tmessage: \"操作成功\",\r\n\t\t\t\t\t\t\t\t\t\ttype: \"success\",\r\n\t\t\t\t\t\t\t\t\t\tduration: 1500,\r\n\t\t\t\t\t\t\t\t\t\tonClose: () => {\r\n\t\t\t\t\t\t\t\t\t\t\tthis.parent.showFlag = true;\r\n\t\t\t\t\t\t\t\t\t\t\tthis.parent.addOrUpdateFlag = false;\r\n\t\t\t\t\t\t\t\t\t\t\tthis.parent.yuangonggongziCrossAddOrUpdateFlag = false;\r\n\t\t\t\t\t\t\t\t\t\t\tthis.parent.search();\r\n\t\t\t\t\t\t\t\t\t\t\tthis.parent.contentStyleChange();\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\tthis.$message.error(data.msg);\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t});\r\n\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t} else { \r\n\t\t\t\t} \r\n\t\t\t});\r\n\t\t} else {\r\n\t\t\tthis.$http({\r\n\t\t\t\turl: `yuangonggongzi/${!this.ruleForm.id ? \"save\" : \"update\"}`,\r\n\t\t\t\tmethod: \"post\",\r\n\t\t\t   data: this.ruleForm\r\n\t\t\t}).then(({ data }) => {\r\n\t\t\t\tif (data && data.code === 0) {\r\n\t\t\t\t\tthis.$message({\r\n\t\t\t\t\t\tmessage: \"操作成功\",\r\n\t\t\t\t\t\ttype: \"success\",\r\n\t\t\t\t\t\tduration: 1500,\r\n\t\t\t\t\t\tonClose: () => {\r\n\t\t\t\t\t\t\tthis.parent.showFlag = true;\r\n\t\t\t\t\t\t\tthis.parent.addOrUpdateFlag = false;\r\n\t\t\t\t\t\t\tthis.parent.yuangonggongziCrossAddOrUpdateFlag = false;\r\n\t\t\t\t\t\t\tthis.parent.search();\r\n\t\t\t\t\t\t\tthis.parent.contentStyleChange();\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t});\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.$message.error(data.msg);\r\n\t\t\t   }\r\n\t\t\t});\r\n\t\t }\r\n         }\r\n       });\r\n    },\r\n    // 获取uuid\r\n    getUUID () {\r\n      return new Date().getTime();\r\n    },\r\n    // 返回\r\n    back() {\r\n      this.parent.showFlag = true;\r\n      this.parent.addOrUpdateFlag = false;\r\n      this.parent.yuangonggongziCrossAddOrUpdateFlag = false;\r\n      this.parent.contentStyleChange();\r\n    },\r\n  }\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n\t.amap-wrapper {\r\n\t\twidth: 100%;\r\n\t\theight: 500px;\r\n\t}\r\n\t\r\n\t.search-box {\r\n\t\tposition: absolute;\r\n\t}\r\n\t\r\n\t.el-date-editor.el-input {\r\n\t\twidth: auto;\r\n\t}\r\n\t\r\n\t.add-update-preview .el-form-item :deep .el-form-item__label {\r\n\t  \t  padding: 0 10px 0 0;\r\n\t  \t  color: #374254;\r\n\t  \t  font-weight: 600;\r\n\t  \t  width: 80px;\r\n\t  \t  font-size: 14px;\r\n\t  \t  line-height: 40px;\r\n\t  \t  text-align: right;\r\n\t  \t}\r\n\t\r\n\t.add-update-preview .el-form-item :deep .el-form-item__content {\r\n\t  margin-left: 80px;\r\n\t}\r\n\t\r\n\t.add-update-preview .el-input :deep .el-input__inner {\r\n\t  \t  border: 1px solid rgba(167, 180, 201,.3);\r\n\t  \t  border-radius: 4px;\r\n\t  \t  padding: 0 12px;\r\n\t  \t  outline: none;\r\n\t  \t  color: #a7b4c9;\r\n\t  \t  width: 400px;\r\n\t  \t  font-size: 14px;\r\n\t  \t  height: 40px;\r\n\t  \t}\r\n\t.add-update-preview .el-input-number :deep .el-input__inner {\r\n\t\ttext-align: left;\r\n\t  \t  border: 1px solid rgba(167, 180, 201,.3);\r\n\t  \t  border-radius: 4px;\r\n\t  \t  padding: 0 12px;\r\n\t  \t  outline: none;\r\n\t  \t  color: #a7b4c9;\r\n\t  \t  width: 400px;\r\n\t  \t  font-size: 14px;\r\n\t  \t  height: 40px;\r\n\t  \t}\r\n\t.add-update-preview .el-input-number :deep .el-input-number__decrease {\r\n\t\tdisplay: none;\r\n\t}\r\n\t.add-update-preview .el-input-number :deep .el-input-number__increase {\r\n\t\tdisplay: none;\r\n\t}\r\n\t\r\n\t.add-update-preview .el-select :deep .el-input__inner {\r\n\t  \t  border:  1px solid rgba(167, 180, 201,.3) ;\r\n\t  \t  border-radius: 4px;\r\n\t  \t  padding: 0 10px;\r\n\t  \t  outline: none;\r\n\t  \t  color: #a7b4c9;\r\n\t  \t  width: 400px;\r\n\t  \t  font-size: 14px;\r\n\t  \t  height: 40px;\r\n\t  \t}\r\n\t\r\n\t.add-update-preview .el-date-editor :deep .el-input__inner {\r\n\t  \t  border:  1px solid rgba(167, 180, 201,.3) ;\r\n\t  \t  border-radius: 4px;\r\n\t  \t  padding: 0 10px 0 30px;\r\n\t  \t  outline: none;\r\n\t  \t  color: #a7b4c9;\r\n\t  \t  width: 400px;\r\n\t  \t  font-size: 14px;\r\n\t  \t  height: 40px;\r\n\t  \t}\r\n\t\r\n\t.add-update-preview :deep .el-upload--picture-card {\r\n\t\tbackground: transparent;\r\n\t\tborder: 0;\r\n\t\tborder-radius: 0;\r\n\t\twidth: auto;\r\n\t\theight: auto;\r\n\t\tline-height: initial;\r\n\t\tvertical-align: middle;\r\n\t}\r\n\t\r\n\t.add-update-preview :deep .upload .upload-img {\r\n\t  \t  border: 2px dashed rgba(167, 180, 201,.3) ;\r\n\t  \t  cursor: pointer;\r\n\t  \t  border-radius: 6px;\r\n\t  \t  color: #a7b4c9  ;\r\n\t  \t  width: 200px;\r\n\t  \t  font-size: 32px;\r\n\t  \t  line-height: 200px;\r\n\t  \t  text-align: center;\r\n\t  \t  height: 200px;\r\n\t  \t}\r\n\t\r\n\t.add-update-preview :deep .el-upload-list .el-upload-list__item {\r\n\t  \t  border: 2px dashed rgba(167, 180, 201,.3) ;\r\n\t  \t  cursor: pointer;\r\n\t  \t  border-radius: 6px;\r\n\t  \t  color: #a7b4c9  ;\r\n\t  \t  width: 200px;\r\n\t  \t  font-size: 32px;\r\n\t  \t  line-height: 200px;\r\n\t  \t  text-align: center;\r\n\t  \t  height: 200px;\r\n\t  \t}\r\n\t\r\n\t.add-update-preview :deep .el-upload .el-icon-plus {\r\n\t  \t  border: 2px dashed rgba(167, 180, 201,.3) ;\r\n\t  \t  cursor: pointer;\r\n\t  \t  border-radius: 6px;\r\n\t  \t  color: #a7b4c9  ;\r\n\t  \t  width: 200px;\r\n\t  \t  font-size: 32px;\r\n\t  \t  line-height: 200px;\r\n\t  \t  text-align: center;\r\n\t  \t  height: 200px;\r\n\t  \t}\r\n\t\r\n\t.add-update-preview .el-textarea :deep .el-textarea__inner {\r\n\t  \t  border: 1px solid rgba(167, 180, 201,.3)     ;\r\n\t  \t  border-radius: 4px;\r\n\t  \t  padding: 12px;\r\n\t  \t  outline: none;\r\n\t  \t  color: #a7b4c9 ;\r\n\t  \t  width: 400px;\r\n\t  \t  font-size: 14px;\r\n\t  \t  height: 120px;\r\n\t  \t}\r\n\t\r\n\t.add-update-preview .btn .btn1 {\r\n\t\t\t\tborder: 0;\r\n\t\t\t\tcursor: pointer;\r\n\t\t\t\tborder-radius: 4px;\r\n\t\t\t\tpadding: 0 24px;\r\n\t\t\t\tmargin: 4px;\r\n\t\t\t\toutline: none;\r\n\t\t\t\tcolor: #fff;\r\n\t\t\t\tbackground: #6574cd;\r\n\t\t\t\twidth: auto;\r\n\t\t\t\tfont-size: 14px;\r\n\t\t\t\theight: 40px;\r\n\t\t\t}\r\n\t\r\n\t.add-update-preview .btn .btn1:hover {\r\n\t\t\t\topacity: 0.8;\r\n\t\t\t}\r\n\t\r\n\t.add-update-preview .btn .btn2 {\r\n\t\t\t\tborder: 0;\r\n\t\t\t\tcursor: pointer;\r\n\t\t\t\tborder-radius: 4px;\r\n\t\t\t\tpadding: 0 24px;\r\n\t\t\t\tmargin: 4px;\r\n\t\t\t\toutline: none;\r\n\t\t\t\tcolor: #fff;\r\n\t\t\t\tbackground: #6574cd;\r\n\t\t\t\twidth: auto;\r\n\t\t\t\tfont-size: 14px;\r\n\t\t\t\theight: 40px;\r\n\t\t\t}\r\n\t\r\n\t.add-update-preview .btn .btn2:hover {\r\n\t\t\t\topacity: 0.8;\r\n\t\t\t}\r\n\t\r\n\t.add-update-preview .btn .btn3 {\r\n\t\t\t\tborder: 0;\r\n\t\t\t\tcursor: pointer;\r\n\t\t\t\tborder-radius: 4px;\r\n\t\t\t\tpadding: 0 24px;\r\n\t\t\t\tmargin: 4px;\r\n\t\t\t\toutline: none;\r\n\t\t\t\tcolor: #fff;\r\n\t\t\t\tbackground: #ff2b88;\r\n\t\t\t\twidth: auto;\r\n\t\t\t\tfont-size: 14px;\r\n\t\t\t\theight: 40px;\r\n\t\t\t}\r\n\t\r\n\t.add-update-preview .btn .btn3:hover {\r\n\t\t\t\topacity: 0.8;\r\n\t\t\t}\r\n\t\r\n\t.add-update-preview .btn .btn4 {\r\n\t\t\t\tborder: 0;\r\n\t\t\t\tcursor: pointer;\r\n\t\t\t\tborder-radius: 4px;\r\n\t\t\t\tpadding: 0 24px;\r\n\t\t\t\tmargin: 4px;\r\n\t\t\t\toutline: none;\r\n\t\t\t\tcolor: #fff;\r\n\t\t\t\tbackground: #ff2b88;\r\n\t\t\t\twidth: auto;\r\n\t\t\t\tfont-size: 14px;\r\n\t\t\t\theight: 40px;\r\n\t\t\t}\r\n\t\r\n\t.add-update-preview .btn .btn4:hover {\r\n\t\t\t\topacity: 0.8;\r\n\t\t\t}\r\n\t\r\n\t.add-update-preview .btn .btn5 {\r\n\t\t\t\tborder: 0;\r\n\t\t\t\tcursor: pointer;\r\n\t\t\t\tborder-radius: 4px;\r\n\t\t\t\tpadding: 0 24px;\r\n\t\t\t\tmargin: 4px;\r\n\t\t\t\toutline: none;\r\n\t\t\t\tcolor: #fff;\r\n\t\t\t\tbackground: #ff2b88;\r\n\t\t\t\twidth: auto;\r\n\t\t\t\tfont-size: 14px;\r\n\t\t\t\theight: 40px;\r\n\t\t\t}\r\n\t\r\n\t.add-update-preview .btn .btn5:hover {\r\n\t\t\t\topacity: 0.8;\r\n\t\t\t}\r\n</style>\r\n"]}]}