{"remainingRequest": "E:\\java系统\\公司财务系统\\src\\main\\resources\\admin\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\java系统\\公司财务系统\\src\\main\\resources\\admin\\admin\\src\\views\\modules\\caiwuxinxi\\add-or-update.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\java系统\\公司财务系统\\src\\main\\resources\\admin\\admin\\src\\views\\modules\\caiwuxinxi\\add-or-update.vue", "mtime": 1754641987395}, {"path": "E:\\java系统\\公司财务系统\\src\\main\\resources\\admin\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754805254356}, {"path": "E:\\java系统\\公司财务系统\\src\\main\\resources\\admin\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754805257322}, {"path": "E:\\java系统\\公司财务系统\\src\\main\\resources\\admin\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754805254356}, {"path": "E:\\java系统\\公司财务系统\\src\\main\\resources\\admin\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754628160828}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["add-or-update.vue"], "names": [], "mappings": ";AAqIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;AAYA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;AAYA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "add-or-update.vue", "sourceRoot": "src/views/modules/caiwuxinxi", "sourcesContent": ["<template>\r\n\t<div class=\"addEdit-block\" :style='{\"padding\":\"30px\",\"margin\":\"0\"}'>\r\n\t\t<el-form\r\n\t\t\t:style='{\"border\":\"1px solid rgba(167, 180, 201,.3)  \",\"padding\":\"30px\",\"borderRadius\":\"6px\",\"background\":\"#fff\"}'\r\n\t\t\tclass=\"add-update-preview\"\r\n\t\t\tref=\"ruleForm\"\r\n\t\t\t:model=\"ruleForm\"\r\n\t\t\t:rules=\"rules\"\r\n\t\t\tlabel-width=\"80px\"\r\n\t\t>\r\n\t\t\t<template >\r\n\t\t\t\t<el-form-item :style='{\"margin\":\"0 0 20px 0\"}' class=\"input\" v-if=\"type!='info'\" label=\"统计编号\" prop=\"tongjibianhao\">\r\n\t\t\t\t\t<el-input v-model=\"ruleForm.tongjibianhao\" placeholder=\"统计编号\" readonly></el-input>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<el-form-item :style='{\"margin\":\"0 0 20px 0\"}' class=\"input\" v-else-if=\"ruleForm.tongjibianhao\" label=\"统计编号\" prop=\"tongjibianhao\">\r\n\t\t\t\t\t<el-input v-model=\"ruleForm.tongjibianhao\" placeholder=\"统计编号\" readonly></el-input>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<el-form-item :style='{\"margin\":\"0 0 20px 0\"}' class=\"select\" v-if=\"type!='info'\"  label=\"月份\" prop=\"yuefen\">\r\n\t\t\t\t\t<el-select :disabled=\"ro.yuefen\" v-model=\"ruleForm.yuefen\" placeholder=\"请选择月份\" >\r\n\t\t\t\t\t\t<el-option\r\n\t\t\t\t\t\t\tv-for=\"(item,index) in yuefenOptions\"\r\n\t\t\t\t\t\t\tv-bind:key=\"index\"\r\n\t\t\t\t\t\t\t:label=\"item\"\r\n\t\t\t\t\t\t\t:value=\"item\">\r\n\t\t\t\t\t\t</el-option>\r\n\t\t\t\t\t</el-select>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<el-form-item :style='{\"margin\":\"0 0 20px 0\"}' v-else class=\"input\" label=\"月份\" prop=\"yuefen\">\r\n\t\t\t\t\t<el-input v-model=\"ruleForm.yuefen\"\r\n\t\t\t\t\t\tplaceholder=\"月份\" readonly></el-input>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<el-form-item :style='{\"margin\":\"0 0 20px 0\"}' class=\"input\" v-if=\"type!='info'\"  label=\"收入金额\" prop=\"shourujine\">\r\n\t\t\t\t\t<el-input-number v-model=\"ruleForm.shourujine\" placeholder=\"收入金额\" :readonly=\"ro.shourujine\"></el-input-number>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<el-form-item :style='{\"margin\":\"0 0 20px 0\"}' v-else class=\"input\" label=\"收入金额\" prop=\"shourujine\">\r\n\t\t\t\t\t<el-input v-model=\"ruleForm.shourujine\" placeholder=\"收入金额\" readonly></el-input>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<el-form-item :style='{\"margin\":\"0 0 20px 0\"}' class=\"input\" v-if=\"type!='info'\"  label=\"支出金额\" prop=\"zhichujine\">\r\n\t\t\t\t\t<el-input-number v-model=\"ruleForm.zhichujine\" placeholder=\"支出金额\" :readonly=\"ro.zhichujine\"></el-input-number>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<el-form-item :style='{\"margin\":\"0 0 20px 0\"}' v-else class=\"input\" label=\"支出金额\" prop=\"zhichujine\">\r\n\t\t\t\t\t<el-input v-model=\"ruleForm.zhichujine\" placeholder=\"支出金额\" readonly></el-input>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<el-form-item :style='{\"margin\":\"0 0 20px 0\"}' class=\"input\" v-if=\"type!='info'\" label=\"利润\" prop=\"lirun\">\r\n\t\t\t\t\t<el-input v-model=\"lirun\" placeholder=\"利润\" readonly></el-input>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<el-form-item :style='{\"margin\":\"0 0 20px 0\"}' class=\"input\" v-else-if=\"ruleForm.lirun\" label=\"利润\" prop=\"lirun\">\r\n\t\t\t\t\t<el-input v-model=\"ruleForm.lirun\" placeholder=\"利润\" readonly></el-input>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<el-form-item :style='{\"margin\":\"0 0 20px 0\"}' class=\"date\" v-if=\"type!='info'\" label=\"登记日期\" prop=\"dengjiriqi\">\r\n\t\t\t\t\t<el-date-picker\r\n\t\t\t\t\t\tformat=\"yyyy 年 MM 月 dd 日\"\r\n\t\t\t\t\t\tvalue-format=\"yyyy-MM-dd\"\r\n\t\t\t\t\t\tv-model=\"ruleForm.dengjiriqi\"\r\n\t\t\t\t\t\ttype=\"date\"\r\n\t\t\t\t\t\t:readonly=\"ro.dengjiriqi\"\r\n\t\t\t\t\t\tplaceholder=\"登记日期\"\r\n\t\t\t\t\t></el-date-picker>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<el-form-item :style='{\"margin\":\"0 0 20px 0\"}' class=\"input\" v-else-if=\"ruleForm.dengjiriqi\" label=\"登记日期\" prop=\"dengjiriqi\">\r\n\t\t\t\t\t<el-input v-model=\"ruleForm.dengjiriqi\" placeholder=\"登记日期\" readonly></el-input>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<el-form-item :style='{\"margin\":\"0 0 20px 0\"}' class=\"date\" v-if=\"type!='info'\" label=\"收入日期\" prop=\"shoururiqi\">\r\n\t\t\t\t\t<el-date-picker\r\n\t\t\t\t\t\tformat=\"yyyy 年 MM 月 dd 日\"\r\n\t\t\t\t\t\tvalue-format=\"yyyy-MM-dd\"\r\n\t\t\t\t\t\tv-model=\"ruleForm.shoururiqi\" \r\n\t\t\t\t\t\ttype=\"date\"\r\n\t\t\t\t\t\t:readonly=\"ro.shoururiqi\"\r\n\t\t\t\t\t\tplaceholder=\"收入日期\"\r\n\t\t\t\t\t></el-date-picker> \r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<el-form-item :style='{\"margin\":\"0 0 20px 0\"}' class=\"input\" v-else-if=\"ruleForm.shoururiqi\" label=\"收入日期\" prop=\"shoururiqi\">\r\n\t\t\t\t\t<el-input v-model=\"ruleForm.shoururiqi\" placeholder=\"收入日期\" readonly></el-input>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<el-form-item :style='{\"margin\":\"0 0 20px 0\"}' class=\"date\" v-if=\"type!='info'\" label=\"支出时间\" prop=\"zhichushijian\">\r\n\t\t\t\t\t<el-date-picker\r\n\t\t\t\t\t\tformat=\"yyyy 年 MM 月 dd 日\"\r\n\t\t\t\t\t\tvalue-format=\"yyyy-MM-dd\"\r\n\t\t\t\t\t\tv-model=\"ruleForm.zhichushijian\" \r\n\t\t\t\t\t\ttype=\"date\"\r\n\t\t\t\t\t\t:readonly=\"ro.zhichushijian\"\r\n\t\t\t\t\t\tplaceholder=\"支出时间\"\r\n\t\t\t\t\t></el-date-picker> \r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<el-form-item :style='{\"margin\":\"0 0 20px 0\"}' class=\"input\" v-else-if=\"ruleForm.zhichushijian\" label=\"支出时间\" prop=\"zhichushijian\">\r\n\t\t\t\t\t<el-input v-model=\"ruleForm.zhichushijian\" placeholder=\"支出时间\" readonly></el-input>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t</template>\r\n\t\t\t\t<el-form-item :style='{\"margin\":\"0 0 20px 0\"}' class=\"textarea\" v-if=\"type!='info'\" label=\"收入来源\" prop=\"shourulaiyuan\">\r\n\t\t\t\t\t<el-input\r\n\t\t\t\t\t  style=\"min-width: 200px; max-width: 600px;\"\r\n\t\t\t\t\t  type=\"textarea\"\r\n\t\t\t\t\t  :rows=\"8\"\r\n\t\t\t\t\t  placeholder=\"收入来源\"\r\n\t\t\t\t\t  v-model=\"ruleForm.shourulaiyuan\" >\r\n\t\t\t\t\t</el-input>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<el-form-item :style='{\"margin\":\"0 0 20px 0\"}' v-else-if=\"ruleForm.shourulaiyuan\" label=\"收入来源\" prop=\"shourulaiyuan\">\r\n\t\t\t\t\t<span :style='{\"fontSize\":\"14px\",\"lineHeight\":\"40px\",\"color\":\"#333\",\"fontWeight\":\"500\",\"display\":\"inline-block\"}'>{{ruleForm.shourulaiyuan}}</span>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<el-form-item :style='{\"margin\":\"0 0 20px 0\"}' class=\"textarea\" v-if=\"type!='info'\" label=\"支出原因\" prop=\"zhichuyuanyin\">\r\n\t\t\t\t\t<el-input\r\n\t\t\t\t\t  style=\"min-width: 200px; max-width: 600px;\"\r\n\t\t\t\t\t  type=\"textarea\"\r\n\t\t\t\t\t  :rows=\"8\"\r\n\t\t\t\t\t  placeholder=\"支出原因\"\r\n\t\t\t\t\t  v-model=\"ruleForm.zhichuyuanyin\" >\r\n\t\t\t\t\t</el-input>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<el-form-item :style='{\"margin\":\"0 0 20px 0\"}' v-else-if=\"ruleForm.zhichuyuanyin\" label=\"支出原因\" prop=\"zhichuyuanyin\">\r\n\t\t\t\t\t<span :style='{\"fontSize\":\"14px\",\"lineHeight\":\"40px\",\"color\":\"#333\",\"fontWeight\":\"500\",\"display\":\"inline-block\"}'>{{ruleForm.zhichuyuanyin}}</span>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t<el-form-item :style='{\"padding\":\"0\",\"margin\":\"0\"}' class=\"btn\">\r\n\t\t\t\t<el-button class=\"btn3\"  v-if=\"type!='info'\" type=\"success\" @click=\"onSubmit\">\r\n\t\t\t\t\t<span class=\"icon iconfont icon-xihuan\" :style='{\"margin\":\"0 2px\",\"fontSize\":\"14px\",\"color\":\"#fff\",\"height\":\"40px\"}'></span>\r\n\t\t\t\t\t提交\r\n\t\t\t\t</el-button>\r\n\t\t\t\t<el-button class=\"btn4\" v-if=\"type!='info'\" type=\"success\" @click=\"back()\">\r\n\t\t\t\t\t<span class=\"icon iconfont icon-xihuan\" :style='{\"margin\":\"0 2px\",\"fontSize\":\"14px\",\"color\":\"#fff\",\"height\":\"40px\"}'></span>\r\n\t\t\t\t\t取消\r\n\t\t\t\t</el-button>\r\n\t\t\t\t<el-button class=\"btn5\" v-if=\"type=='info'\" type=\"success\" @click=\"back()\">\r\n\t\t\t\t\t<span class=\"icon iconfont icon-xihuan\" :style='{\"margin\":\"0 2px\",\"fontSize\":\"14px\",\"color\":\"#fff\",\"height\":\"40px\"}'></span>\r\n\t\t\t\t\t返回\r\n\t\t\t\t</el-button>\r\n\t\t\t</el-form-item>\r\n\t\t</el-form>\r\n    \r\n\r\n  </div>\r\n</template>\r\n<script>\r\n// 数字，邮件，手机，url，身份证校验\r\nimport { isNumber,isIntNumer,isEmail,isPhone, isMobile,isURL,checkIdCard } from \"@/utils/validate\";\r\nexport default {\r\n\tdata() {\r\n\t\tlet self = this\r\n\t\tvar validateIdCard = (rule, value, callback) => {\r\n\t\t\tif(!value){\r\n\t\t\t\tcallback();\r\n\t\t\t} else if (!checkIdCard(value)) {\r\n\t\t\t\tcallback(new Error(\"请输入正确的身份证号\"));\r\n\t\t\t} else {\r\n\t\t\t\tcallback();\r\n\t\t\t}\r\n\t\t};\r\n\t\tvar validateUrl = (rule, value, callback) => {\r\n\t\t\tif(!value){\r\n\t\t\t\tcallback();\r\n\t\t\t} else if (!isURL(value)) {\r\n\t\t\t\tcallback(new Error(\"请输入正确的URL地址\"));\r\n\t\t\t} else {\r\n\t\t\t\tcallback();\r\n\t\t\t}\r\n\t\t};\r\n\t\tvar validateMobile = (rule, value, callback) => {\r\n\t\t\tif(!value){\r\n\t\t\t\tcallback();\r\n\t\t\t} else if (!isMobile(value)) {\r\n\t\t\t\tcallback(new Error(\"请输入正确的手机号码\"));\r\n\t\t\t} else {\r\n\t\t\t\tcallback();\r\n\t\t\t}\r\n\t\t};\r\n\t\tvar validatePhone = (rule, value, callback) => {\r\n\t\t\tif(!value){\r\n\t\t\t\tcallback();\r\n\t\t\t} else if (!isPhone(value)) {\r\n\t\t\t\tcallback(new Error(\"请输入正确的电话号码\"));\r\n\t\t\t} else {\r\n\t\t\t\tcallback();\r\n\t\t\t}\r\n\t\t};\r\n\t\tvar validateEmail = (rule, value, callback) => {\r\n\t\t\tif(!value){\r\n\t\t\t\tcallback();\r\n\t\t\t} else if (!isEmail(value)) {\r\n\t\t\t\tcallback(new Error(\"请输入正确的邮箱地址\"));\r\n\t\t\t} else {\r\n\t\t\t\tcallback();\r\n\t\t\t}\r\n\t\t};\r\n\t\tvar validateNumber = (rule, value, callback) => {\r\n\t\t\tif(!value){\r\n\t\t\t\tcallback();\r\n\t\t\t} else if (!isNumber(value)) {\r\n\t\t\t\tcallback(new Error(\"请输入数字?\"));\r\n\t\t\t} else {\r\n\t\t\t\tcallback();\r\n\t\t\t}\r\n\t\t};\r\n\t\tvar validateIntNumber = (rule, value, callback) => {\r\n\t\t\tif(!value){\r\n\t\t\t\tcallback();\r\n\t\t\t} else if (!isIntNumer(value)) {\r\n\t\t\t\tcallback(new Error(\"请输入整数?\"));\r\n\t\t\t} else {\r\n\t\t\t\tcallback();\r\n\t\t\t}\r\n\t\t};\r\n\t\treturn {\r\n\t\t\tid: '',\r\n\t\t\ttype: '',\r\n\t\t\t\r\n\t\t\t\r\n\t\t\tro:{\r\n\t\t\t\ttongjibianhao : false,\r\n\t\t\t\tyuefen : false,\r\n\t\t\t\tshourujine : false,\r\n\t\t\t\tzhichujine : false,\r\n\t\t\t\tlirun : false,\r\n\t\t\t\tdengjiriqi : false,\r\n\t\t\t\tshoururiqi : false,\r\n\t\t\t\tshourulaiyuan : false,\r\n\t\t\t\tzhichushijian : false,\r\n\t\t\t\tzhichuyuanyin : false,\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t\r\n\t\t\truleForm: {\r\n\t\t\t\ttongjibianhao: this.getUUID(),\r\n\t\t\t\tyuefen: '',\r\n\t\t\t\tshourujine: '',\r\n\t\t\t\tzhichujine: '',\r\n\t\t\t\tlirun: '',\r\n\t\t\t\tdengjiriqi: '',\r\n\t\t\t\tshoururiqi: '',\r\n\t\t\t\tshourulaiyuan: '',\r\n\t\t\t\tzhichushijian: '',\r\n\t\t\t\tzhichuyuanyin: '',\r\n\t\t\t},\r\n\t\t\r\n\t\t\tyuefenOptions: [],\r\n\r\n\t\t\t\r\n\t\t\trules: {\r\n\t\t\t\ttongjibianhao: [\r\n\t\t\t\t],\r\n\t\t\t\tyuefen: [\r\n\t\t\t\t],\r\n\t\t\t\tshourujine: [\r\n\t\t\t\t\t{ required: true, message: '收入金额不能为空', trigger: 'blur' },\r\n\t\t\t\t\t{ validator: validateNumber, trigger: 'blur' },\r\n\t\t\t\t],\r\n\t\t\t\tzhichujine: [\r\n\t\t\t\t\t{ required: true, message: '支出金额不能为空', trigger: 'blur' },\r\n\t\t\t\t\t{ validator: validateNumber, trigger: 'blur' },\r\n\t\t\t\t],\r\n\t\t\t\tlirun: [\r\n\t\t\t\t\t{ validator: validateNumber, trigger: 'blur' },\r\n\t\t\t\t],\r\n\t\t\t\tdengjiriqi: [\r\n\t\t\t\t],\r\n\t\t\t\tshoururiqi: [\r\n\t\t\t\t],\r\n\t\t\t\tshourulaiyuan: [\r\n\t\t\t\t],\r\n\t\t\t\tzhichushijian: [\r\n\t\t\t\t],\r\n\t\t\t\tzhichuyuanyin: [\r\n\t\t\t\t],\r\n\t\t\t}\r\n\t\t};\r\n\t},\r\n\tprops: [\"parent\"],\r\n\tcomputed: {\r\n\t\tlirun: {\r\n\t\t\tget: function () {\r\n\t\t\t\treturn 0+parseFloat(this.ruleForm.shourujine==\"\"?0:this.ruleForm.shourujine)-parseFloat(this.ruleForm.zhichujine==\"\"?0:this.ruleForm.zhichujine) || 0\r\n\t\t\t}\r\n\t\t},\r\n\r\n\r\n\r\n\t},\r\n    components: {\r\n    },\r\n\tcreated() {\r\n\t\tthis.ruleForm.dengjiriqi = this.getCurDate()\r\n\t},\r\n\tmethods: {\r\n\t\t\r\n\t\t// 下载\r\n\t\tdownload(file){\r\n\t\t\twindow.open(`${file}`)\r\n\t\t},\r\n\t\t// 初始�?\r\n\t\tinit(id,type) {\r\n\t\t\tif (id) {\r\n\t\t\t\tthis.id = id;\r\n\t\t\t\tthis.type = type;\r\n\t\t\t}\r\n\t\t\tif(this.type=='info'||this.type=='else'){\r\n\t\t\t\tthis.info(id);\r\n\t\t\t}else if(this.type=='logistics'){\r\n\t\t\t\tthis.logistics=false;\r\n\t\t\t\tthis.info(id);\r\n\t\t\t}else if(this.type=='cross'){\r\n\t\t\t\tvar obj = this.$storage.getObj('crossObj');\r\n\t\t\t\tfor (var o in obj){\r\n\t\t\t\t\t\tif(o=='tongjibianhao'){\r\n\t\t\t\t\t\t\tthis.ruleForm.tongjibianhao = obj[o];\r\n\t\t\t\t\t\t\tthis.ro.tongjibianhao = true;\r\n\t\t\t\t\t\t\tcontinue;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif(o=='yuefen'){\r\n\t\t\t\t\t\t\tthis.ruleForm.yuefen = obj[o];\r\n\t\t\t\t\t\t\tthis.ro.yuefen = true;\r\n\t\t\t\t\t\t\tcontinue;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif(o=='shourujine'){\r\n\t\t\t\t\t\t\tthis.ruleForm.shourujine = obj[o];\r\n\t\t\t\t\t\t\tthis.ro.shourujine = true;\r\n\t\t\t\t\t\t\tcontinue;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif(o=='zhichujine'){\r\n\t\t\t\t\t\t\tthis.ruleForm.zhichujine = obj[o];\r\n\t\t\t\t\t\t\tthis.ro.zhichujine = true;\r\n\t\t\t\t\t\t\tcontinue;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif(o=='lirun'){\r\n\t\t\t\t\t\t\tthis.ruleForm.lirun = obj[o];\r\n\t\t\t\t\t\t\tthis.ro.lirun = true;\r\n\t\t\t\t\t\t\tcontinue;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif(o=='dengjiriqi'){\r\n\t\t\t\t\t\t\tthis.ruleForm.dengjiriqi = obj[o];\r\n\t\t\t\t\t\t\tthis.ro.dengjiriqi = true;\r\n\t\t\t\t\t\t\tcontinue;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif(o=='shoururiqi'){\r\n\t\t\t\t\t\t\tthis.ruleForm.shoururiqi = obj[o];\r\n\t\t\t\t\t\t\tthis.ro.shoururiqi = true;\r\n\t\t\t\t\t\t\tcontinue;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif(o=='shourulaiyuan'){\r\n\t\t\t\t\t\t\tthis.ruleForm.shourulaiyuan = obj[o];\r\n\t\t\t\t\t\t\tthis.ro.shourulaiyuan = true;\r\n\t\t\t\t\t\t\tcontinue;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif(o=='zhichushijian'){\r\n\t\t\t\t\t\t\tthis.ruleForm.zhichushijian = obj[o];\r\n\t\t\t\t\t\t\tthis.ro.zhichushijian = true;\r\n\t\t\t\t\t\t\tcontinue;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif(o=='zhichuyuanyin'){\r\n\t\t\t\t\t\t\tthis.ruleForm.zhichuyuanyin = obj[o];\r\n\t\t\t\t\t\t\tthis.ro.zhichuyuanyin = true;\r\n\t\t\t\t\t\t\tcontinue;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t// 获取用户信息\r\n\t\t\tthis.$http({\r\n\t\t\t\turl: `${this.$storage.get('sessionTable')}/session`,\r\n\t\t\t\tmethod: \"get\"\r\n\t\t\t}).then(({ data }) => {\r\n\t\t\t\tif (data && data.code === 0) {\r\n\t\t\t\t\t\r\n\t\t\t\t\tvar json = data.data;\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.$message.error(data.msg);\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t\t\r\n            this.yuefenOptions = \"一月,二月,三月,四月,五月,六月,七月,八月,九月,十月,十一月,十二月,\".split(',')\r\n\t\t\t\r\n\t\t},\r\n    // 多级联动参数\r\n\r\n    info(id) {\r\n      this.$http({\r\n        url: `caiwuxinxi/info/${id}`,\r\n        method: \"get\"\r\n      }).then(({ data }) => {\r\n        if (data && data.code === 0) {\r\n        this.ruleForm = data.data;\r\n        //解决前台上传图片后台不显示的问题\r\n        let reg=new RegExp('../../../upload','g')//g代表全部\r\n        } else {\r\n          this.$message.error(data.msg);\r\n        }\r\n      });\r\n    },\r\n\r\n\r\n    // 提交\r\n    onSubmit() {\r\n\t\tif(this.ruleForm.tongjibianhao) {\r\n\t\t\tthis.ruleForm.tongjibianhao = String(this.ruleForm.tongjibianhao)\r\n\t\t}\r\n        this.ruleForm.lirun = this.lirun\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\nvar objcross = this.$storage.getObj('crossObj');\r\n      //更新跨表属�?\r\n       var crossuserid;\r\n       var crossrefid;\r\n       var crossoptnum;\r\n       if(this.type=='cross'){\r\n                var statusColumnName = this.$storage.get('statusColumnName');\r\n                var statusColumnValue = this.$storage.get('statusColumnValue');\r\n                if(statusColumnName!='') {\r\n                        var obj = this.$storage.getObj('crossObj');\r\n                       if(statusColumnName && !statusColumnName.startsWith(\"[\")) {\r\n                               for (var o in obj){\r\n                                 if(o==statusColumnName){\r\n                                   obj[o] = statusColumnValue;\r\n                                 }\r\n                               }\r\n                               var table = this.$storage.get('crossTable');\r\n                             this.$http({\r\n                                 url: `${table}/update`,\r\n                                 method: \"post\",\r\n                                 data: obj\r\n                               }).then(({ data }) => {});\r\n                       } else {\r\n                               crossuserid=this.$storage.get('userid');\r\n                               crossrefid=obj['id'];\r\n                               crossoptnum=this.$storage.get('statusColumnName');\r\n                               crossoptnum=crossoptnum.replace(/\\[/,\"\").replace(/\\]/,\"\");\r\n                        }\r\n                }\r\n        }\r\n\t\tthis.$refs[\"ruleForm\"].validate(valid => {\r\n\t\t\tif (valid) {\r\n\t\t\t\tif(crossrefid && crossuserid) {\r\n\t\t\t\t\tthis.ruleForm.crossuserid = crossuserid;\r\n\t\t\t\t\tthis.ruleForm.crossrefid = crossrefid;\r\n\t\t\t\t\tlet params = { \r\n\t\t\t\t\t\tpage: 1, \r\n\t\t\t\t\t\tlimit: 10, \r\n\t\t\t\t\t\tcrossuserid:this.ruleForm.crossuserid,\r\n\t\t\t\t\t\tcrossrefid:this.ruleForm.crossrefid,\r\n\t\t\t\t\t} \r\n\t\t\t\tthis.$http({ \r\n\t\t\t\t\turl: \"caiwuxinxi/page\", \r\n\t\t\t\t\tmethod: \"get\", \r\n\t\t\t\t\tparams: params \r\n\t\t\t\t}).then(({ \r\n\t\t\t\t\tdata \r\n\t\t\t\t}) => { \r\n\t\t\t\t\tif (data && data.code === 0) { \r\n\t\t\t\t\t\tif(data.data.total>=crossoptnum) {\r\n\t\t\t\t\t\t\tthis.$message.error(this.$storage.get('tips'));\r\n\t\t\t\t\t\t\treturn false;\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tthis.$http({\r\n\t\t\t\t\t\t\t\turl: `caiwuxinxi/${!this.ruleForm.id ? \"save\" : \"update\"}`,\r\n\t\t\t\t\t\t\t\tmethod: \"post\",\r\n\t\t\t\t\t\t\t\tdata: this.ruleForm\r\n\t\t\t\t\t\t\t}).then(({ data }) => {\r\n\t\t\t\t\t\t\t\tif (data && data.code === 0) {\r\n\t\t\t\t\t\t\t\t\tthis.$message({\r\n\t\t\t\t\t\t\t\t\t\tmessage: \"操作成功\",\r\n\t\t\t\t\t\t\t\t\t\ttype: \"success\",\r\n\t\t\t\t\t\t\t\t\t\tduration: 1500,\r\n\t\t\t\t\t\t\t\t\t\tonClose: () => {\r\n\t\t\t\t\t\t\t\t\t\t\tthis.parent.showFlag = true;\r\n\t\t\t\t\t\t\t\t\t\t\tthis.parent.addOrUpdateFlag = false;\r\n\t\t\t\t\t\t\t\t\t\t\tthis.parent.caiwuxinxiCrossAddOrUpdateFlag = false;\r\n\t\t\t\t\t\t\t\t\t\t\tthis.parent.search();\r\n\t\t\t\t\t\t\t\t\t\t\tthis.parent.contentStyleChange();\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\tthis.$message.error(data.msg);\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t});\r\n\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t} else { \r\n\t\t\t\t} \r\n\t\t\t});\r\n\t\t} else {\r\n\t\t\tthis.$http({\r\n\t\t\t\turl: `caiwuxinxi/${!this.ruleForm.id ? \"save\" : \"update\"}`,\r\n\t\t\t\tmethod: \"post\",\r\n\t\t\t   data: this.ruleForm\r\n\t\t\t}).then(({ data }) => {\r\n\t\t\t\tif (data && data.code === 0) {\r\n\t\t\t\t\tthis.$message({\r\n\t\t\t\t\t\tmessage: \"操作成功\",\r\n\t\t\t\t\t\ttype: \"success\",\r\n\t\t\t\t\t\tduration: 1500,\r\n\t\t\t\t\t\tonClose: () => {\r\n\t\t\t\t\t\t\tthis.parent.showFlag = true;\r\n\t\t\t\t\t\t\tthis.parent.addOrUpdateFlag = false;\r\n\t\t\t\t\t\t\tthis.parent.caiwuxinxiCrossAddOrUpdateFlag = false;\r\n\t\t\t\t\t\t\tthis.parent.search();\r\n\t\t\t\t\t\t\tthis.parent.contentStyleChange();\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t});\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.$message.error(data.msg);\r\n\t\t\t   }\r\n\t\t\t});\r\n\t\t }\r\n         }\r\n       });\r\n    },\r\n    // 获取uuid\r\n    getUUID () {\r\n      return new Date().getTime();\r\n    },\r\n    // 返回\r\n    back() {\r\n      this.parent.showFlag = true;\r\n      this.parent.addOrUpdateFlag = false;\r\n      this.parent.caiwuxinxiCrossAddOrUpdateFlag = false;\r\n      this.parent.contentStyleChange();\r\n    },\r\n  }\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n\t.amap-wrapper {\r\n\t\twidth: 100%;\r\n\t\theight: 500px;\r\n\t}\r\n\t\r\n\t.search-box {\r\n\t\tposition: absolute;\r\n\t}\r\n\t\r\n\t.el-date-editor.el-input {\r\n\t\twidth: auto;\r\n\t}\r\n\t\r\n\t.add-update-preview .el-form-item :deep .el-form-item__label {\r\n\t  \t  padding: 0 10px 0 0;\r\n\t  \t  color: #374254;\r\n\t  \t  font-weight: 600;\r\n\t  \t  width: 80px;\r\n\t  \t  font-size: 14px;\r\n\t  \t  line-height: 40px;\r\n\t  \t  text-align: right;\r\n\t  \t}\r\n\t\r\n\t.add-update-preview .el-form-item :deep .el-form-item__content {\r\n\t  margin-left: 80px;\r\n\t}\r\n\t\r\n\t.add-update-preview .el-input :deep .el-input__inner {\r\n\t  \t  border: 1px solid rgba(167, 180, 201,.3);\r\n\t  \t  border-radius: 4px;\r\n\t  \t  padding: 0 12px;\r\n\t  \t  outline: none;\r\n\t  \t  color: #a7b4c9;\r\n\t  \t  width: 400px;\r\n\t  \t  font-size: 14px;\r\n\t  \t  height: 40px;\r\n\t  \t}\r\n\t.add-update-preview .el-input-number :deep .el-input__inner {\r\n\t\ttext-align: left;\r\n\t  \t  border: 1px solid rgba(167, 180, 201,.3);\r\n\t  \t  border-radius: 4px;\r\n\t  \t  padding: 0 12px;\r\n\t  \t  outline: none;\r\n\t  \t  color: #a7b4c9;\r\n\t  \t  width: 400px;\r\n\t  \t  font-size: 14px;\r\n\t  \t  height: 40px;\r\n\t  \t}\r\n\t.add-update-preview .el-input-number :deep .el-input-number__decrease {\r\n\t\tdisplay: none;\r\n\t}\r\n\t.add-update-preview .el-input-number :deep .el-input-number__increase {\r\n\t\tdisplay: none;\r\n\t}\r\n\t\r\n\t.add-update-preview .el-select :deep .el-input__inner {\r\n\t  \t  border:  1px solid rgba(167, 180, 201,.3) ;\r\n\t  \t  border-radius: 4px;\r\n\t  \t  padding: 0 10px;\r\n\t  \t  outline: none;\r\n\t  \t  color: #a7b4c9;\r\n\t  \t  width: 400px;\r\n\t  \t  font-size: 14px;\r\n\t  \t  height: 40px;\r\n\t  \t}\r\n\t\r\n\t.add-update-preview .el-date-editor :deep .el-input__inner {\r\n\t  \t  border:  1px solid rgba(167, 180, 201,.3) ;\r\n\t  \t  border-radius: 4px;\r\n\t  \t  padding: 0 10px 0 30px;\r\n\t  \t  outline: none;\r\n\t  \t  color: #a7b4c9;\r\n\t  \t  width: 400px;\r\n\t  \t  font-size: 14px;\r\n\t  \t  height: 40px;\r\n\t  \t}\r\n\t\r\n\t.add-update-preview :deep .el-upload--picture-card {\r\n\t\tbackground: transparent;\r\n\t\tborder: 0;\r\n\t\tborder-radius: 0;\r\n\t\twidth: auto;\r\n\t\theight: auto;\r\n\t\tline-height: initial;\r\n\t\tvertical-align: middle;\r\n\t}\r\n\t\r\n\t.add-update-preview :deep .upload .upload-img {\r\n\t  \t  border: 2px dashed rgba(167, 180, 201,.3) ;\r\n\t  \t  cursor: pointer;\r\n\t  \t  border-radius: 6px;\r\n\t  \t  color: #a7b4c9  ;\r\n\t  \t  width: 200px;\r\n\t  \t  font-size: 32px;\r\n\t  \t  line-height: 200px;\r\n\t  \t  text-align: center;\r\n\t  \t  height: 200px;\r\n\t  \t}\r\n\t\r\n\t.add-update-preview :deep .el-upload-list .el-upload-list__item {\r\n\t  \t  border: 2px dashed rgba(167, 180, 201,.3) ;\r\n\t  \t  cursor: pointer;\r\n\t  \t  border-radius: 6px;\r\n\t  \t  color: #a7b4c9  ;\r\n\t  \t  width: 200px;\r\n\t  \t  font-size: 32px;\r\n\t  \t  line-height: 200px;\r\n\t  \t  text-align: center;\r\n\t  \t  height: 200px;\r\n\t  \t}\r\n\t\r\n\t.add-update-preview :deep .el-upload .el-icon-plus {\r\n\t  \t  border: 2px dashed rgba(167, 180, 201,.3) ;\r\n\t  \t  cursor: pointer;\r\n\t  \t  border-radius: 6px;\r\n\t  \t  color: #a7b4c9  ;\r\n\t  \t  width: 200px;\r\n\t  \t  font-size: 32px;\r\n\t  \t  line-height: 200px;\r\n\t  \t  text-align: center;\r\n\t  \t  height: 200px;\r\n\t  \t}\r\n\t\r\n\t.add-update-preview .el-textarea :deep .el-textarea__inner {\r\n\t  \t  border: 1px solid rgba(167, 180, 201,.3)     ;\r\n\t  \t  border-radius: 4px;\r\n\t  \t  padding: 12px;\r\n\t  \t  outline: none;\r\n\t  \t  color: #a7b4c9 ;\r\n\t  \t  width: 400px;\r\n\t  \t  font-size: 14px;\r\n\t  \t  height: 120px;\r\n\t  \t}\r\n\t\r\n\t.add-update-preview .btn .btn1 {\r\n\t\t\t\tborder: 0;\r\n\t\t\t\tcursor: pointer;\r\n\t\t\t\tborder-radius: 4px;\r\n\t\t\t\tpadding: 0 24px;\r\n\t\t\t\tmargin: 4px;\r\n\t\t\t\toutline: none;\r\n\t\t\t\tcolor: #fff;\r\n\t\t\t\tbackground: #6574cd;\r\n\t\t\t\twidth: auto;\r\n\t\t\t\tfont-size: 14px;\r\n\t\t\t\theight: 40px;\r\n\t\t\t}\r\n\t\r\n\t.add-update-preview .btn .btn1:hover {\r\n\t\t\t\topacity: 0.8;\r\n\t\t\t}\r\n\t\r\n\t.add-update-preview .btn .btn2 {\r\n\t\t\t\tborder: 0;\r\n\t\t\t\tcursor: pointer;\r\n\t\t\t\tborder-radius: 4px;\r\n\t\t\t\tpadding: 0 24px;\r\n\t\t\t\tmargin: 4px;\r\n\t\t\t\toutline: none;\r\n\t\t\t\tcolor: #fff;\r\n\t\t\t\tbackground: #6574cd;\r\n\t\t\t\twidth: auto;\r\n\t\t\t\tfont-size: 14px;\r\n\t\t\t\theight: 40px;\r\n\t\t\t}\r\n\t\r\n\t.add-update-preview .btn .btn2:hover {\r\n\t\t\t\topacity: 0.8;\r\n\t\t\t}\r\n\t\r\n\t.add-update-preview .btn .btn3 {\r\n\t\t\t\tborder: 0;\r\n\t\t\t\tcursor: pointer;\r\n\t\t\t\tborder-radius: 4px;\r\n\t\t\t\tpadding: 0 24px;\r\n\t\t\t\tmargin: 4px;\r\n\t\t\t\toutline: none;\r\n\t\t\t\tcolor: #fff;\r\n\t\t\t\tbackground: #ff2b88;\r\n\t\t\t\twidth: auto;\r\n\t\t\t\tfont-size: 14px;\r\n\t\t\t\theight: 40px;\r\n\t\t\t}\r\n\t\r\n\t.add-update-preview .btn .btn3:hover {\r\n\t\t\t\topacity: 0.8;\r\n\t\t\t}\r\n\t\r\n\t.add-update-preview .btn .btn4 {\r\n\t\t\t\tborder: 0;\r\n\t\t\t\tcursor: pointer;\r\n\t\t\t\tborder-radius: 4px;\r\n\t\t\t\tpadding: 0 24px;\r\n\t\t\t\tmargin: 4px;\r\n\t\t\t\toutline: none;\r\n\t\t\t\tcolor: #fff;\r\n\t\t\t\tbackground: #ff2b88;\r\n\t\t\t\twidth: auto;\r\n\t\t\t\tfont-size: 14px;\r\n\t\t\t\theight: 40px;\r\n\t\t\t}\r\n\t\r\n\t.add-update-preview .btn .btn4:hover {\r\n\t\t\t\topacity: 0.8;\r\n\t\t\t}\r\n\t\r\n\t.add-update-preview .btn .btn5 {\r\n\t\t\t\tborder: 0;\r\n\t\t\t\tcursor: pointer;\r\n\t\t\t\tborder-radius: 4px;\r\n\t\t\t\tpadding: 0 24px;\r\n\t\t\t\tmargin: 4px;\r\n\t\t\t\toutline: none;\r\n\t\t\t\tcolor: #fff;\r\n\t\t\t\tbackground: #ff2b88;\r\n\t\t\t\twidth: auto;\r\n\t\t\t\tfont-size: 14px;\r\n\t\t\t\theight: 40px;\r\n\t\t\t}\r\n\t\r\n\t.add-update-preview .btn .btn5:hover {\r\n\t\t\t\topacity: 0.8;\r\n\t\t\t}\r\n</style>\r\n"]}]}