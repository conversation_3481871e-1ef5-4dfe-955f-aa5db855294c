{"remainingRequest": "E:\\java系统\\公司财务系统\\src\\main\\resources\\admin\\admin\\node_modules\\babel-loader\\lib\\index.js!E:\\java系统\\公司财务系统\\src\\main\\resources\\admin\\admin\\src\\utils\\base.js", "dependencies": [{"path": "E:\\java系统\\公司财务系统\\src\\main\\resources\\admin\\admin\\src\\utils\\base.js", "mtime": 1754806962013}, {"path": "E:\\java系统\\公司财务系统\\src\\main\\resources\\admin\\admin\\babel.config.js", "mtime": 1711079474000}, {"path": "E:\\java系统\\公司财务系统\\src\\main\\resources\\admin\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754805254356}, {"path": "E:\\java系统\\公司财务系统\\src\\main\\resources\\admin\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754805257322}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:dmFyIGJhc2UgPSB7CiAgZ2V0OiBmdW5jdGlvbiBnZXQoKSB7CiAgICByZXR1cm4gewogICAgICB1cmw6ICJodHRwczovL2NhaXd1LnlvdXJkb21haW4uY29tL2FwaS8iLAogICAgICAvLyDmm7/mjaLkuLrmgqjnmoTln5/lkI0KICAgICAgbmFtZTogInNwcmluZ2Jvb3QyZzQzdDNrMCIsCiAgICAgIC8vIOmAgOWHuuWIsOmmlumhtemTvuaOpQogICAgICBpbmRleFVybDogJycKICAgIH07CiAgfSwKICBnZXRQcm9qZWN0TmFtZTogZnVuY3Rpb24gZ2V0UHJvamVjdE5hbWUoKSB7CiAgICByZXR1cm4gewogICAgICBwcm9qZWN0TmFtZTogIuWFrOWPuOi0ouWKoeeuoeeQhuezu+e7nyIKICAgIH07CiAgfQp9OwpleHBvcnQgZGVmYXVsdCBiYXNlOw=="}, {"version": 3, "names": ["base", "get", "url", "name", "indexUrl", "getProjectName", "projectName"], "sources": ["E:/java系统/公司财务系统/src/main/resources/admin/admin/src/utils/base.js"], "sourcesContent": ["const base = {\r\n    get() {\r\n        return {\r\n            url : \"https://caiwu.yourdomain.com/api/\",  // 替换为您的域名\r\n            name: \"springboot2g43t3k0\",\r\n            // 退出到首页链接\r\n            indexUrl: ''\r\n        };\r\n    },\r\n    getProjectName(){\r\n        return {\r\n            projectName: \"公司财务管理系统\"\r\n        } \r\n    }\r\n}\r\nexport default base\r\n"], "mappings": "AAAA,IAAMA,IAAI,GAAG;EACTC,GAAG,WAAHA,GAAGA,CAAA,EAAG;IACF,OAAO;MACHC,GAAG,EAAG,mCAAmC;MAAG;MAC5CC,IAAI,EAAE,oBAAoB;MAC1B;MACAC,QAAQ,EAAE;IACd,CAAC;EACL,CAAC;EACDC,cAAc,WAAdA,cAAcA,CAAA,EAAE;IACZ,OAAO;MACHC,WAAW,EAAE;IACjB,CAAC;EACL;AACJ,CAAC;AACD,eAAeN,IAAI", "ignoreList": []}]}