package com.entity.view;

import com.entity.YuangongdanganEntity;

import com.baomidou.mybatisplus.annotations.TableName;
import org.apache.commons.beanutils.BeanUtils;
import java.lang.reflect.InvocationTargetException;
import java.math.BigDecimal;

import java.io.Serializable;
import com.utils.EncryptUtil;
 

/**
 * 员工档案
 * 后端返回视图实体辅助类   
 * （通常后端关联的表或者自定义的字段需要返回使用）
 * <AUTHOR> @email 
 * @date 2024-02-19 14:47:30
 */
@TableName("yuangongdangan")
public class YuangongdanganView  extends YuangongdanganEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	public YuangongdanganView(){
	}
 
 	public YuangongdanganView(YuangongdanganEntity yuangongdanganEntity){
 	try {
			BeanUtils.copyProperties(this, yuangongdanganEntity);
		} catch (IllegalAccessException | InvocationTargetException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
 		
	}


}
