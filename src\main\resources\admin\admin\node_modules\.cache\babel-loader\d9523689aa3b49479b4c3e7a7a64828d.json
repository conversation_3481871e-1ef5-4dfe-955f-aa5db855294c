{"remainingRequest": "E:\\java系统\\公司财务系统\\src\\main\\resources\\admin\\admin\\node_modules\\babel-loader\\lib\\index.js!E:\\java系统\\公司财务系统\\src\\main\\resources\\admin\\admin\\src\\utils\\validate.js", "dependencies": [{"path": "E:\\java系统\\公司财务系统\\src\\main\\resources\\admin\\admin\\src\\utils\\validate.js", "mtime": 1754721255956}, {"path": "E:\\java系统\\公司财务系统\\src\\main\\resources\\admin\\admin\\babel.config.js", "mtime": 1711079474000}, {"path": "E:\\java系统\\公司财务系统\\src\\main\\resources\\admin\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754805254356}, {"path": "E:\\java系统\\公司财务系统\\src\\main\\resources\\admin\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754805257322}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMucmVnZXhwLmV4ZWMuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5yZWdleHAudGVzdC5qcyI7Ci8qKg0KICog6YKu566xDQogKiBAcGFyYW0geyp9IHMNCiAqLwpleHBvcnQgZnVuY3Rpb24gaXNFbWFpbChzKSB7CiAgcmV0dXJuIC9eKFthLXpBLVowLTlfLV0pK0AoW2EtekEtWjAtOV8tXSkrKCguW2EtekEtWjAtOV8tXXsyLDN9KXsxLDJ9KSQvLnRlc3Qocyk7Cn0KCi8qKg0KICog5omL5py65Y+356CBIC0g5bey56e76Zmk5qC85byP6ZmQ5Yi277yM5YWB6K645Lu75oSP5qC85byPDQogKiBAcGFyYW0geyp9IHMNCiAqLwpleHBvcnQgZnVuY3Rpb24gaXNNb2JpbGUocykgewogIHJldHVybiB0cnVlOyAvLyDnp7vpmaTmoLzlvI/pmZDliLbvvIzlhYHorrjku7vmhI/ovpPlhaUKfQoKLyoqDQogKiDnlLXor53lj7fnoIENCiAqIEBwYXJhbSB7Kn0gcw0KICovCmV4cG9ydCBmdW5jdGlvbiBpc1Bob25lKHMpIHsKICByZXR1cm4gL14oWzAtOV17Myw0fS0pP1swLTldezcsOH0kLy50ZXN0KHMpOwp9CgovKioNCiAqIFVSTOWcsOWdgA0KICogQHBhcmFtIHsqfSBzDQogKi8KZXhwb3J0IGZ1bmN0aW9uIGlzVVJMKHMpIHsKICByZXR1cm4gL15odHRwW3NdPzpcL1wvLiovLnRlc3Qocyk7Cn0KCi8qKg0KICog5Yy56YWN5pWw5a2X77yM5Y+v5Lul5piv5bCP5pWw77yM5LiN5Y+v5Lul5piv6LSf5pWwLOWPr+S7peS4uuepug0KICogQHBhcmFtIHsqfSBzIA0KICovCmV4cG9ydCBmdW5jdGlvbiBpc051bWJlcihzKSB7CiAgcmV0dXJuIC8oXi0/WystXT8oWzAtOV0qXC4/WzAtOV0rfFswLTldK1wuP1swLTldKikoW2VFXVsrLV0/WzAtOV0rKT8kKXwoXiQpLy50ZXN0KHMpOwp9Ci8qKg0KICog5Yy56YWN5pW05pWw77yM5Y+v5Lul5Li656m6DQogKiBAcGFyYW0geyp9IHMgDQogKi8KZXhwb3J0IGZ1bmN0aW9uIGlzSW50TnVtZXIocykgewogIHJldHVybiAvKF4tP1xkKyQpfCheJCkvLnRlc3Qocyk7Cn0KLyoqDQogKiDouqvku73or4HmoKHpqowNCiAqLwpleHBvcnQgZnVuY3Rpb24gY2hlY2tJZENhcmQoaWRjYXJkKSB7CiAgdmFyIHJlZ0lkQ2FyZCA9IC8oXlxkezE1fSQpfCheXGR7MTh9JCl8KF5cZHsxN30oXGR8WHx4KSQpLzsKICBpZiAoIXJlZ0lkQ2FyZC50ZXN0KGlkY2FyZCkpIHsKICAgIHJldHVybiBmYWxzZTsKICB9IGVsc2UgewogICAgcmV0dXJuIHRydWU7CiAgfQp9"}, {"version": 3, "names": ["isEmail", "s", "test", "isMobile", "isPhone", "isURL", "isNumber", "isIntNumer", "checkIdCard", "idcard", "regIdCard"], "sources": ["E:/java系统/公司财务系统/src/main/resources/admin/admin/src/utils/validate.js"], "sourcesContent": ["/**\r\n * 邮箱\r\n * @param {*} s\r\n */\r\nexport function isEmail(s) {\r\n\treturn /^([a-zA-Z0-9_-])+@([a-zA-Z0-9_-])+((.[a-zA-Z0-9_-]{2,3}){1,2})$/.test(s)\r\n}\r\n\r\n/**\r\n * 手机号码 - 已移除格式限制，允许任意格式\r\n * @param {*} s\r\n */\r\nexport function isMobile(s) {\r\n\treturn true  // 移除格式限制，允许任意输入\r\n}\r\n\r\n/**\r\n * 电话号码\r\n * @param {*} s\r\n */\r\nexport function isPhone(s) {\r\n\treturn /^([0-9]{3,4}-)?[0-9]{7,8}$/.test(s)\r\n}\r\n\r\n/**\r\n * URL地址\r\n * @param {*} s\r\n */\r\nexport function isURL(s) {\r\n\treturn /^http[s]?:\\/\\/.*/.test(s)\r\n}\r\n\r\n/**\r\n * 匹配数字，可以是小数，不可以是负数,可以为空\r\n * @param {*} s \r\n */\r\nexport function isNumber(s) {\r\n\treturn /(^-?[+-]?([0-9]*\\.?[0-9]+|[0-9]+\\.?[0-9]*)([eE][+-]?[0-9]+)?$)|(^$)/.test(s);\r\n}\r\n/**\r\n * 匹配整数，可以为空\r\n * @param {*} s \r\n */\r\nexport function isIntNumer(s) {\r\n\treturn /(^-?\\d+$)|(^$)/.test(s);\r\n}\r\n/**\r\n * 身份证校验\r\n */\r\nexport function checkIdCard(idcard) {\r\n\tconst regIdCard = /(^\\d{15}$)|(^\\d{18}$)|(^\\d{17}(\\d|X|x)$)/;\r\n\tif (!regIdCard.test(idcard)) {\r\n\t\treturn false;\r\n\t} else {\r\n\t\treturn true;\r\n\t}\r\n}"], "mappings": ";;AAAA;AACA;AACA;AACA;AACA,OAAO,SAASA,OAAOA,CAACC,CAAC,EAAE;EAC1B,OAAO,iEAAiE,CAACC,IAAI,CAACD,CAAC,CAAC;AACjF;;AAEA;AACA;AACA;AACA;AACA,OAAO,SAASE,QAAQA,CAACF,CAAC,EAAE;EAC3B,OAAO,IAAI,EAAE;AACd;;AAEA;AACA;AACA;AACA;AACA,OAAO,SAASG,OAAOA,CAACH,CAAC,EAAE;EAC1B,OAAO,4BAA4B,CAACC,IAAI,CAACD,CAAC,CAAC;AAC5C;;AAEA;AACA;AACA;AACA;AACA,OAAO,SAASI,KAAKA,CAACJ,CAAC,EAAE;EACxB,OAAO,kBAAkB,CAACC,IAAI,CAACD,CAAC,CAAC;AAClC;;AAEA;AACA;AACA;AACA;AACA,OAAO,SAASK,QAAQA,CAACL,CAAC,EAAE;EAC3B,OAAO,qEAAqE,CAACC,IAAI,CAACD,CAAC,CAAC;AACrF;AACA;AACA;AACA;AACA;AACA,OAAO,SAASM,UAAUA,CAACN,CAAC,EAAE;EAC7B,OAAO,gBAAgB,CAACC,IAAI,CAACD,CAAC,CAAC;AAChC;AACA;AACA;AACA;AACA,OAAO,SAASO,WAAWA,CAACC,MAAM,EAAE;EACnC,IAAMC,SAAS,GAAG,0CAA0C;EAC5D,IAAI,CAACA,SAAS,CAACR,IAAI,CAACO,MAAM,CAAC,EAAE;IAC5B,OAAO,KAAK;EACb,CAAC,MAAM;IACN,OAAO,IAAI;EACZ;AACD", "ignoreList": []}]}