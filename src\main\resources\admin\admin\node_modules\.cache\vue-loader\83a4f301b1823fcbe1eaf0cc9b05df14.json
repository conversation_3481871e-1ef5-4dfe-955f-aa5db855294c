{"remainingRequest": "E:\\java系统\\公司财务系统\\src\\main\\resources\\admin\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\java系统\\公司财务系统\\src\\main\\resources\\admin\\admin\\src\\views\\register.vue?vue&type=style&index=0&id=77453986&lang=scss&scoped=true", "dependencies": [{"path": "E:\\java系统\\公司财务系统\\src\\main\\resources\\admin\\admin\\src\\views\\register.vue", "mtime": 1754805481715}, {"path": "E:\\java系统\\公司财务系统\\src\\main\\resources\\admin\\admin\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1754805255591}, {"path": "E:\\java系统\\公司财务系统\\src\\main\\resources\\admin\\admin\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1754628163326}, {"path": "E:\\java系统\\公司财务系统\\src\\main\\resources\\admin\\admin\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1754628158457}, {"path": "E:\\java系统\\公司财务系统\\src\\main\\resources\\admin\\admin\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1754628153247}, {"path": "E:\\java系统\\公司财务系统\\src\\main\\resources\\admin\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754805254356}, {"path": "E:\\java系统\\公司财务系统\\src\\main\\resources\\admin\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754628160828}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["register.vue"], "names": [], "mappings": ";AAyTA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "register.vue", "sourceRoot": "src/views", "sourcesContent": ["<template>\r\n\t<div>\r\n\t\t<div class=\"container\" :style='{\"minHeight\":\"100vh\",\"padding\":\"0px 180px 0px 0px\",\"alignItems\":\"center\",\"background\":\"url(http://codegen.caihongy.cn/20240129/17e5d014970b46b0a6da2629973d8845.png) no-repeat\",\"display\":\"flex\",\"width\":\"100%\",\"backgroundSize\":\"cover\",\"justifyContent\":\"flex-end\"}'>\r\n\t\t\t<el-form v-if=\"pageFlag=='register'\" :style='{\"padding\":\"20px\",\"boxShadow\":\"0 1px 20px rgba( 255,  255, 255, .8)\",\"margin\":\"20px 0\",\"borderRadius\":\"4px\",\"background\":\"#fff\",\"width\":\"400px\",\"height\":\"auto\"}' ref=\"rgsForm\" class=\"rgs-form\" :model=\"rgsForm\" :rules=\"rules\">\r\n\t\t\t\t<div v-if=\"true\" :style='{\"width\":\"100%\",\"margin\":\"0 0 10px 0\",\"lineHeight\":\"44px\",\"fontSize\":\"20px\",\"color\":\"#374254\",\"textAlign\":\"center\"}' class=\"title\">公司财务管理系统注册</div>\r\n\t\t\t\t<el-form-item :style='{\"width\":\"80%\",\"padding\":\"0\",\"margin\":\"0 auto 15px\",\"height\":\"auto\"}' class=\"list-item\" v-if=\"tableName=='yuangong'\">\r\n\t\t\t\t\t<div v-if=\"false\" :style='{\"width\":\"64px\",\"lineHeight\":\"44px\",\"fontSize\":\"14px\",\"position\":\"relative\",\"color\":\"rgba(64, 158, 255, 1)\"}' class=\"lable\" :class=\"changeRules('gonghao')?'required':''\">工号</div>\r\n\t\t\t\t\t<el-input  v-model=\"ruleForm.gonghao\"  autocomplete=\"off\" placeholder=\"工号\"  type=\"text\"  />\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<el-form-item :style='{\"width\":\"80%\",\"padding\":\"0\",\"margin\":\"0 auto 15px\",\"height\":\"auto\"}' class=\"list-item\" v-if=\"tableName=='yuangong'\">\r\n\t\t\t\t\t<div v-if=\"false\" :style='{\"width\":\"64px\",\"lineHeight\":\"44px\",\"fontSize\":\"14px\",\"position\":\"relative\",\"color\":\"rgba(64, 158, 255, 1)\"}' class=\"lable\" :class=\"changeRules('mima')?'required':''\">密码</div>\r\n\t\t\t\t\t<el-input  v-model=\"ruleForm.mima\"  autocomplete=\"off\" placeholder=\"密码\"  type=\"password\"  />\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<el-form-item :style='{\"width\":\"80%\",\"padding\":\"0\",\"margin\":\"0 auto 15px\",\"height\":\"auto\"}' class=\"list-item\" v-if=\"tableName=='yuangong'\">\r\n\t\t\t\t\t<div v-if=\"false\" :style='{\"width\":\"64px\",\"lineHeight\":\"44px\",\"fontSize\":\"14px\",\"position\":\"relative\",\"color\":\"rgba(64, 158, 255, 1)\"}' class=\"lable\" :class=\"changeRules('mima')?'required':''\">确认密码</div>\r\n\t\t\t\t\t<el-input  v-model=\"ruleForm.mima2\" autocomplete=\"off\" placeholder=\"确认密码\" type=\"password\" />\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<el-form-item :style='{\"width\":\"80%\",\"padding\":\"0\",\"margin\":\"0 auto 15px\",\"height\":\"auto\"}' class=\"list-item\" v-if=\"tableName=='yuangong'\">\r\n\t\t\t\t\t<div v-if=\"false\" :style='{\"width\":\"64px\",\"lineHeight\":\"44px\",\"fontSize\":\"14px\",\"position\":\"relative\",\"color\":\"rgba(64, 158, 255, 1)\"}' class=\"lable\" :class=\"changeRules('xingming')?'required':''\">姓名</div>\r\n\t\t\t\t\t<el-input  v-model=\"ruleForm.xingming\"  autocomplete=\"off\" placeholder=\"姓名\"  type=\"text\"  />\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<el-form-item :style='{\"width\":\"80%\",\"padding\":\"0\",\"margin\":\"0 auto 15px\",\"height\":\"auto\"}' class=\"list-item\" v-if=\"tableName=='yuangong'\">\r\n\t\t\t\t\t<div v-if=\"false\" :style='{\"width\":\"64px\",\"lineHeight\":\"44px\",\"fontSize\":\"14px\",\"position\":\"relative\",\"color\":\"rgba(64, 158, 255, 1)\"}' class=\"lable\" :class=\"changeRules('touxiang')?'required':''\">头像�?/div>\r\n                    <file-upload\r\n                        tip=\"点击上传头像\"\r\n                        action=\"file/upload\"\r\n                        :limit=\"3\"\r\n                        :multiple=\"true\"\r\n                        :fileUrls=\"ruleForm.touxiang?ruleForm.touxiang:''\"\r\n                        @change=\"yuangongtouxiangUploadChange\"\r\n                    ></file-upload>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<el-form-item :style='{\"width\":\"80%\",\"padding\":\"0\",\"margin\":\"0 auto 15px\",\"height\":\"auto\"}' class=\"list-item\" v-if=\"tableName=='yuangong'\">\r\n\t\t\t\t\t<div v-if=\"false\" :style='{\"width\":\"64px\",\"lineHeight\":\"44px\",\"fontSize\":\"14px\",\"position\":\"relative\",\"color\":\"rgba(64, 158, 255, 1)\"}' class=\"lable\" :class=\"changeRules('xingbie')?'required':''\">性别�?/div>\r\n                    <el-select v-model=\"ruleForm.xingbie\" placeholder=\"请选择性别\" >\r\n                        <el-option\r\n                            v-for=\"(item,index) in yuangongxingbieOptions\"\r\n                            v-bind:key=\"index\"\r\n                            :label=\"item\"\r\n                            :value=\"item\">\r\n                        </el-option>\r\n                    </el-select>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<el-form-item :style='{\"width\":\"80%\",\"padding\":\"0\",\"margin\":\"0 auto 15px\",\"height\":\"auto\"}' class=\"list-item\" v-if=\"tableName=='yuangong'\">\r\n\t\t\t\t\t<div v-if=\"false\" :style='{\"width\":\"64px\",\"lineHeight\":\"44px\",\"fontSize\":\"14px\",\"position\":\"relative\",\"color\":\"rgba(64, 158, 255, 1)\"}' class=\"lable\" :class=\"changeRules('lianxidianhua')?'required':''\">联系电话�?/div>\r\n\t\t\t\t\t<el-input  v-model=\"ruleForm.lianxidianhua\"  autocomplete=\"off\" placeholder=\"联系电话\"  type=\"text\"  />\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<button :style='{\"border\":\"0\",\"cursor\":\"pointer\",\"padding\":\"0 10px\",\"margin\":\"20px auto 5px\",\"outline\":\"none\",\"color\":\"#fff\",\"borderRadius\":\"4px\",\"background\":\"#dc4e41\",\"display\":\"block\",\"width\":\"80%\",\"fontSize\":\"16px\",\"height\":\"44px\"}' type=\"button\" class=\"r-btn\" @click=\"login()\">注册</button>\r\n\t\t\t\t<div :style='{\"cursor\":\"pointer\",\"padding\":\"0 10%\",\"color\":\"rgba(159, 159, 159, 1)\",\"display\":\"inline-block\",\"lineHeight\":\"1\",\"fontSize\":\"12px\",\"textDecoration\":\"underline\"}' class=\"r-login\" @click=\"close()\">已有账号，直接登�?/div>\r\n\t\t\t</el-form>\r\n\t\t\t\r\n\t\t</div>\r\n\t</div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\truleForm: {\r\n\t\t\t},\r\n\t\t\tforgetForm: {},\r\n            pageFlag : '',\r\n\t\t\ttableName:\"\",\r\n\t\t\trules: {},\r\n            yuangongxingbieOptions: [],\r\n            yuangongbumenOptions: [],\r\n            yuangongzhiweiOptions: [],\r\n\t\t};\r\n\t},\r\n\tmounted(){\r\n\t\tthis.pageFlag = this.$route.query.pageFlag\r\n\t\tif(this.$route.query.pageFlag=='register'){\r\n\t\t\t\r\n\t\t\tlet table = this.$storage.get(\"loginTable\");\r\n\t\t\tthis.tableName = table;\r\n\t\t\tif(this.tableName=='yuangong'){\r\n\t\t\t\tthis.ruleForm = {\r\n\t\t\t\t\tgonghao: '',\r\n\t\t\t\t\tmima: '',\r\n\t\t\t\t\txingming: '',\r\n\t\t\t\t\ttouxiang: '',\r\n\t\t\t\t\txingbie: '',\r\n\t\t\t\t\tlianxidianhua: '',\r\n\t\t\t\t\tbumen: '',\r\n\t\t\t\t\tzhiwei: '',\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\tif ('yuangong' == this.tableName) {\r\n\t\t\t\tthis.rules.gonghao = [{ required: true, message: '请输入工号', trigger: 'blur' }]\r\n\t\t\t}\r\n\t\t\tif ('yuangong' == this.tableName) {\r\n\t\t\t\tthis.rules.mima = [{ required: true, message: '请输入密码', trigger: 'blur' }]\r\n\t\t\t}\r\n\t\t\tif ('yuangong' == this.tableName) {\r\n\t\t\t\tthis.rules.xingming = [{ required: true, message: '请输入姓名', trigger: 'blur' }]\r\n\t\t\t}\r\n\t\t\tthis.yuangongxingbieOptions = \"男,女\".split(',')\r\n\t\t\tthis.$http({\r\n\t\t\t\turl: `option/bumen/bumen`,\r\n\t\t\t\tmethod: \"get\"\r\n\t\t\t}).then(({ data }) => {\r\n\t\t\t\tif (data && data.code === 0) {\r\n\t\t\t\t\tthis.yuangongbumenOptions = data.data;\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.$message.error(data.msg);\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t}\r\n\t},\r\n\tcreated() {\r\n\t},\r\n\tdestroyed() {\r\n\t\t  \t},\r\n\tmethods: {\r\n\t\tchangeRules(name){\r\n\t\t\tif(this.rules[name]){\r\n\t\t\t\treturn true\r\n\t\t\t}\r\n\t\t\treturn false\r\n\t\t},\r\n\t\t// 获取uuid\r\n\t\tgetUUID () {\r\n\t\t\treturn new Date().getTime();\r\n\t\t},\r\n\t\tclose(){\r\n\t\t\tthis.$router.push({ path: \"/login\" });\r\n\t\t},\r\n        yuangongtouxiangUploadChange(fileUrls) {\r\n            this.ruleForm.touxiang = fileUrls;\r\n        },\r\n\r\n        // 多级联动参数\r\n        yuangongchange1(e,conditionColumn){\r\n            this.ruleForm.zhiwei = '';\r\n            this.yuangongzhiweiOptions = [];\r\n            this.$http({\r\n                url: `option/zhiwei/zhiwei?conditionColumn=${conditionColumn}&conditionValue=${e}`,\r\n                method: \"get\"\r\n            }).then(({ data }) => {\r\n                if (data && data.code === 0) {\r\n                    this.yuangongzhiweiOptions = data.data;\r\n                } else {\r\n                    this.$message.error(data.msg);\r\n                }\r\n            });\r\n        },\r\n\r\n\r\n\t\t// 注册\r\n\t\tlogin() {\r\n\t\t\tvar url=this.tableName+\"/register\";\r\n\t\t\t\t\tif((!this.ruleForm.gonghao) && `yuangong` == this.tableName){\r\n\t\t\t\t\t\tthis.$message.error(`工号不能为空`);\r\n\t\t\t\t\t\treturn\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif((!this.ruleForm.mima) && `yuangong` == this.tableName){\r\n\t\t\t\t\t\tthis.$message.error(`密码不能为空`);\r\n\t\t\t\t\t\treturn\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif((this.ruleForm.mima!=this.ruleForm.mima2) && `yuangong` == this.tableName){\r\n\t\t\t\t\t\tthis.$message.error(`两次密码输入不一致`);\r\n\t\t\t\t\t\treturn\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif((!this.ruleForm.xingming) && `yuangong` == this.tableName){\r\n\t\t\t\t\t\tthis.$message.error(`姓名不能为空`);\r\n\t\t\t\t\t\treturn\r\n\t\t\t\t\t}\r\n            if(this.ruleForm.touxiang!=null) {\r\n                this.ruleForm.touxiang = this.ruleForm.touxiang.replace(new RegExp(this.$base.url,\"g\"),\"\");\r\n            }\r\n\t\t\t\t\t// 已移除员工注册时联系电话的手机格式验证，允许任意格式输入\r\n\t\t\t\t\t// if(`yuangong` == this.tableName && this.ruleForm.lianxidianhua &&(!this.$validate.isMobile(this.ruleForm.lianxidianhua))){\r\n\t\t\t\t\t//\tthis.$message.error(`联系电话应输入手机格式`);\r\n\t\t\t\t\t//\treturn\r\n\t\t\t\t\t// }\r\n\t\t\tthis.$http({\r\n\t\t\t\turl: url,\r\n\t\t\t\tmethod: \"post\",\r\n\t\t\t\tdata:this.ruleForm\r\n\t\t\t}).then(({ data }) => {\r\n\t\t\t\tif (data && data.code === 0) {\r\n\t\t\t\t\tthis.$message({\r\n\t\t\t\t\t\tmessage: \"注册成功\",\r\n\t\t\t\t\t\ttype: \"success\",\r\n\t\t\t\t\t\tduration: 1500,\r\n\t\t\t\t\t\tonClose: () => {\r\n\t\t\t\t\t\t\tthis.$router.replace({ path: \"/login\" });\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t});\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.$message.error(data.msg);\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t}\r\n\t}\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n\t.container {\r\n\t  position: relative;\r\n\t  background: url(http://codegen.caihongy.cn/20240129/17e5d014970b46b0a6da2629973d8845.png) no-repeat;\r\n\r\n\t\t.el-date-editor.el-input {\r\n\t\t  width: 100%;\r\n\t\t}\r\n\t\t\r\n\t\t.rgs-form .el-input :deep .el-input__inner {\r\n\t\t\t\t\t\tborder: 1px solid rgba(167, 180, 201,.3)    ;\r\n\t\t\t\t\t\tborder-radius: 0;\r\n\t\t\t\t\t\tpadding: 0 10px;\r\n\t\t\t\t\t\toutline: none;\r\n\t\t\t\t\t\tcolor: #a7b4c9 ;\r\n\t\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\t\tfont-size: 14px;\r\n\t\t\t\t\t\theight: 44px;\r\n\t\t\t\t\t}\r\n\t\t\r\n\t\t.rgs-form .el-select :deep .el-input__inner {\r\n\t\t\t\t\t\tborder: 1px solid rgba(167, 180, 201,.3)    ;\r\n\t\t\t\t\t\tborder-radius: 0;\r\n\t\t\t\t\t\tpadding: 0 10px;\r\n\t\t\t\t\t\toutline: none;\r\n\t\t\t\t\t\tcolor: #a7b4c9 ;\r\n\t\t\t\t\t\twidth: 288px;\r\n\t\t\t\t\t\tfont-size: 14px;\r\n\t\t\t\t\t\theight: 44px;\r\n\t\t\t\t\t}\r\n\t\t\r\n\t\t.rgs-form .el-date-editor :deep .el-input__inner {\r\n\t\t\t\t\t\tborder: 1px solid rgba(167, 180, 201,.3)    ;\r\n\t\t\t\t\t\tborder-radius: 0;\r\n\t\t\t\t\t\tpadding: 0 10px 0 30px;\r\n\t\t\t\t\t\toutline: none;\r\n\t\t\t\t\t\tcolor: #a7b4c9 ;\r\n\t\t\t\t\t\twidth: 288px;\r\n\t\t\t\t\t\tfont-size: 14px;\r\n\t\t\t\t\t\theight: 44px;\r\n\t\t\t\t\t}\r\n\t\t\r\n\t\t.rgs-form .el-date-editor :deep .el-input__inner {\r\n\t\t\t\t\t\tborder: 1px solid rgba(167, 180, 201,.3)    ;\r\n\t\t\t\t\t\tborder-radius: 0;\r\n\t\t\t\t\t\tpadding: 0 10px 0 30px;\r\n\t\t\t\t\t\toutline: none;\r\n\t\t\t\t\t\tcolor: #a7b4c9 ;\r\n\t\t\t\t\t\twidth: 288px;\r\n\t\t\t\t\t\tfont-size: 14px;\r\n\t\t\t\t\t\theight: 44px;\r\n\t\t\t\t\t}\r\n\t\t\r\n\t\t.rgs-form :deep .el-upload--picture-card {\r\n\t\t\tbackground: transparent;\r\n\t\t\tborder: 0;\r\n\t\t\tborder-radius: 0;\r\n\t\t\twidth: auto;\r\n\t\t\theight: auto;\r\n\t\t\tline-height: initial;\r\n\t\t\tvertical-align: middle;\r\n\t\t}\r\n\t\t\r\n\t\t.rgs-form :deep .upload .upload-img {\r\n\t\t  \t\t  border: 1px dashed  rgba(167, 180, 201,.3)    ;\r\n\t\t  \t\t  cursor: pointer;\r\n\t\t  \t\t  border-radius: 8px;\r\n\t\t  \t\t  color: rgba(167, 180, 201,.3);\r\n\t\t  \t\t  width: 160px;\r\n\t\t  \t\t  font-size: 32px;\r\n\t\t  \t\t  line-height: 160px;\r\n\t\t  \t\t  text-align: center;\r\n\t\t  \t\t  height: 160px;\r\n\t\t  \t\t}\r\n\t\t\r\n\t\t.rgs-form :deep .el-upload-list .el-upload-list__item {\r\n\t\t  \t\t  border: 1px dashed  rgba(167, 180, 201,.3)    ;\r\n\t\t  \t\t  cursor: pointer;\r\n\t\t  \t\t  border-radius: 8px;\r\n\t\t  \t\t  color: rgba(167, 180, 201,.3);\r\n\t\t  \t\t  width: 160px;\r\n\t\t  \t\t  font-size: 32px;\r\n\t\t  \t\t  line-height: 160px;\r\n\t\t  \t\t  text-align: center;\r\n\t\t  \t\t  height: 160px;\r\n\t\t  \t\t}\r\n\t\t\r\n\t\t.rgs-form :deep .el-upload .el-icon-plus {\r\n\t\t  \t\t  border: 1px dashed  rgba(167, 180, 201,.3)    ;\r\n\t\t  \t\t  cursor: pointer;\r\n\t\t  \t\t  border-radius: 8px;\r\n\t\t  \t\t  color: rgba(167, 180, 201,.3);\r\n\t\t  \t\t  width: 160px;\r\n\t\t  \t\t  font-size: 32px;\r\n\t\t  \t\t  line-height: 160px;\r\n\t\t  \t\t  text-align: center;\r\n\t\t  \t\t  height: 160px;\r\n\t\t  \t\t}\r\n\t}\r\n\t.required {\r\n\t\tposition: relative;\r\n\t}\r\n\t.required::after{\r\n\t\t\t\tcolor: red;\r\n\t\t\t\tleft: -10px;\r\n\t\t\t\tposition: absolute;\r\n\t\t\t\tcontent: \"*\";\r\n\t\t\t}\r\n\t.editor>.avatar-uploader {\r\n\t\tline-height: 0;\r\n\t\theight: 0;\r\n\t}\r\n</style>\r\n"]}]}