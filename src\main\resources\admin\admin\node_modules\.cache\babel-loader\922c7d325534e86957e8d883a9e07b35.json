{"remainingRequest": "E:\\java系统\\公司财务系统\\src\\main\\resources\\admin\\admin\\node_modules\\babel-loader\\lib\\index.js!E:\\java系统\\公司财务系统\\src\\main\\resources\\admin\\admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!E:\\java系统\\公司财务系统\\src\\main\\resources\\admin\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\java系统\\公司财务系统\\src\\main\\resources\\admin\\admin\\src\\components\\common\\Editor.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\java系统\\公司财务系统\\src\\main\\resources\\admin\\admin\\src\\components\\common\\Editor.vue", "mtime": 1754634351804}, {"path": "E:\\java系统\\公司财务系统\\src\\main\\resources\\admin\\admin\\babel.config.js", "mtime": 1711079474000}, {"path": "E:\\java系统\\公司财务系统\\src\\main\\resources\\admin\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754805254356}, {"path": "E:\\java系统\\公司财务系统\\src\\main\\resources\\admin\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754805257322}, {"path": "E:\\java系统\\公司财务系统\\src\\main\\resources\\admin\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754805254356}, {"path": "E:\\java系统\\公司财务系统\\src\\main\\resources\\admin\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754628160828}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["toolbarOptions", "header", "list", "script", "indent", "size", "color", "background", "font", "align", "quill<PERSON><PERSON>or", "props", "value", "type", "String", "action", "maxSize", "Number", "default", "components", "data", "content", "quillUpdateImg", "editorOption", "placeholder", "theme", "modules", "toolbar", "container", "handlers", "image", "document", "querySelector", "click", "quill", "format", "$storage", "get", "computed", "getActionUrl", "concat", "$base", "name", "methods", "onEditorBlur", "onEditorFocus", "onEditorChange", "console", "log", "$emit", "beforeUpload", "uploadSuccess", "res", "file", "code", "insertEmbed", "length", "url", "$message", "error", "uploadError"], "sources": ["src/components/common/Editor.vue"], "sourcesContent": ["<template>\r\n  <div>\r\n    <!-- 图片上传组件辅助-->\r\n    <el-upload\r\n      class=\"avatar-uploader\"\r\n      :action=\"getActionUrl\"\r\n      name=\"file\"\r\n      :headers=\"header\"\r\n      :show-file-list=\"false\"\r\n      :on-success=\"uploadSuccess\"\r\n      :on-error=\"uploadError\"\r\n      :before-upload=\"beforeUpload\"\r\n    ></el-upload>\r\n\r\n    <quill-editor\r\n      class=\"editor\"\r\n      v-model=\"value\"\r\n      ref=\"myQuillEditor\"\r\n      :options=\"editorOption\"\r\n      @blur=\"onEditorBlur($event)\"\r\n      @focus=\"onEditorFocus($event)\"\r\n      @change=\"onEditorChange($event)\"\r\n    ></quill-editor>\r\n  </div>\r\n</template>\r\n<script>\r\n// 工具栏配置\r\nconst toolbarOptions = [\r\n  [\"bold\", \"italic\", \"underline\", \"strike\"], // 加粗 斜体 下划线 删除线\r\n  [\"blockquote\", \"code-block\"], // 引用  代码块\r\n  [{ header: 1 }, { header: 2 }], // 1级 2级标题\r\n  [{ list: \"ordered\" }, { list: \"bullet\" }], // 有序、无序列表\r\n  [{ script: \"sub\" }, { script: \"super\" }], // 上标/下标\r\n  [{ indent: \"-1\" }, { indent: \"+1\" }], // 缩进\r\n  // [{'direction': 'rtl'}],                         // 文本方向\r\n  [{ size: [\"small\", false, \"large\", \"huge\"] }], // 字体大小\r\n  [{ header: [1, 2, 3, 4, 5, 6, false] }], // 标题\r\n  [{ color: [] }, { background: [] }], // 字体颜色、字体背景颜色\r\n  [{ font: [] }], // 字体种类\r\n  [{ align: [] }], // 对齐方式\r\n  [\"clean\"], // 清除文本格式\r\n  [\"link\", \"image\", \"video\"] // 链接、图片、视频\r\n];\r\n\r\nimport { quillEditor } from \"vue-quill-editor\";\r\nimport \"quill/dist/quill.core.css\";\r\nimport \"quill/dist/quill.snow.css\";\r\nimport \"quill/dist/quill.bubble.css\";\r\n\r\nexport default {\r\n  props: {\r\n    /*编辑器的内容*/\r\n    value: {\r\n      type: String\r\n    },\r\n    action: {\r\n      type: String\r\n    },\r\n    /*图片大小*/\r\n    maxSize: {\r\n      type: Number,\r\n      default: 4000 //kb\r\n    }\r\n  },\r\n\r\n  components: {\r\n    quillEditor\r\n  },\r\n\r\n  data() {\r\n    return {\r\n      content: this.value,\r\n      quillUpdateImg: false, // 根据图片上传状态来确定是否显示loading动画，刚开始是false,不显示\r\n      editorOption: {\r\n        placeholder: \"\",\r\n        theme: \"snow\", // or 'bubble'\r\n        modules: {\r\n          toolbar: {\r\n            container: toolbarOptions,\r\n            // container: \"#toolbar\",\r\n            handlers: {\r\n              image: function(value) {\r\n                if (value) {\r\n                  // 触发input框选择图片文件\r\n                  document.querySelector(\".avatar-uploader input\").click();\r\n                } else {\r\n                  this.quill.format(\"image\", false);\r\n                }\r\n              }\r\n              // link: function(value) {\r\n              //   if (value) {\r\n              //     var href = prompt('请输入url');\r\n              //     this.quill.format(\"link\", href);\r\n              //   } else {\r\n              //     this.quill.format(\"link\", false);\r\n              //   }\r\n              // },\r\n            }\r\n          }\r\n        }\r\n      },\r\n      // serverUrl: `${base.url}sys/storage/uploadSwiper?token=${storage.get('token')}`, // 这里写你要上传的图片服务器地址\r\n      header: {\r\n        // token: sessionStorage.token\r\n       'Token': this.$storage.get(\"Token\")\r\n      } // 有的图片服务器要求请求头需要有token\r\n    };\r\n  },\r\n  computed: {\r\n    // 计算属性的 getter\r\n    getActionUrl: function() {\r\n      // return this.$base.url + this.action + \"?token=\" + this.$storage.get(\"token\");\r\n      return `/${this.$base.name}/` + this.action;\r\n    }\r\n  },\r\n  methods: {\r\n    onEditorBlur() {\r\n      //失去焦点事件\r\n    },\r\n    onEditorFocus() {\r\n      //获得焦点事件\r\n    },\r\n    onEditorChange() {\r\n      console.log(this.value);\r\n      //内容改变事件\r\n      this.$emit(\"input\", this.value);\r\n    },\r\n    // 富文本图片上传前\r\n    beforeUpload() {\r\n      // 显示loading动画\r\n      this.quillUpdateImg = true;\r\n    },\r\n\r\n    uploadSuccess(res, file) {\r\n      // res为图片服务器返回的数�?      // 获取富文本组件实�?      let quill = this.$refs.myQuillEditor.quill;\r\n      // 如果上传成功\r\n      if (res.code === 0) {\r\n        // 获取光标所在位�?        let length = quill.getSelection().index;\r\n        // 插入图片  res.url为服务器返回的图片地址\r\n        quill.insertEmbed(length, \"image\", this.$base.url+ \"upload/\" +res.file);\r\n        // 调整光标到最�?        quill.setSelection(length + 1);\r\n      } else {\r\n        this.$message.error(\"图片插入失败\");\r\n      }\r\n      // loading动画消失\r\n      this.quillUpdateImg = false;\r\n    },\r\n    // 富文本图片上传失败\r\n    uploadError() {\r\n      // loading动画消失\r\n      this.quillUpdateImg = false;\r\n      this.$message.error(\"图片插入失败\");\r\n    }\r\n  }\r\n};\r\n</script> \r\n\r\n<style>\r\n.editor {\r\n  line-height: normal !important;\r\n}\r\n.ql-snow .ql-tooltip[data-mode=\"link\"]::before {\r\n  content: \"请输入链接地址:\";\r\n}\r\n.ql-snow .ql-tooltip.ql-editing a.ql-action::after {\r\n  border-right: 0px;\r\n  content: \"保存\";\r\n  padding-right: 0px;\r\n}\r\n\r\n.ql-snow .ql-tooltip[data-mode=\"video\"]::before {\r\n  content: \"请输入视频地址:\";\r\n}\r\n.ql-container {\r\n\theight: 400px;\r\n}\r\n\r\n.ql-snow .ql-picker.ql-size .ql-picker-label::before,\r\n.ql-snow .ql-picker.ql-size .ql-picker-item::before {\r\n  content: \"14px\";\r\n}\r\n.ql-snow .ql-picker.ql-size .ql-picker-label[data-value=\"small\"]::before,\r\n.ql-snow .ql-picker.ql-size .ql-picker-item[data-value=\"small\"]::before {\r\n  content: \"10px\";\r\n}\r\n.ql-snow .ql-picker.ql-size .ql-picker-label[data-value=\"large\"]::before,\r\n.ql-snow .ql-picker.ql-size .ql-picker-item[data-value=\"large\"]::before {\r\n  content: \"18px\";\r\n}\r\n.ql-snow .ql-picker.ql-size .ql-picker-label[data-value=\"huge\"]::before,\r\n.ql-snow .ql-picker.ql-size .ql-picker-item[data-value=\"huge\"]::before {\r\n  content: \"32px\";\r\n}\r\n\r\n.ql-snow .ql-picker.ql-header .ql-picker-label::before,\r\n.ql-snow .ql-picker.ql-header .ql-picker-item::before {\r\n  content: \"文本\";\r\n}\r\n.ql-snow .ql-picker.ql-header .ql-picker-label[data-value=\"1\"]::before,\r\n.ql-snow .ql-picker.ql-header .ql-picker-item[data-value=\"1\"]::before {\r\n  content: \"标题1\";\r\n}\r\n.ql-snow .ql-picker.ql-header .ql-picker-label[data-value=\"2\"]::before,\r\n.ql-snow .ql-picker.ql-header .ql-picker-item[data-value=\"2\"]::before {\r\n  content: \"标题2\";\r\n}\r\n.ql-snow .ql-picker.ql-header .ql-picker-label[data-value=\"3\"]::before,\r\n.ql-snow .ql-picker.ql-header .ql-picker-item[data-value=\"3\"]::before {\r\n  content: \"标题3\";\r\n}\r\n.ql-snow .ql-picker.ql-header .ql-picker-label[data-value=\"4\"]::before,\r\n.ql-snow .ql-picker.ql-header .ql-picker-item[data-value=\"4\"]::before {\r\n  content: \"标题4\";\r\n}\r\n.ql-snow .ql-picker.ql-header .ql-picker-label[data-value=\"5\"]::before,\r\n.ql-snow .ql-picker.ql-header .ql-picker-item[data-value=\"5\"]::before {\r\n  content: \"标题5\";\r\n}\r\n.ql-snow .ql-picker.ql-header .ql-picker-label[data-value=\"6\"]::before,\r\n.ql-snow .ql-picker.ql-header .ql-picker-item[data-value=\"6\"]::before {\r\n  content: \"标题6\";\r\n}\r\n\r\n.ql-snow .ql-picker.ql-font .ql-picker-label::before,\r\n.ql-snow .ql-picker.ql-font .ql-picker-item::before {\r\n  content: \"标准字体\";\r\n}\r\n.ql-snow .ql-picker.ql-font .ql-picker-label[data-value=\"serif\"]::before,\r\n.ql-snow .ql-picker.ql-font .ql-picker-item[data-value=\"serif\"]::before {\r\n  content: \"衬线字体\";\r\n}\r\n.ql-snow .ql-picker.ql-font .ql-picker-label[data-value=\"monospace\"]::before,\r\n.ql-snow .ql-picker.ql-font .ql-picker-item[data-value=\"monospace\"]::before {\r\n  content: \"等宽字体\";\r\n}\r\n</style>\r\n"], "mappings": ";;AA0BA;AACA,IAAAA,cAAA,IACA;AAAA;AACA;AAAA;AACA;EAAAC,MAAA;AAAA;EAAAA,MAAA;AAAA;AAAA;AACA;EAAAC,IAAA;AAAA;EAAAA,IAAA;AAAA;AAAA;AACA;EAAAC,MAAA;AAAA;EAAAA,MAAA;AAAA;AAAA;AACA;EAAAC,MAAA;AAAA;EAAAA,MAAA;AAAA;AAAA;AACA;AACA;EAAAC,IAAA;AAAA;AAAA;AACA;EAAAJ,MAAA;AAAA;AAAA;AACA;EAAAK,KAAA;AAAA;EAAAC,UAAA;AAAA;AAAA;AACA;EAAAC,IAAA;AAAA;AAAA;AACA;EAAAC,KAAA;AAAA;AAAA;AACA;AAAA;AACA;AAAA,CACA;AAEA,SAAAC,WAAA;AACA;AACA;AACA;AAEA;EACAC,KAAA;IACA;IACAC,KAAA;MACAC,IAAA,EAAAC;IACA;IACAC,MAAA;MACAF,IAAA,EAAAC;IACA;IACA;IACAE,OAAA;MACAH,IAAA,EAAAI,MAAA;MACAC,OAAA;IACA;EACA;EAEAC,UAAA;IACAT,WAAA,EAAAA;EACA;EAEAU,IAAA,WAAAA,KAAA;IACA;MACAC,OAAA,OAAAT,KAAA;MACAU,cAAA;MAAA;MACAC,YAAA;QACAC,WAAA;QACAC,KAAA;QAAA;QACAC,OAAA;UACAC,OAAA;YACAC,SAAA,EAAA5B,cAAA;YACA;YACA6B,QAAA;cACAC,KAAA,WAAAA,MAAAlB,KAAA;gBACA,IAAAA,KAAA;kBACA;kBACAmB,QAAA,CAAAC,aAAA,2BAAAC,KAAA;gBACA;kBACA,KAAAC,KAAA,CAAAC,MAAA;gBACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;YACA;UACA;QACA;MACA;MACA;MACAlC,MAAA;QACA;QACA,cAAAmC,QAAA,CAAAC,GAAA;MACA;IACA;EACA;EACAC,QAAA;IACA;IACAC,YAAA,WAAAA,aAAA;MACA;MACA,WAAAC,MAAA,MAAAC,KAAA,CAAAC,IAAA,cAAA3B,MAAA;IACA;EACA;EACA4B,OAAA;IACAC,YAAA,WAAAA,aAAA;MACA;IAAA,CACA;IACAC,aAAA,WAAAA,cAAA;MACA;IAAA,CACA;IACAC,cAAA,WAAAA,eAAA;MACAC,OAAA,CAAAC,GAAA,MAAApC,KAAA;MACA;MACA,KAAAqC,KAAA,eAAArC,KAAA;IACA;IACA;IACAsC,YAAA,WAAAA,aAAA;MACA;MACA,KAAA5B,cAAA;IACA;IAEA6B,aAAA,WAAAA,cAAAC,GAAA,EAAAC,IAAA;MACA;MACA;MACA,IAAAD,GAAA,CAAAE,IAAA;QACA;QACA;QACApB,KAAA,CAAAqB,WAAA,CAAAC,MAAA,gBAAAf,KAAA,CAAAgB,GAAA,eAAAL,GAAA,CAAAC,IAAA;QACA;MACA;QACA,KAAAK,QAAA,CAAAC,KAAA;MACA;MACA;MACA,KAAArC,cAAA;IACA;IACA;IACAsC,WAAA,WAAAA,YAAA;MACA;MACA,KAAAtC,cAAA;MACA,KAAAoC,QAAA,CAAAC,KAAA;IACA;EACA;AACA", "ignoreList": []}]}