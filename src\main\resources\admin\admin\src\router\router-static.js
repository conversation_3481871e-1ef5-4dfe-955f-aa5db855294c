import Vue from 'vue';
//配置路由
import VueRouter from 'vue-router'
Vue.use(VueRouter);
//1.创建组件
import Index from '@/views/index'
import Home from '@/views/home'
import Login from '@/views/login'
import NotFound from '@/views/404'
import UpdatePassword from '@/views/update-password'
import pay from '@/views/pay'
import register from '@/views/register'
import center from '@/views/center'
    import yuangong from '@/views/modules/yuangong/list'
    import zichancaigou from '@/views/modules/zichancaigou/list'
    import yuangonggongzi from '@/views/modules/yuangonggongzi/list'
    import gudingzichan from '@/views/modules/gudingzichan/list'
    import zichanshenling from '@/views/modules/zichanshenling/list'
    import jiangchengxinxi from '@/views/modules/jiangchengxinxi/list'
    import zichanleixing from '@/views/modules/zichanleixing/list'
    import caiwuxinxi from '@/views/modules/caiwuxinxi/list'
    import qingjiaxinxi from '@/views/modules/qingjiaxinxi/list'
    import bumen from '@/views/modules/bumen/list'
    import yuangongdangan from '@/views/modules/yuangongdangan/list'
    import zhiwei from '@/views/modules/zhiwei/list'
    import kaoqinxinxi from '@/views/modules/kaoqinxinxi/list'
    import gonggaoxinxi from '@/views/modules/gonggaoxinxi/list'


//2.配置路由   注意：名字
export const routes = [{
    path: '/',
    redirect: '/login'  // 根路径直接重定向到登录页
  }, {
    path: '/main',
    name: '系统首页',
    component: Index,
    children: [{
      // 这里不设置值，是把main作为默认页面
      path: '/',
      name: '系统首页',
      component: Home,
      meta: {icon:'', title:'center', affix: true}
    }, {
      path: '/updatePassword',
      name: '修改密码',
      component: UpdatePassword,
      meta: {icon:'', title:'updatePassword'}
    }, {
      path: '/pay',
      name: '支付',
      component: pay,
      meta: {icon:'', title:'pay'}
    }, {
      path: '/center',
      name: '个人信息',
      component: center,
      meta: {icon:'', title:'center'}
    }
      ,{
	path: '/yuangong',
        name: '员工',
        component: yuangong
      }
      ,{
	path: '/zichancaigou',
        name: '资产采购',
        component: zichancaigou
      }
      ,{
	path: '/yuangonggongzi',
        name: '员工工资',
        component: yuangonggongzi
      }
      ,{
	path: '/gudingzichan',
        name: '固定资产',
        component: gudingzichan
      }
      ,{
	path: '/zichanshenling',
        name: '资产申领',
        component: zichanshenling
      }
      ,{
	path: '/jiangchengxinxi',
        name: '奖惩信息',
        component: jiangchengxinxi
      }
      ,{
	path: '/zichanleixing',
        name: '资产类型',
        component: zichanleixing
      }
      ,{
	path: '/caiwuxinxi',
        name: '财务信息',
        component: caiwuxinxi
      }
      ,{
	path: '/qingjiaxinxi',
        name: '请假信息',
        component: qingjiaxinxi
      }
      ,{
	path: '/bumen',
        name: '部门',
        component: bumen
      }
      ,{
	path: '/yuangongdangan',
        name: '员工档案',
        component: yuangongdangan
      }
      ,{
	path: '/zhiwei',
        name: '职位',
        component: zhiwei
      }
      ,{
	path: '/kaoqinxinxi',
        name: '考勤信息',
        component: kaoqinxinxi
      }
      ,{
	path: '/gonggaoxinxi',
        name: '公告信息',
        component: gonggaoxinxi
      }
    ]
  },
  {
    path: '/login',
    name: 'login',
    component: Login,
    meta: {icon:'', title:'login'}
  },
  {
    path: '/register',
    name: 'register',
    component: register,
    meta: {icon:'', title:'register'}
  },
  {
    path: '*',
    component: NotFound
  }
]
//3.实例化VueRouter  注意：名字
const router = new VueRouter({
  mode: 'hash',
  /*hash模式改为history*/
  routes // （缩写）相当于 routes: routes
})
const originalPush = VueRouter.prototype.push
//修改原型对象中的push方法
VueRouter.prototype.push = function push(location) {
   return originalPush.call(this, location).catch(err => err)
}
export default router;
