{"remainingRequest": "E:\\java系统\\公司财务系统\\src\\main\\resources\\admin\\admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--13-0!E:\\java系统\\公司财务系统\\src\\main\\resources\\admin\\admin\\node_modules\\babel-loader\\lib\\index.js!E:\\java系统\\公司财务系统\\src\\main\\resources\\admin\\admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??ref--6!E:\\java系统\\公司财务系统\\src\\main\\resources\\admin\\admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!E:\\java系统\\公司财务系统\\src\\main\\resources\\admin\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\java系统\\公司财务系统\\src\\main\\resources\\admin\\admin\\src\\components\\index\\IndexHeader.vue?vue&type=template&id=71e3f342&scoped=true", "dependencies": [{"path": "E:\\java系统\\公司财务系统\\src\\main\\resources\\admin\\admin\\src\\components\\index\\IndexHeader.vue", "mtime": 1754631104825}, {"path": "E:\\java系统\\公司财务系统\\src\\main\\resources\\admin\\admin\\babel.config.js", "mtime": 1711079474000}, {"path": "E:\\java系统\\公司财务系统\\src\\main\\resources\\admin\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754805254356}, {"path": "E:\\java系统\\公司财务系统\\src\\main\\resources\\admin\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754805254356}, {"path": "E:\\java系统\\公司财务系统\\src\\main\\resources\\admin\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754805257322}, {"path": "E:\\java系统\\公司财务系统\\src\\main\\resources\\admin\\admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1754628163793}, {"path": "E:\\java系统\\公司财务系统\\src\\main\\resources\\admin\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754805254356}, {"path": "E:\\java系统\\公司财务系统\\src\\main\\resources\\admin\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754628160828}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["render", "_vm", "_c", "_self", "staticClass", "style", "display", "width", "objectFit", "borderRadius", "float", "height", "attrs", "src", "fit", "_e", "padding", "lineHeight", "color", "_v", "_s", "$project", "projectName", "fontSize", "position", "right", "trigger", "on", "command", "handleCommand", "alignItems", "user", "margin", "avatar", "$base", "url", "require", "$storage", "get", "slot", "staticRenderFns", "_withStripped"], "sources": ["E:/java系统/公司财务系统/src/main/resources/admin/admin/src/components/index/IndexHeader.vue"], "sourcesContent": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _c(\n    \"div\",\n    { staticClass: \"navbar\" },\n    [\n      _c(\n        \"div\",\n        { staticClass: \"title\", style: { display: \"none\" } },\n        [\n          true\n            ? _c(\"el-image\", {\n                staticClass: \"title-img\",\n                style: {\n                  width: \"44px\",\n                  objectFit: \"cover\",\n                  borderRadius: \"100%\",\n                  float: \"left\",\n                  height: \"44px\",\n                },\n                attrs: {\n                  src: \"http://codegen.caihongy.cn/20201114/7856ba26477849ea828f481fa2773a95.jpg\",\n                  fit: \"cover\",\n                },\n              })\n            : _vm._e(),\n          _c(\n            \"span\",\n            {\n              staticClass: \"title-name\",\n              style: {\n                padding: \"0 0 0 12px\",\n                lineHeight: \"44px\",\n                color: \"#fff\",\n                float: \"left\",\n              },\n            },\n            [_vm._v(_vm._s(this.$project.projectName))]\n          ),\n        ],\n        1\n      ),\n      _c(\n        \"el-dropdown\",\n        {\n          style: {\n            fontSize: \"14px\",\n            position: \"absolute\",\n            right: \"20px\",\n            color: \"#666\",\n            display: \"flex\",\n          },\n          attrs: { trigger: \"click\" },\n          on: { command: _vm.handleCommand },\n        },\n        [\n          _c(\n            \"div\",\n            {\n              staticClass: \"el-dropdown-link\",\n              style: { alignItems: \"center\", display: \"flex\" },\n            },\n            [\n              _vm.user\n                ? _c(\"el-image\", {\n                    style: {\n                      width: \"32px\",\n                      margin: \"0 10px\",\n                      objectFit: \"cover\",\n                      borderRadius: \"100%\",\n                      display: \"none\",\n                      height: \"32px\",\n                    },\n                    attrs: {\n                      src: _vm.avatar\n                        ? this.$base.url + _vm.avatar\n                        : require(\"@/assets/img/avator.png\"),\n                      fit: \"cover\",\n                    },\n                  })\n                : _vm._e(),\n              _c(\n                \"span\",\n                {\n                  style: {\n                    color: \"#fff\",\n                    lineHeight: \"32px\",\n                    fontSize: \"14px\",\n                  },\n                },\n                [_vm._v(_vm._s(this.$storage.get(\"adminName\")))]\n              ),\n              _c(\"span\", {\n                staticClass: \"icon iconfont icon-xiala\",\n                style: { color: \"#fff\", margin: \"0 0 0 5px\", fontSize: \"14px\" },\n              }),\n            ],\n            1\n          ),\n          _c(\n            \"el-dropdown-menu\",\n            {\n              staticClass: \"top-el-dropdown-menu\",\n              attrs: { slot: \"dropdown\" },\n              slot: \"dropdown\",\n            },\n            [\n              _c(\n                \"el-dropdown-item\",\n                { staticClass: \"item1\", attrs: { command: \"\" } },\n                [_vm._v(\"首页\")]\n              ),\n              _c(\n                \"el-dropdown-item\",\n                { staticClass: \"item2\", attrs: { command: \"center\" } },\n                [_vm._v(\"个人中心\")]\n              ),\n              this.$storage.get(\"role\") == \"管理员\"\n                ? _c(\n                    \"el-dropdown-item\",\n                    { staticClass: \"item3\", attrs: { command: \"backUp\" } },\n                    [_vm._v(\"数据备份\")]\n                  )\n                : _vm._e(),\n              _c(\n                \"el-dropdown-item\",\n                { staticClass: \"item4\", attrs: { command: \"logout\" } },\n                [_vm._v(\"退出登录\")]\n              ),\n            ],\n            1\n          ),\n        ],\n        1\n      ),\n    ],\n    1\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;EAC7B,IAAIC,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CACP,KAAK,EACL;IAAEE,WAAW,EAAE;EAAS,CAAC,EACzB,CACEF,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE,OAAO;IAAEC,KAAK,EAAE;MAAEC,OAAO,EAAE;IAAO;EAAE,CAAC,EACpD,CACE,IAAI,GACAJ,EAAE,CAAC,UAAU,EAAE;IACbE,WAAW,EAAE,WAAW;IACxBC,KAAK,EAAE;MACLE,KAAK,EAAE,MAAM;MACbC,SAAS,EAAE,OAAO;MAClBC,YAAY,EAAE,MAAM;MACpBC,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE;IACV,CAAC;IACDC,KAAK,EAAE;MACLC,GAAG,EAAE,0EAA0E;MAC/EC,GAAG,EAAE;IACP;EACF,CAAC,CAAC,GACFb,GAAG,CAACc,EAAE,CAAC,CAAC,EACZb,EAAE,CACA,MAAM,EACN;IACEE,WAAW,EAAE,YAAY;IACzBC,KAAK,EAAE;MACLW,OAAO,EAAE,YAAY;MACrBC,UAAU,EAAE,MAAM;MAClBC,KAAK,EAAE,MAAM;MACbR,KAAK,EAAE;IACT;EACF,CAAC,EACD,CAACT,GAAG,CAACkB,EAAE,CAAClB,GAAG,CAACmB,EAAE,CAAC,IAAI,CAACC,QAAQ,CAACC,WAAW,CAAC,CAAC,CAC5C,CAAC,CACF,EACD,CACF,CAAC,EACDpB,EAAE,CACA,aAAa,EACb;IACEG,KAAK,EAAE;MACLkB,QAAQ,EAAE,MAAM;MAChBC,QAAQ,EAAE,UAAU;MACpBC,KAAK,EAAE,MAAM;MACbP,KAAK,EAAE,MAAM;MACbZ,OAAO,EAAE;IACX,CAAC;IACDM,KAAK,EAAE;MAAEc,OAAO,EAAE;IAAQ,CAAC;IAC3BC,EAAE,EAAE;MAAEC,OAAO,EAAE3B,GAAG,CAAC4B;IAAc;EACnC,CAAC,EACD,CACE3B,EAAE,CACA,KAAK,EACL;IACEE,WAAW,EAAE,kBAAkB;IAC/BC,KAAK,EAAE;MAAEyB,UAAU,EAAE,QAAQ;MAAExB,OAAO,EAAE;IAAO;EACjD,CAAC,EACD,CACEL,GAAG,CAAC8B,IAAI,GACJ7B,EAAE,CAAC,UAAU,EAAE;IACbG,KAAK,EAAE;MACLE,KAAK,EAAE,MAAM;MACbyB,MAAM,EAAE,QAAQ;MAChBxB,SAAS,EAAE,OAAO;MAClBC,YAAY,EAAE,MAAM;MACpBH,OAAO,EAAE,MAAM;MACfK,MAAM,EAAE;IACV,CAAC;IACDC,KAAK,EAAE;MACLC,GAAG,EAAEZ,GAAG,CAACgC,MAAM,GACX,IAAI,CAACC,KAAK,CAACC,GAAG,GAAGlC,GAAG,CAACgC,MAAM,GAC3BG,OAAO,CAAC,yBAAyB,CAAC;MACtCtB,GAAG,EAAE;IACP;EACF,CAAC,CAAC,GACFb,GAAG,CAACc,EAAE,CAAC,CAAC,EACZb,EAAE,CACA,MAAM,EACN;IACEG,KAAK,EAAE;MACLa,KAAK,EAAE,MAAM;MACbD,UAAU,EAAE,MAAM;MAClBM,QAAQ,EAAE;IACZ;EACF,CAAC,EACD,CAACtB,GAAG,CAACkB,EAAE,CAAClB,GAAG,CAACmB,EAAE,CAAC,IAAI,CAACiB,QAAQ,CAACC,GAAG,CAAC,WAAW,CAAC,CAAC,CAAC,CACjD,CAAC,EACDpC,EAAE,CAAC,MAAM,EAAE;IACTE,WAAW,EAAE,0BAA0B;IACvCC,KAAK,EAAE;MAAEa,KAAK,EAAE,MAAM;MAAEc,MAAM,EAAE,WAAW;MAAET,QAAQ,EAAE;IAAO;EAChE,CAAC,CAAC,CACH,EACD,CACF,CAAC,EACDrB,EAAE,CACA,kBAAkB,EAClB;IACEE,WAAW,EAAE,sBAAsB;IACnCQ,KAAK,EAAE;MAAE2B,IAAI,EAAE;IAAW,CAAC;IAC3BA,IAAI,EAAE;EACR,CAAC,EACD,CACErC,EAAE,CACA,kBAAkB,EAClB;IAAEE,WAAW,EAAE,OAAO;IAAEQ,KAAK,EAAE;MAAEgB,OAAO,EAAE;IAAG;EAAE,CAAC,EAChD,CAAC3B,GAAG,CAACkB,EAAE,CAAC,IAAI,CAAC,CACf,CAAC,EACDjB,EAAE,CACA,kBAAkB,EAClB;IAAEE,WAAW,EAAE,OAAO;IAAEQ,KAAK,EAAE;MAAEgB,OAAO,EAAE;IAAS;EAAE,CAAC,EACtD,CAAC3B,GAAG,CAACkB,EAAE,CAAC,MAAM,CAAC,CACjB,CAAC,EACD,IAAI,CAACkB,QAAQ,CAACC,GAAG,CAAC,MAAM,CAAC,IAAI,KAAK,GAC9BpC,EAAE,CACA,kBAAkB,EAClB;IAAEE,WAAW,EAAE,OAAO;IAAEQ,KAAK,EAAE;MAAEgB,OAAO,EAAE;IAAS;EAAE,CAAC,EACtD,CAAC3B,GAAG,CAACkB,EAAE,CAAC,MAAM,CAAC,CACjB,CAAC,GACDlB,GAAG,CAACc,EAAE,CAAC,CAAC,EACZb,EAAE,CACA,kBAAkB,EAClB;IAAEE,WAAW,EAAE,OAAO;IAAEQ,KAAK,EAAE;MAAEgB,OAAO,EAAE;IAAS;EAAE,CAAC,EACtD,CAAC3B,GAAG,CAACkB,EAAE,CAAC,MAAM,CAAC,CACjB,CAAC,CACF,EACD,CACF,CAAC,CACF,EACD,CACF,CAAC,CACF,EACD,CACF,CAAC;AACH,CAAC;AACD,IAAIqB,eAAe,GAAG,EAAE;AACxBxC,MAAM,CAACyC,aAAa,GAAG,IAAI;AAE3B,SAASzC,MAAM,EAAEwC,eAAe", "ignoreList": []}]}