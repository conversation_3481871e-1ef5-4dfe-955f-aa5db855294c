package com.entity;

import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.annotations.TableName;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import java.lang.reflect.InvocationTargetException;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

import org.springframework.format.annotation.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.beanutils.BeanUtils;
import com.baomidou.mybatisplus.annotations.TableField;
import com.baomidou.mybatisplus.enums.FieldFill;
import com.baomidou.mybatisplus.enums.IdType;


/**
 * 奖惩信息
 * 数据库通用操作实体类（普通增删改查）
 * <AUTHOR> @email 
 * @date 2024-02-19 14:47:30
 */
@TableName("jiangchengxinxi")
public class JiangchengxinxiEntity<T> implements Serializable {
	private static final long serialVersionUID = 1L;


	public JiangchengxinxiEntity() {
		
	}
	
	public JiangchengxinxiEntity(T t) {
		try {
			BeanUtils.copyProperties(this, t);
		} catch (IllegalAccessException | InvocationTargetException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
	}
	
	/**
	 * 主键id
	 */
    @TableId(type = IdType.AUTO)
    private Long id;
	/**
	 * 工号
	 */
					
	private String gonghao;
	
	/**
	 * 姓名
	 */
					
	private String xingming;
	
	/**
	 * 部门
	 */
					
	private String bumen;
	
	/**
	 * 职位
	 */
					
	private String zhiwei;
	
	/**
	 * 奖惩类型
	 */
					
	private String jiangchengleixing;
	
	/**
	 * 奖惩金额
	 */
					
	private Integer jiangchengjine;
	
	/**
	 * 奖惩原因
	 */
					
	private String jiangchengyuanyin;
	
	/**
	 * 奖惩日期
	 */
				
	@JsonFormat(locale="zh", timezone="GMT+8", pattern="yyyy-MM-dd")
	@DateTimeFormat 		
	private Date jiangchengriqi;
	
	
	@JsonFormat(locale="zh", timezone="GMT+8", pattern="yyyy-MM-dd HH:mm:ss")
	@DateTimeFormat
	private Date addtime;

	public Date getAddtime() {
		return addtime;
	}
	public void setAddtime(Date addtime) {
		this.addtime = addtime;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}
	/**
	 * 设置：工号
	 */
	public void setGonghao(String gonghao) {
		this.gonghao = gonghao;
	}
	/**
	 * 获取：工号
	 */
	public String getGonghao() {
		return gonghao;
	}
	/**
	 * 设置：姓名
	 */
	public void setXingming(String xingming) {
		this.xingming = xingming;
	}
	/**
	 * 获取：姓名
	 */
	public String getXingming() {
		return xingming;
	}
	/**
	 * 设置：部门
	 */
	public void setBumen(String bumen) {
		this.bumen = bumen;
	}
	/**
	 * 获取：部门
	 */
	public String getBumen() {
		return bumen;
	}
	/**
	 * 设置：职位
	 */
	public void setZhiwei(String zhiwei) {
		this.zhiwei = zhiwei;
	}
	/**
	 * 获取：职位
	 */
	public String getZhiwei() {
		return zhiwei;
	}
	/**
	 * 设置：奖惩类型
	 */
	public void setJiangchengleixing(String jiangchengleixing) {
		this.jiangchengleixing = jiangchengleixing;
	}
	/**
	 * 获取：奖惩类型
	 */
	public String getJiangchengleixing() {
		return jiangchengleixing;
	}
	/**
	 * 设置：奖惩金额
	 */
	public void setJiangchengjine(Integer jiangchengjine) {
		this.jiangchengjine = jiangchengjine;
	}
	/**
	 * 获取：奖惩金额
	 */
	public Integer getJiangchengjine() {
		return jiangchengjine;
	}
	/**
	 * 设置：奖惩原因
	 */
	public void setJiangchengyuanyin(String jiangchengyuanyin) {
		this.jiangchengyuanyin = jiangchengyuanyin;
	}
	/**
	 * 获取：奖惩原因
	 */
	public String getJiangchengyuanyin() {
		return jiangchengyuanyin;
	}
	/**
	 * 设置：奖惩日期
	 */
	public void setJiangchengriqi(Date jiangchengriqi) {
		this.jiangchengriqi = jiangchengriqi;
	}
	/**
	 * 获取：奖惩日期
	 */
	public Date getJiangchengriqi() {
		return jiangchengriqi;
	}

}
