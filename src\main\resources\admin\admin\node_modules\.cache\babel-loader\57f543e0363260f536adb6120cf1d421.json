{"remainingRequest": "E:\\java系统\\公司财务系统\\src\\main\\resources\\admin\\admin\\node_modules\\babel-loader\\lib\\index.js!E:\\java系统\\公司财务系统\\src\\main\\resources\\admin\\admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!E:\\java系统\\公司财务系统\\src\\main\\resources\\admin\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\java系统\\公司财务系统\\src\\main\\resources\\admin\\admin\\src\\components\\common\\ExcelFileUpload.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\java系统\\公司财务系统\\src\\main\\resources\\admin\\admin\\src\\components\\common\\ExcelFileUpload.vue", "mtime": 1754631104704}, {"path": "E:\\java系统\\公司财务系统\\src\\main\\resources\\admin\\admin\\babel.config.js", "mtime": 1711079474000}, {"path": "E:\\java系统\\公司财务系统\\src\\main\\resources\\admin\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754805254356}, {"path": "E:\\java系统\\公司财务系统\\src\\main\\resources\\admin\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754805257322}, {"path": "E:\\java系统\\公司财务系统\\src\\main\\resources\\admin\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754805254356}, {"path": "E:\\java系统\\公司财务系统\\src\\main\\resources\\admin\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754628160828}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["storage", "base", "data", "dialogVisible", "dialogImageUrl", "fileList", "fileUrlList", "myHeaders", "props", "mounted", "init", "get", "watch", "fileUrls", "val", "oldVal", "computed", "getActionUrl", "console", "log", "concat", "$base", "name", "action", "methods", "split", "fileArray", "for<PERSON>ach", "item", "index", "url", "file", "push", "setFileList", "handleBeforeUpload", "handleUploadSuccess", "res", "code", "length", "response", "$emit", "join", "$message", "success", "error", "msg", "handleUploadErr", "err", "handleRemove", "handleUploadPreview", "handleExceed", "files", "warning", "fileUrlArray", "token", "_this", "startsWith"], "sources": ["src/components/common/ExcelFileUpload.vue"], "sourcesContent": ["<template>\r\n  <div>\r\n    <!-- 上传文件组件 -->\r\n    <el-upload\r\n      ref=\"upload\"\r\n      :action=\"getActionUrl\"\r\n      list-type=\"picture-card\"\r\n      accept=\".xls,.xlsx\"\r\n      :limit=\"limit\"\r\n      :headers=\"myHeaders\"\r\n      :on-exceed=\"handleExceed\"\r\n      :on-preview=\"handleUploadPreview\"\r\n      :on-remove=\"handleRemove\"\r\n      :on-success=\"handleUploadSuccess\"\r\n      :on-error=\"handleUploadErr\"\r\n      :before-upload=\"handleBeforeUpload\"\r\n      :show-file-list=\"false\"\r\n    >\r\n      <i class=\"el-icon-plus\"></i>\r\n      <div slot=\"tip\" class=\"el-upload__tip\" style=\"color:#838fa1;\">{{tip}}</div>\r\n    </el-upload>\r\n    <el-dialog :visible.sync=\"dialogVisible\" size=\"tiny\" append-to-body>\r\n      <img width=\"100%\" :src=\"dialogImageUrl\" alt>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n<script>\r\nimport storage from \"@/utils/storage\";\r\nimport base from \"@/utils/base\";\r\nexport default {\r\n  data() {\r\n    return {\r\n      // 查看大图\r\n      dialogVisible: false,\r\n      // 查看大图\r\n      dialogImageUrl: \"\",\r\n      // 组件渲染图片的数组字段，有特殊格式要�?\r\n      fileList: [],\r\n      fileUrlList: [],\r\n      myHeaders:{}\r\n    };\r\n  },\r\n  props: [\"tip\", \"action\", \"limit\", \"multiple\", \"fileUrls\"],\r\n  mounted() {\r\n    this.init();\r\n    this.myHeaders= {\r\n      'Token':storage.get(\"Token\")\r\n    }\r\n  },\r\n  watch: {\r\n    fileUrls: function(val, oldVal) {\r\n      //   console.log(\"new: %s, old: %s\", val, oldVal);\r\n      this.init();\r\n    }\r\n  },\r\n  computed: {\r\n    // 计算属性的 getter\r\n    getActionUrl: function() {\r\n\t\tconsole.log(123)\r\n\t\tthis.fileList = []\r\n      // return base.url + this.action + \"?token=\" + storage.get(\"token\");\r\n      return `/${this.$base.name}/` + this.action;\r\n    }\r\n  },\r\n  methods: {\r\n    // 初始�?\r\n    init() {\r\n      //   console.log(this.fileUrls);\r\n      if (this.fileUrls) {\r\n        this.fileUrlList = this.fileUrls.split(\",\");\r\n        let fileArray = [];\r\n        this.fileUrlList.forEach(function(item, index) {\r\n          var url = item;\r\n          var name = index;\r\n          var file = {\r\n            name: name,\r\n            url: url\r\n          };\r\n          fileArray.push(file);\r\n        });\r\n        this.setFileList(fileArray);\r\n      }\r\n    },\r\n    handleBeforeUpload(file) {\r\n    \r\n    },\r\n    // 上传文件成功后执�?\r\n    handleUploadSuccess(res, file, fileList) {\r\n      if (res && res.code === 0) {\r\n        fileList[fileList.length - 1][\"url\"] = \"upload/\" + file.response.file;\r\n        this.setFileList(fileList);\r\n        this.$emit(\"change\", this.fileUrlList.join(\",\"));\r\n        this.$message.success(\"文件导入成功\");\r\n      } else {\r\n        this.$message.error(res.msg);\r\n      }\r\n    },\r\n    // 图片上传失败\r\n    handleUploadErr(err, file, fileList) {\r\n      this.$message.error(\"文件导入失败\");\r\n    },\r\n    // 移除图片\r\n    handleRemove(file, fileList) {\r\n      this.setFileList(fileList);\r\n      this.$emit(\"change\", this.fileUrlList.join(\",\"));\r\n    },\r\n    // 查看大图\r\n    handleUploadPreview(file) {\r\n      this.dialogImageUrl = file.url;\r\n      this.dialogVisible = true;\r\n    },\r\n    // 限制图片数量\r\n    handleExceed(files, fileList) {\r\n      this.$message.warning(`最多上�?{this.limit}张图片`);\r\n    },\r\n    // 重新对fileList进行赋�?\r\n    setFileList(fileList) {\r\n      var fileArray = [];\r\n      var fileUrlArray = [];\r\n      // 有些图片不是公开的，所以需要携带token信息做权限校�?\r\n      var token = storage.get(\"token\");\r\n      let _this = this;\r\n      fileList.forEach(function(item, index) {\r\n        var url = item.url.split(\"?\")[0];\r\n    if(!url.startsWith(\"http\")) {\r\n      url = _this.$base.url+url\r\n    }\r\n        var name = item.name;\r\n        var file = {\r\n          name: name,\r\n          url: url + \"?token=\" + token\r\n        };\r\n        fileArray.push(file);\r\n        fileUrlArray.push(url);\r\n      });\r\n      this.fileList = fileArray;\r\n      this.fileUrlList = fileUrlArray;\r\n    }\r\n  }\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n</style>\r\n\r\n"], "mappings": ";;;;;;;;;AA2BA,OAAAA,OAAA;AACA,OAAAC,IAAA;AACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,aAAA;MACA;MACAC,cAAA;MACA;MACAC,QAAA;MACAC,WAAA;MACAC,SAAA;IACA;EACA;EACAC,KAAA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,IAAA;IACA,KAAAH,SAAA;MACA,SAAAP,OAAA,CAAAW,GAAA;IACA;EACA;EACAC,KAAA;IACAC,QAAA,WAAAA,SAAAC,GAAA,EAAAC,MAAA;MACA;MACA,KAAAL,IAAA;IACA;EACA;EACAM,QAAA;IACA;IACAC,YAAA,WAAAA,aAAA;MACAC,OAAA,CAAAC,GAAA;MACA,KAAAd,QAAA;MACA;MACA,WAAAe,MAAA,MAAAC,KAAA,CAAAC,IAAA,cAAAC,MAAA;IACA;EACA;EACAC,OAAA;IACA;IACAd,IAAA,WAAAA,KAAA;MACA;MACA,SAAAG,QAAA;QACA,KAAAP,WAAA,QAAAO,QAAA,CAAAY,KAAA;QACA,IAAAC,SAAA;QACA,KAAApB,WAAA,CAAAqB,OAAA,WAAAC,IAAA,EAAAC,KAAA;UACA,IAAAC,GAAA,GAAAF,IAAA;UACA,IAAAN,IAAA,GAAAO,KAAA;UACA,IAAAE,IAAA;YACAT,IAAA,EAAAA,IAAA;YACAQ,GAAA,EAAAA;UACA;UACAJ,SAAA,CAAAM,IAAA,CAAAD,IAAA;QACA;QACA,KAAAE,WAAA,CAAAP,SAAA;MACA;IACA;IACAQ,kBAAA,WAAAA,mBAAAH,IAAA,GAEA;IACA;IACAI,mBAAA,WAAAA,oBAAAC,GAAA,EAAAL,IAAA,EAAA1B,QAAA;MACA,IAAA+B,GAAA,IAAAA,GAAA,CAAAC,IAAA;QACAhC,QAAA,CAAAA,QAAA,CAAAiC,MAAA,2BAAAP,IAAA,CAAAQ,QAAA,CAAAR,IAAA;QACA,KAAAE,WAAA,CAAA5B,QAAA;QACA,KAAAmC,KAAA,gBAAAlC,WAAA,CAAAmC,IAAA;QACA,KAAAC,QAAA,CAAAC,OAAA;MACA;QACA,KAAAD,QAAA,CAAAE,KAAA,CAAAR,GAAA,CAAAS,GAAA;MACA;IACA;IACA;IACAC,eAAA,WAAAA,gBAAAC,GAAA,EAAAhB,IAAA,EAAA1B,QAAA;MACA,KAAAqC,QAAA,CAAAE,KAAA;IACA;IACA;IACAI,YAAA,WAAAA,aAAAjB,IAAA,EAAA1B,QAAA;MACA,KAAA4B,WAAA,CAAA5B,QAAA;MACA,KAAAmC,KAAA,gBAAAlC,WAAA,CAAAmC,IAAA;IACA;IACA;IACAQ,mBAAA,WAAAA,oBAAAlB,IAAA;MACA,KAAA3B,cAAA,GAAA2B,IAAA,CAAAD,GAAA;MACA,KAAA3B,aAAA;IACA;IACA;IACA+C,YAAA,WAAAA,aAAAC,KAAA,EAAA9C,QAAA;MACA,KAAAqC,QAAA,CAAAU,OAAA;IACA;IACA;IACAnB,WAAA,WAAAA,YAAA5B,QAAA;MACA,IAAAqB,SAAA;MACA,IAAA2B,YAAA;MACA;MACA,IAAAC,KAAA,GAAAtD,OAAA,CAAAW,GAAA;MACA,IAAA4C,KAAA;MACAlD,QAAA,CAAAsB,OAAA,WAAAC,IAAA,EAAAC,KAAA;QACA,IAAAC,GAAA,GAAAF,IAAA,CAAAE,GAAA,CAAAL,KAAA;QACA,KAAAK,GAAA,CAAA0B,UAAA;UACA1B,GAAA,GAAAyB,KAAA,CAAAlC,KAAA,CAAAS,GAAA,GAAAA,GAAA;QACA;QACA,IAAAR,IAAA,GAAAM,IAAA,CAAAN,IAAA;QACA,IAAAS,IAAA;UACAT,IAAA,EAAAA,IAAA;UACAQ,GAAA,EAAAA,GAAA,eAAAwB;QACA;QACA5B,SAAA,CAAAM,IAAA,CAAAD,IAAA;QACAsB,YAAA,CAAArB,IAAA,CAAAF,GAAA;MACA;MACA,KAAAzB,QAAA,GAAAqB,SAAA;MACA,KAAApB,WAAA,GAAA+C,YAAA;IACA;EACA;AACA", "ignoreList": []}]}