{"remainingRequest": "E:\\java系统\\公司财务系统\\src\\main\\resources\\admin\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\java系统\\公司财务系统\\src\\main\\resources\\admin\\admin\\src\\components\\index\\IndexAsideStatic.vue?vue&type=style&index=0&id=0175fa3e&lang=scss&scoped=true", "dependencies": [{"path": "E:\\java系统\\公司财务系统\\src\\main\\resources\\admin\\admin\\src\\components\\index\\IndexAsideStatic.vue", "mtime": 1754631104815}, {"path": "E:\\java系统\\公司财务系统\\src\\main\\resources\\admin\\admin\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1754805255591}, {"path": "E:\\java系统\\公司财务系统\\src\\main\\resources\\admin\\admin\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1754628163326}, {"path": "E:\\java系统\\公司财务系统\\src\\main\\resources\\admin\\admin\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1754628158457}, {"path": "E:\\java系统\\公司财务系统\\src\\main\\resources\\admin\\admin\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1754628153247}, {"path": "E:\\java系统\\公司财务系统\\src\\main\\resources\\admin\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754805254356}, {"path": "E:\\java系统\\公司财务系统\\src\\main\\resources\\admin\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754628160828}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["IndexAsideStatic.vue"], "names": [], "mappings": ";AAi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file": "IndexAsideStatic.vue", "sourceRoot": "src/components/index", "sourcesContent": ["<template>\r\n\t<div class=\"menu-preview\">\r\n\t\t<!-- 竖向-2 -->\r\n\t\t<el-scrollbar :wrap-class=\"isCollapse ? 'scrollbar-wrapper scrollbar-wrapper-close' : 'scrollbar-wrapper scrollbar-wrapper-open'\">\r\n\t\t  <el-button :style=\"verticalStyle2[isCollapse?'close':'open'].btn.default\" type=\"primary\" @click=\"collapse\">\r\n\t\t    <span class=\"icon iconfont\"\r\n\t\t      :style=\"verticalStyle2[isCollapse?'close':'open'].btn.icon.default\"\r\n\t\t      :class=\"verticalStyle2[isCollapse?'close':'open'].btn.icon.text\"></span>{{verticalStyle2[isCollapse?'close':'open'].btn.text}}\r\n\t\t  </el-button>\r\n\t\t  <div class=\"userinfo\"\r\n\t\t    :style=\"verticalStyle2[isCollapse?'close':'open'].userinfo.box.default\">\r\n\t\t    <el-image v-if=\"avatar\" :style=\"verticalStyle2[isCollapse?'close':'open'].userinfo.img.default\" :src=\"avatar?this.$base.url + avatar:require('@/assets/img/avator.png')\" fit=\"cover\"></el-image>\r\n\t\t    <div :style=\"verticalStyle2[isCollapse?'close':'open'].userinfo.nickname.default\">\r\n\t\t      {{this.$storage.get('adminName')}}</div>\r\n\t\t  </div>\r\n\t\t  <el-menu :default-active=\"activeMenu\" :unique-opened=\"true\" :style=\"verticalStyle2[isCollapse?'close':'open'].menu.box.default\"\r\n\t\t    class=\"el-menu-vertical-2\" :collapse-transition=\"false\" :collapse=\"isCollapse\">\r\n\t\t    <el-menu-item class=\"home\" :popper-append-to-body=\"false\" popper-class=\"home\" @click.native=\"menuHandler('')\" :style=\"verticalStyle2[isCollapse?'close':'open'].home.one.box.default\" index=\"/\">\r\n\t\t      <div class=\"el-tooltip\">\r\n\t\t        <i :style=\"verticalStyle2[isCollapse?'close':'open'].home.one.icon.default\"\r\n\t\t          class=\"icon iconfont icon-shouye-zhihui\"></i>\r\n\t\t        <span :style=\"verticalStyle2[isCollapse?'close':'open'].home.one.title.default\"\r\n\t\t          slot=\"title\">{{verticalStyle2.open.home.one.title.text}}</span>\r\n\t\t      </div>\r\n\t\t    </el-menu-item>\r\n\t\t    <el-submenu class=\"user\" popper-class=\"user\" :popper-append-to-body=\"false\"\r\n\t\t      :style=\"verticalStyle2[isCollapse?'close':'open'].user.one.box.default\" index=\"1\">\r\n\t\t      <template slot=\"title\">\r\n\t\t        <i :style=\"verticalStyle2[isCollapse?'close':'open'].user.one.icon.default\"\r\n\t\t          class=\"icon iconfont icon-kuaijiezhifu\"></i>\r\n\t\t        <span :style=\"verticalStyle2[isCollapse?'close':'open'].user.one.title.default\"\r\n\t\t          slot=\"title\">{{verticalStyle2.open.user.one.title.text}}</span>\r\n\t\t      </template>\r\n\t\t      <el-menu-item index=\"/updatePassword\" @click=\"menuHandler('updatePassword')\">修改密码</el-menu-item>\r\n\t\t      <el-menu-item index=\"/center\" @click=\"menuHandler('center')\">个人信息</el-menu-item>\r\n\t\t    </el-submenu>\r\n\t\t\t<template v-for=\"(menu,index) in menuList.backMenu\">\r\n\t\t\t\t<el-submenu v-if=\"menu.child.length > 1 || !verticalIsMultiple\" class=\"other\" popper-class=\"other\" :popper-append-to-body=\"false\" :style=\"verticalStyle2[isCollapse?'close':'open'].menu.one.box.default\" :index=\"index+2+''\">\r\n\t\t\t\t\t<template slot=\"title\">\r\n\t\t\t\t\t\t<i :style=\"verticalStyle2[isCollapse?'close':'open'].menu.one.icon.default\" class=\"el-icon-menu\" :class=\"icons[index]\"></i>\r\n\t\t\t\t\t\t<span :style=\"verticalStyle2[isCollapse?'close':'open'].menu.one.title.default\" slot=\"title\">{{menu.menu + (verticalFlag ? '管理' : '')}}</span>\r\n\t\t\t\t\t</template>\r\n\t\t\t\t\t<el-menu-item v-for=\"(child,sort) in menu.child\" :key=\"sort\" :index=\"'/'+child.tableName\" @click=\"menuHandler(child.tableName)\">{{ child.menu }}</el-menu-item>\r\n\t\t\t\t</el-submenu>\r\n\t\t\t\t<el-menu-item v-if=\"menu.child.length <= 1 && verticalIsMultiple\" class=\"other\" popper-class=\"other\" :style=\"verticalStyle2[isCollapse?'close':'open'].menu.one.box.default\" @click=\"menuHandler(menu.child[0].tableName)\" :index=\"'/'+menu.child[0].tableName\">\r\n\t\t\t\t  <div class=\"el-tooltip\">\r\n\t\t\t\t    <i :style=\"verticalStyle2[isCollapse?'close':'open'].menu.one.icon.default\" class=\"el-icon-menu\" :class=\"icons[index]\"></i>\r\n\t\t\t\t    <span :style=\"verticalStyle2[isCollapse?'close':'open'].menu.one.title.default\" slot=\"title\">{{menu.child[0].menu + (verticalFlag ? '管理' : '')}}</span>\r\n\t\t\t\t  </div>\r\n\t\t\t\t</el-menu-item>\r\n\t\t\t</template>\r\n\t\t  </el-menu>\r\n\t\t</el-scrollbar>\r\n\r\n\r\n\t</div>\r\n</template>\r\n\r\n<script>\r\nimport menu from '@/utils/menu'\r\nexport default {\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\tmenuList: [],\r\n\t\t\tdynamicMenuRoutes: [],\r\n\t\t\trole: '',\r\n\t\t\tuser: null,\r\n\t\t\tavatar:'',\r\n\t\t\ticons: [\r\n\t\t\t\t'el-icon-s-cooperation',\r\n\t\t\t\t'el-icon-s-order',\r\n\t\t\t\t'el-icon-s-platform',\r\n\t\t\t\t'el-icon-s-fold',\r\n\t\t\t\t'el-icon-s-unfold',\r\n\t\t\t\t'el-icon-s-operation',\r\n\t\t\t\t'el-icon-s-promotion',\r\n\t\t\t\t'el-icon-s-release',\r\n\t\t\t\t'el-icon-s-ticket',\r\n\t\t\t\t'el-icon-s-management',\r\n\t\t\t\t'el-icon-s-open',\r\n\t\t\t\t'el-icon-s-shop',\r\n\t\t\t\t'el-icon-s-marketing',\r\n\t\t\t\t'el-icon-s-flag',\r\n\t\t\t\t'el-icon-s-comment',\r\n\t\t\t\t'el-icon-s-finance',\r\n\t\t\t\t'el-icon-s-claim',\r\n\t\t\t\t'el-icon-s-custom',\r\n\t\t\t\t'el-icon-s-opportunity',\r\n\t\t\t\t'el-icon-s-data',\r\n\t\t\t\t'el-icon-s-check',\r\n\t\t\t\t'el-icon-s-grid',\r\n\t\t\t\t'el-icon-menu',\r\n\t\t\t\t'el-icon-chat-dot-square',\r\n\t\t\t\t'el-icon-message',\r\n\t\t\t\t'el-icon-postcard',\r\n\t\t\t\t'el-icon-position',\r\n\t\t\t\t'el-icon-microphone',\r\n\t\t\t\t'el-icon-close-notification',\r\n\t\t\t\t'el-icon-bangzhu',\r\n\t\t\t\t'el-icon-time',\r\n\t\t\t\t'el-icon-odometer',\r\n\t\t\t\t'el-icon-crop',\r\n\t\t\t\t'el-icon-aim',\r\n\t\t\t\t'el-icon-switch-button',\r\n\t\t\t\t'el-icon-full-screen',\r\n\t\t\t\t'el-icon-copy-document',\r\n\t\t\t\t'el-icon-mic',\r\n\t\t\t\t'el-icon-stopwatch',\r\n\t\t\t],\r\n\t\t\tmenulistBorderBottom: {},\r\n\t\t\tverticalFlag: false,\r\n\t\t\tisCollapse: false,\r\n\t\t\tverticalStyle2: {\"isCollapse\":false,\"close\":{\"contentBox\":{\"hover\":{},\"active\":{\"margin\":\"0 0 0 54px\"},\"default\":{\"minHeight\":\"100%\",\"padding\":\"0\",\"margin\":\"0 0 0 64px\",\"position\":\"relative\",\"display\":\"block\"}},\"box\":{\"hover\":{},\"active\":{\"width\":\"54px\"},\"default\":{\"boxShadow\":\"1px 0 6px  rgba(64, 158, 255, .3)\",\"overflow\":\"hidden\",\"top\":\"0\",\"left\":\"0\",\"background\":\"#304156\",\"bottom\":\"0\",\"width\":\"64px\",\"fontSize\":\"0px\",\"position\":\"fixed\",\"transition\":\"width 0.3s\",\"height\":\"100%\",\"zIndex\":\"1001\"}},\"menu\":{\"two\":{\"title\":{\"hover\":{\"padding\":\"0 20px\",\"backgroundColor\":\"red !important\",\"lineHeight\":\"56px\",\"color\":\"#fff\",\"height\":\"56px\"},\"active\":{\"padding\":\"0 20px\",\"backgroundColor\":\"blue !important\",\"lineHeight\":\"56px\",\"color\":\"#fff\",\"height\":\"56px\"},\"default\":{\"padding\":\"0 20px\",\"backgroundColor\":\"#fff\",\"lineHeight\":\"56px\",\"color\":\"#666\",\"height\":\"56px\"}},\"box\":{\"hover\":{},\"default\":{\"border\":\"none\"}}},\"box\":{\"hover\":{},\"default\":{\"border\":0,\"padding\":\"0\",\"listStyle\":\"none\",\"margin\":\"0\",\"position\":\"relative\",\"background\":\"#FFF\"}},\"one\":{\"box1\":{\"hover\":{\"color\":\"#fff\",\"background\":\"blue\"},\"active\":{\"color\":\"#fff\",\"background\":\"blue\"},\"default\":{\"cursor\":\"pointer\",\"padding\":\"0 20px\",\"whiteSpace\":\"nowrap\",\"position\":\"relative\",\"color\":\"#333\",\"background\":\"#fff\"}},\"icon\":{\"hover\":{},\"default\":{\"verticalAlign\":\"middle\",\"margin\":\"0\",\"color\":\"inherit\",\"textAlign\":\"center\",\"display\":\"inline-block\",\"width\":\"24px\",\"fontSize\":\"18px\"},\"flag\":true},\"box\":{\"hover\":{},\"default\":{\"padding\":\"0\",\"listStyle\":\"none\",\"margin\":\"0\"}},\"title\":{\"hover\":{},\"default\":{\"width\":\"0\",\"verticalAlign\":\"middle\",\"fontSize\":\"14px\",\"color\":\"inherit\",\"height\":\"0\"}},\"arrow\":{\"hover\":{},\"default\":{\"verticalAlign\":\"middle\",\"margin\":\"-7px 0 0 0\",\"top\":\"50%\",\"color\":\"inherit\",\"display\":\"none\",\"fontSize\":\"12px\",\"position\":\"absolute\",\"right\":\"20px\"}}}},\"btn\":{\"icon\":{\"hover\":{},\"default\":{\"margin\":\"0 2px\",\"fontSize\":\"14px\",\"color\":\"#fff\",\"height\":\"40px\"},\"text\":\"icon-xihuan\"},\"hover\":{\"opacity\":\"0.8\"},\"default\":{\"border\":\"0\",\"cursor\":\"pointer\",\"padding\":\"0 9px\",\"margin\":\"0 0 10px\",\"outline\":\"none\",\"color\":\"#fff\",\"borderRadius\":\"0\",\"background\":\"rgba(64, 158, 255, 1)\",\"width\":\"auto\",\"fontSize\":\"14px\",\"height\":\"40px\"},\"text\":\"切换\"},\"user\":{\"two\":{\"title\":{\"hover\":{\"padding\":\"0 20px\",\"backgroundColor\":\"red !important\",\"lineHeight\":\"56px\",\"color\":\"#fff\",\"height\":\"56px\"},\"active\":{\"padding\":\"0 20px\",\"backgroundColor\":\"blue !important\",\"lineHeight\":\"56px\",\"color\":\"#fff\",\"height\":\"56px\"},\"default\":{\"padding\":\"0 20px\",\"backgroundColor\":\"#fff\",\"lineHeight\":\"56px\",\"color\":\"#656\",\"height\":\"56px\"}},\"box\":{\"hover\":{},\"default\":{\"border\":\"none\"}}},\"one\":{\"box1\":{\"hover\":{\"color\":\"#fff\",\"background\":\"blue\"},\"active\":{\"color\":\"#fff\",\"background\":\"blue\"},\"default\":{\"cursor\":\"pointer\",\"padding\":\"0 20px\",\"whiteSpace\":\"nowrap\",\"position\":\"relative\",\"color\":\"#323\",\"background\":\"#fff\"}},\"icon\":{\"hover\":{},\"default\":{\"verticalAlign\":\"middle\",\"margin\":\"0\",\"color\":\"inherit\",\"textAlign\":\"center\",\"display\":\"inline-block\",\"width\":\"24px\",\"fontSize\":\"18px\"},\"flag\":true,\"text\":\"icon-kuaijiezhifu\"},\"box\":{\"hover\":{},\"default\":{\"padding\":\"0\",\"listStyle\":\"none\",\"margin\":\"0\"}},\"title\":{\"hover\":{},\"default\":{\"width\":\"0\",\"verticalAlign\":\"middle\",\"fontSize\":\"14px\",\"color\":\"inherit\",\"height\":\"0\"}},\"arrow\":{\"hover\":{},\"default\":{\"verticalAlign\":\"middle\",\"margin\":\"-7px 0 0 0\",\"top\":\"50%\",\"color\":\"inherit\",\"display\":\"none\",\"fontSize\":\"12px\",\"position\":\"absolute\",\"right\":\"20px\"}}}},\"userinfo\":{\"nickname\":{\"hover\":{},\"default\":{\"fontSize\":\"24px\",\"lineHeight\":\"1.5\",\"color\":\"#fff\",\"textAlign\":\"center\"}},\"img\":{\"hover\":{},\"default\":{\"width\":\"100%\",\"objectFit\":\"cover\",\"borderRadius\":\"20px\",\"display\":\"block\",\"height\":\"170px\"}},\"box\":{\"hover\":{},\"default\":{\"width\":\"100%\",\"padding\":\"20px\",\"display\":\"none\",\"height\":\"auto\"}}},\"home\":{\"two\":{\"title\":{\"hover\":{\"padding\":\"0 20px\",\"backgroundColor\":\"red !important\",\"lineHeight\":\"56px\",\"color\":\"#fff\",\"height\":\"56px\"},\"active\":{\"padding\":\"0 20px\",\"backgroundColor\":\"blue !important\",\"lineHeight\":\"56px\",\"color\":\"#fff\",\"height\":\"56px\"},\"default\":{\"padding\":\"0 20px\",\"backgroundColor\":\"#fff\",\"lineHeight\":\"56px\",\"color\":\"#646\",\"height\":\"56px\"}},\"box\":{\"hover\":{},\"default\":{\"border\":\"none\"}}},\"one\":{\"box1\":{\"hover\":{\"color\":\"#fff\",\"background\":\"blue\"},\"active\":{\"color\":\"#fff\",\"background\":\"blue\"},\"default\":{\"cursor\":\"pointer\",\"padding\":\"0 20px\",\"whiteSpace\":\"nowrap\",\"position\":\"relative\",\"color\":\"#313\",\"background\":\"#fff\"}},\"icon\":{\"hover\":{},\"default\":{\"verticalAlign\":\"middle\",\"margin\":\"0\",\"color\":\"inherit\",\"textAlign\":\"center\",\"display\":\"inline-block\",\"width\":\"24px\",\"fontSize\":\"18px\"},\"flag\":true,\"text\":\"icon-shouye-zhihui\"},\"box\":{\"hover\":{},\"default\":{\"padding\":\"0\",\"listStyle\":\"none\",\"margin\":\"0\"}},\"title\":{\"hover\":{},\"default\":{\"width\":\"0\",\"verticalAlign\":\"middle\",\"fontSize\":\"14px\",\"color\":\"inherit\",\"height\":\"0\"}},\"arrow\":{\"hover\":{},\"default\":{\"verticalAlign\":\"middle\",\"margin\":\"-7px 0 0 0\",\"top\":\"50%\",\"color\":\"inherit\",\"display\":\"none\",\"fontSize\":\"12px\",\"position\":\"absolute\",\"right\":\"20px\"}}}}},\"open\":{\"contentBox\":{\"hover\":{},\"default\":{\"minHeight\":\"100%\",\"padding\":\"0\",\"margin\":\"0 0 0 210px\",\"background\":\"rgb(235, 242, 251)\",\"display\":\"block\",\"overflow-x\":\"hidden\",\"position\":\"relative\"}},\"box\":{\"hover\":{},\"default\":{\"boxShadow\":\"1px 0 6px  rgba(64, 158, 255, .3)\",\"padding\":\"0 0 60px\",\"overflow\":\"hidden\",\"top\":\"60px\",\"left\":\"0\",\"background\":\"#0d102c\",\"bottom\":\"0\",\"width\":\"210px\",\"position\":\"fixed\",\"transition\":\"width 0.3s\",\"height\":\"100%\",\"zIndex\":\"1001\"}},\"menu\":{\"two\":{\"title\":{\"hover\":{\"padding\":\"0 40px\",\"lineHeight\":\"50px\",\"color\":\"#2ddcd3\",\"height\":\"50px\"},\"active\":{\"padding\":\"0 40px\",\"lineHeight\":\"50px\",\"color\":\"#2ddcd3\",\"height\":\"50px\"},\"default\":{\"padding\":\"0 40px\",\"lineHeight\":\"50px\",\"color\":\"#fff\",\"background\":\"#0d102c\",\"height\":\"50px\"}},\"box\":{\"hover\":{},\"default\":{\"border\":\"none\"}}},\"box\":{\"hover\":{},\"default\":{\"border\":0,\"padding\":\"0 0 60px\",\"listStyle\":\"none\",\"margin\":\"0\",\"background\":\"none\",\"flexDirection\":\"column\",\"display\":\"flex\",\"position\":\"relative\"}},\"one\":{\"box1\":{\"hover\":{\"color\":\"#2ddcd3\"},\"active\":{\"color\":\"#2ddcd3\"},\"default\":{\"cursor\":\"pointer\",\"padding\":\"0 20px\",\"borderColor\":\"#20233d\",\"whiteSpace\":\"nowrap\",\"color\":\"#fff\",\"background\":\"#0d102c\",\"borderWidth\":\"1px 0\",\"position\":\"relative\",\"borderStyle\":\"solid\"}},\"icon\":{\"hover\":{},\"default\":{\"verticalAlign\":\"middle\",\"margin\":\"0 3px\",\"color\":\"inherit\",\"borderRadius\":\"100%\",\"textAlign\":\"center\",\"background\":\"linear-gradient(120deg, #0f75ff 60%, #2ddcd3  100%)\",\"display\":\"inline-block\",\"width\":\"34px\",\"fontSize\":\"18px\",\"lineHeight\":\"30px\",\"height\":\"30px\"},\"flag\":true},\"box\":{\"hover\":{},\"default\":{\"padding\":\"0\",\"listStyle\":\"none\",\"margin\":\"0\",\"borderColor\":\"#20233d\",\"borderStyle\":\"solid\",\"borderWidth\":\"0\"}},\"title\":{\"hover\":{},\"default\":{\"color\":\"inherit\",\"verticalAlign\":\"middle\",\"fontSize\":\"14px\"}},\"arrow\":{\"hover\":{},\"default\":{\"verticalAlign\":\"middle\",\"margin\":\"-7px 0 0 0\",\"top\":\"50%\",\"color\":\"inherit\",\"fontSize\":\"12px\",\"position\":\"absolute\",\"right\":\"20px\"}}}},\"btn\":{\"icon\":{\"hover\":{},\"default\":{\"margin\":\"0 2px\",\"fontSize\":\"14px\",\"color\":\"#fff\",\"height\":\"40px\"},\"text\":\"icon-xihuan\"},\"hover\":{\"opacity\":\"0.8\"},\"default\":{\"border\":\"0\",\"cursor\":\"pointer\",\"padding\":\"0 9px\",\"outline\":\"none\",\"color\":\"#fff\",\"borderRadius\":\"4px\",\"background\":\"rgba(64, 158, 255, 1)\",\"display\":\"none\",\"width\":\"auto\",\"fontSize\":\"14px\",\"height\":\"40px\"},\"text\":\"切换\"},\"user\":{\"two\":{\"title\":{\"hover\":{\"padding\":\"0 40px\",\"lineHeight\":\"50px\",\"color\":\"#2ddcd3\",\"height\":\"50px\"},\"active\":{\"padding\":\"0 40px\",\"lineHeight\":\"50px\",\"color\":\"#2ddcd3\",\"height\":\"50px\"},\"default\":{\"padding\":\"0 40px\",\"lineHeight\":\"50px\",\"color\":\"#fff\",\"background\":\"#0d102c\",\"height\":\"50px\"}},\"box\":{\"hover\":{},\"default\":{\"border\":\"none\"}}},\"one\":{\"box1\":{\"hover\":{\"color\":\"#2ddcd3\"},\"active\":{\"color\":\"#2ddcd3\"},\"default\":{\"cursor\":\"pointer\",\"padding\":\"0 20px\",\"borderColor\":\"#20233d\",\"whiteSpace\":\"nowrap\",\"color\":\"#fff\",\"background\":\"#0d102c\",\"borderWidth\":\"1px 0\",\"position\":\"relative\",\"borderStyle\":\"solid\"}},\"icon\":{\"hover\":{},\"default\":{\"verticalAlign\":\"middle\",\"margin\":\"0 3px\",\"color\":\"inherit\",\"borderRadius\":\"100%\",\"textAlign\":\"center\",\"background\":\"linear-gradient(120deg, #0f75ff 60%, #2ddcd3  100%)\",\"display\":\"inline-block\",\"width\":\"34px\",\"fontSize\":\"18px\",\"lineHeight\":\"30px\",\"height\":\"30px\"},\"flag\":true,\"text\":\"icon-kuaijiezhifu\"},\"box\":{\"hover\":{},\"default\":{\"padding\":\"0\",\"listStyle\":\"none\",\"margin\":\"0\",\"order\":\"1\"}},\"title\":{\"hover\":{},\"default\":{\"color\":\"inherit\",\"verticalAlign\":\"middle\",\"fontSize\":\"14px\"},\"text\":\"我的信息\"},\"arrow\":{\"hover\":{},\"default\":{\"verticalAlign\":\"middle\",\"margin\":\"-7px 0 0 0\",\"top\":\"50%\",\"color\":\"inherit\",\"fontSize\":\"12px\",\"position\":\"absolute\",\"right\":\"20px\"}}}},\"userinfo\":{\"nickname\":{\"hover\":{},\"default\":{\"fontSize\":\"24px\",\"lineHeight\":\"1.5\",\"color\":\"#fff\",\"textAlign\":\"center\"}},\"img\":{\"hover\":{},\"default\":{\"boxShadow\":\"0 0 0 5px rgba(255, 255, 255, .3)\",\"margin\":\"0 10px 0 0\",\"objectFit\":\"cover\",\"borderRadius\":\"50%\",\"display\":\"block\",\"width\":\"50px\",\"height\":\"50px\"}},\"box\":{\"hover\":{},\"default\":{\"width\":\"100%\",\"padding\":\"20px\",\"alignItems\":\"center\",\"display\":\"flex\",\"height\":\"10vh\"}}},\"home\":{\"two\":{\"title\":{\"hover\":{\"padding\":\"0 40px\",\"lineHeight\":\"50px\",\"color\":\"#2ddcd3\",\"height\":\"50px\"},\"active\":{\"padding\":\"0 40px\",\"lineHeight\":\"50px\",\"color\":\"#2ddcd3\",\"height\":\"50px\"},\"default\":{\"padding\":\"0 40px\",\"lineHeight\":\"50px\",\"color\":\"#fff\",\"background\":\"#0d102c\",\"height\":\"50px\"}},\"box\":{\"hover\":{},\"default\":{\"border\":\"none\"}}},\"one\":{\"box1\":{\"hover\":{\"color\":\"#2ddcd3\"},\"active\":{\"color\":\"#2ddcd3\"},\"default\":{\"cursor\":\"pointer\",\"padding\":\"0 20px\",\"borderColor\":\"red\",\"whiteSpace\":\"nowrap\",\"color\":\"#fff\",\"background\":\"#0d102c\",\"borderWidth\":\"0 0 1px 0\",\"position\":\"relative\",\"borderStyle\":\"solid\"}},\"icon\":{\"hover\":{},\"default\":{\"verticalAlign\":\"middle\",\"margin\":\"0 3px\",\"color\":\"#fff\",\"borderRadius\":\"100%\",\"textAlign\":\"center\",\"background\":\"linear-gradient(120deg, #0f75ff 60%, #2ddcd3  100%)\",\"display\":\"inline-block\",\"width\":\"34px\",\"fontSize\":\"18px\",\"lineHeight\":\"30px\",\"height\":\"30px\"},\"flag\":true,\"text\":\"icon-shouye-zhihui\"},\"box\":{\"hover\":{},\"default\":{\"padding\":\"0\",\"listStyle\":\"none\",\"margin\":\"0\",\"borderColor\":\"#20233d\",\"borderStyle\":\"solid\",\"borderWidth\":\"1px 0\"}},\"title\":{\"hover\":{},\"default\":{\"color\":\"inherit\",\"verticalAlign\":\"middle\",\"fontSize\":\"14px\"},\"text\":\"系统首页\"},\"arrow\":{\"hover\":{},\"default\":{\"verticalAlign\":\"middle\",\"margin\":\"-7px 0 0 0\",\"top\":\"50%\",\"color\":\"inherit\",\"fontSize\":\"12px\",\"position\":\"absolute\",\"right\":\"20px\"}}}}}},\r\n\t\t\tverticalIsMultiple: false,\r\n\t\t}\r\n\t},\r\n\tcomputed: {\r\n\t\tactiveMenu() {\r\n\t\t\tconst route = this.$route\r\n\t\t\tconsole.log(route)\r\n\t\t\tconst {\r\n\t\t\t\tmeta,\r\n\t\t\t\tpath\r\n\t\t\t} = route\r\n\t\t\t// if st path, the sidebar will highlight the path you sete\r\n\t\t\tif (meta.activeMenu) {\r\n\t\t\t\treturn meta.activeMenu\r\n\t\t\t}\r\n\t\t\treturn path\r\n\t\t}\r\n\t},\r\n\twatch:{\r\n\t\tavatar(){\r\n\t\t\tthis.$forceUpdate()\r\n\t\t},\r\n\t},\r\n\tmounted() {\r\n\t\tconst menus = menu.list()\r\n\t\tif(menus) {\r\n\t\t\tthis.menuList = menus\r\n\t\t} else {\r\n\t\t\tlet params = {\r\n\t\t\t\tpage: 1,\r\n\t\t\t\tlimit: 1,\r\n\t\t\t\tsort: 'id',\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\tthis.$http({\r\n\t\t\t\turl: \"menu/list\",\r\n\t\t\t\tmethod: \"get\",\r\n\t\t\t\tparams: params\r\n\t\t\t}).then(({\r\n\t\t\t\tdata\r\n\t\t\t}) => {\r\n\t\t\t\tif (data && data.code === 0) {\r\n\t\t\t\t\tthis.menuList = JSON.parse(data.data.list[0].menujson);\r\n\t\t\t\t\tthis.$storage.set(\"menus\", this.menuList);\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t}\r\n\t\tthis.role = this.$storage.get('role')\r\n\t\t\r\n\t\tfor(let i=0;i<this.menuList.length;i++) {\r\n\t\t\tif(this.menuList[i].roleName == this.role) {\r\n\t\t\t\tthis.menuList = this.menuList[i];\r\n\t\t\t\tbreak;\r\n\t\t\t}\r\n\t\t}\r\n\t\tthis.styleChange()\r\n\t\t\r\n\t\tlet sessionTable = this.$storage.get(\"sessionTable\")\r\n\t\tthis.$http({\r\n\t\t\turl: sessionTable + '/session',\r\n\t\t\tmethod: \"get\"\r\n\t\t}).then(({\r\n\t\t\tdata\r\n\t\t}) => {\r\n\t\t\tif (data && data.code === 0) {\r\n\t\t\t\tif(sessionTable == 'yuangong') {\r\n\t\t\t\t\tthis.avatar = data.data.touxiang\r\n\t\t\t\t}\r\n\t\t\t\tif(sessionTable=='users') {\r\n\t\t\t\t\tthis.avatar = data.data.image\r\n\t\t\t\t}\r\n\t\t\t\tthis.user = data.data;\r\n\t\t\t} else {\r\n\t\t\t\tlet message = this.$message\r\n\t\t\t\tmessage.error(data.msg);\r\n\t\t\t}\r\n\t\t});\r\n\t},\r\n\tcreated(){\r\n\t\tthis.icons.sort(()=>{\r\n\t\t\treturn (0.5-Math.random())\r\n\t\t})\r\n\t},\r\n\tmethods: {\r\n\t\tcollapse() {\r\n\t\t  this.isCollapse = !this.isCollapse\r\n\t\t  this.$emit('oncollapsechange', this.isCollapse)\r\n\t\t},\r\n\t\tstyleChange() {\r\n\t\t\tthis.$nextTick(() => {\r\n\t\t\t\t\t\t\t\tdocument.querySelectorAll('.el-menu-vertical-demo .el-submenu .el-menu').forEach(el => {\r\n\t\t\t\t  el.removeAttribute('style')\r\n\t\t\t\t  const icon = {\"border\":\"none\",\"display\":\"none\"}\r\n\t\t\t\t  Object.keys(icon).forEach((key) => {\r\n\t\t\t\t\tel.style[key] = icon[key]\r\n\t\t\t\t  })\r\n\t\t\t\t})\r\n\t\t\t\t\t\t\t\t\t\t\t})\r\n\t\t},\r\n\t\tmenuHandler(name) {\r\n\t\t\tlet router = this.$router\r\n\t\t\tname = '/'+name\r\n\t\t\trouter.push(name)\r\n\t\t},\r\n\t}\r\n}\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n\t.menu-preview {\r\n\t  .el-scrollbar {\r\n\t    height: 100%;\r\n\t\r\n\t    & :deep .scrollbar-wrapper {\r\n\t      overflow-x: hidden;\r\n\t    }\r\n\t\t\r\n\t\t\t\t// 竖向\r\n\t\t.el-menu-vertical-demo {\r\n\t\t  .el-submenu:first-of-type :deep .el-submenu__title .el-submenu__icon-arrow {\r\n\t\t    display: none;\r\n\t\t  }\r\n\t\t}\r\n\t\t\r\n\t\t.el-menu-vertical-demo>.el-menu-item {\r\n\t\t\t\t  \t\t\t\t  cursor: pointer;\r\n\t\t\t\t  \t\t\t\t  padding: 0 20px;\r\n\t\t\t\t  \t\t\t\t  color: #333;\r\n\t\t\t\t  \t\t\t\t  white-space: nowrap;\r\n\t\t\t\t  \t\t\t\t  background: #fff;\r\n\t\t\t\t  \t\t\t\t  position: relative;\r\n\t\t\t\t  \t\t}\r\n\t\t\r\n\t\t.el-menu-vertical-demo>.el-menu-item:hover {\r\n\t\t\t\t\t\tcolor: #fff;\r\n\t\t\t\t\t\tbackground: blue;\r\n\t\t\t\t\t}\r\n\t\t\r\n\t\t.el-menu-vertical-demo .el-submenu :deep .el-submenu__title {\r\n\t\t\t\t\t\tcursor: pointer;\r\n\t\t\t\t\t\tpadding: 0 20px;\r\n\t\t\t\t\t\tcolor: #333;\r\n\t\t\t\t\t\twhite-space: nowrap;\r\n\t\t\t\t\t\tbackground: #fff;\r\n\t\t\t\t\t\tposition: relative;\r\n\t\t\t\t\t}\r\n\t\t\r\n\t\t.el-menu-vertical-demo .el-submenu :deep .el-submenu__title:hover {\r\n\t\t\t\t\t\tcolor: #fff;\r\n\t\t\t\t\t\tbackground: blue;\r\n\t\t\t\t\t}\r\n\t\t\r\n\t\t.el-menu-vertical-demo .el-submenu :deep .el-submenu__title .el-submenu__icon-arrow {\r\n\t\t\t\t\t\tmargin: -7px 0 0 0;\r\n\t\t\t\t\t\ttop: 50%;\r\n\t\t\t\t\t\tcolor: inherit;\r\n\t\t\t\t\t\tvertical-align: middle;\r\n\t\t\t\t\t\tfont-size: 12px;\r\n\t\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\t\tright: 20px;\r\n\t\t\t\t\t}\r\n\t\t\r\n\t\t.el-menu-vertical-demo .el-submenu {\r\n\t\t\t\t\t\tpadding: 0;\r\n\t\t\t\t\t\tmargin: 0;\r\n\t\t\t\t\t\tlist-style: none;\r\n\t\t\t\t\t}\r\n\t\t\r\n\t\t// .el-menu-vertical-demo .el-submenu :deep .el-menu {\r\n\t\t// \t\t\t\t// \t\tborder: none;\r\n\t\t// \t\t\t\t// \t\tdisplay: none;\r\n\t\t// \t\t\t\t// }\r\n\t\t\r\n\t\t.el-menu-vertical-demo .el-submenu :deep .el-menu .el-menu-item {\r\n\t\t\t\t\t\tpadding: 0 40px;\r\n\t\t\t\t\t\tcolor: #666;\r\n\t\t\t\t\t\tbackground: #fff;\r\n\t\t\t\t\t\tline-height: 50px;\r\n\t\t\t\t\t\theight: 50px;\r\n\t\t\t\t\t}\r\n\t\t\r\n\t\t.el-menu-vertical-demo .el-submenu :deep .el-menu .el-menu-item:hover {\r\n\t\t\t\t\t\tpadding: 0 40px;\r\n\t\t\t\t\t\tcolor: #fff;\r\n\t\t\t\t\t\tbackground: red;\r\n\t\t\t\t\t\tline-height: 50px;\r\n\t\t\t\t\t\theight: 50px;\r\n\t\t\t\t\t}\r\n\t\t\r\n\t\t.el-menu-vertical-demo .el-submenu :deep .el-menu .el-menu-item.is-active {\r\n\t\t\t\t\t\tpadding: 0 40px;\r\n\t\t\t\t\t\tcolor: #fff;\r\n\t\t\t\t\t\tbackground: blue;\r\n\t\t\t\t\t\tline-height: 50px;\r\n\t\t\t\t\t\theight: 50px;\r\n\t\t\t\t\t}\r\n\t\t// 竖向\r\n\t\t\t  }\r\n\t  \t}\r\n\t// 竖向 样式�?open\r\n\t.scrollbar-wrapper-open .el-menu-vertical-2>.el-menu-item.other {\r\n\t\tfont-size: inherit;\r\n\t\tbackground: none;\r\n\t}\r\n\t.scrollbar-wrapper-open .el-menu-vertical-2>.el-menu-item.home {\r\n\t\tfont-size: inherit;\r\n\t\tbackground: none;\r\n\t}\r\n\t.scrollbar-wrapper-open .el-menu-vertical-2>.el-menu-item.other>.el-tooltip {\r\n\t\t\t\tcursor: pointer;\r\n\t\t\t\tpadding: 0 20px;\r\n\t\t\t\tcolor: #fff;\r\n\t\t\t\twhite-space: nowrap;\r\n\t\t\t\tbackground: #0d102c;\r\n\t\t\t\tborder-color: #20233d;\r\n\t\t\t\tborder-width: 1px 0;\r\n\t\t\t\tposition: relative;\r\n\t\t\t\tborder-style: solid;\r\n\t\t\t}\r\n\t\r\n\t.scrollbar-wrapper-open .el-menu-vertical-2>.el-menu-item.other>.el-tooltip:hover {\r\n\t\t\t\tcolor: #2ddcd3 !important;\r\n\t\t\t}\r\n\t\r\n\t\r\n\t.scrollbar-wrapper-open .el-menu-vertical-2 .el-submenu.other :deep .el-submenu__title {\r\n\t\t\t\tcursor: pointer !important;\r\n\t\t\t\tpadding: 0 20px !important;\r\n\t\t\t\tcolor: #fff !important;\r\n\t\t\t\twhite-space: nowrap !important;\r\n\t\t\t\tbackground: #0d102c !important;\r\n\t\t\t\tborder-color: #20233d !important;\r\n\t\t\t\tborder-width: 1px 0 !important;\r\n\t\t\t\tposition: relative !important;\r\n\t\t\t\tborder-style: solid !important;\r\n\t\t\t}\r\n\t.scrollbar-wrapper-open .el-menu-vertical-2>.el-menu-item.other.is-active>.el-tooltip {\r\n\t\t\t\tcolor: #2ddcd3 !important;\r\n\t\t\t}\r\n\t\r\n\t.scrollbar-wrapper-open .el-menu-vertical-2 .el-submenu.other :deep .el-submenu__title:hover {\r\n\t\t\t\tcolor: #2ddcd3 !important;\r\n\t\t\t}\r\n\t.scrollbar-wrapper-open .el-menu-vertical-2 .el-submenu.other.is-active :deep .el-submenu__title {\r\n\t\t\t\tcolor: #2ddcd3 !important;\r\n\t\t\t}\r\n\t\r\n\t.scrollbar-wrapper-open .el-menu-vertical-2 .el-submenu.other :deep .el-submenu__title .iconfont {\r\n\t\t\t\tborder-radius: 100%;\r\n\t\t\t\tmargin: 0 3px;\r\n\t\t\t\tcolor: inherit;\r\n\t\t\t\tbackground: linear-gradient(120deg, #0f75ff 60%, #2ddcd3  100%);\r\n\t\t\t\tdisplay: inline-block;\r\n\t\t\t\tvertical-align: middle;\r\n\t\t\t\twidth: 34px;\r\n\t\t\t\tfont-size: 18px;\r\n\t\t\t\tline-height: 30px;\r\n\t\t\t\ttext-align: center;\r\n\t\t\t\theight: 30px;\r\n\t\t\t}\r\n\t\r\n\t.scrollbar-wrapper-open .el-menu-vertical-2 .el-submenu.other :deep .el-submenu__title .el-submenu__icon-arrow {\r\n\t\t\t\tmargin: -7px 0 0 0;\r\n\t\t\t\ttop: 50%;\r\n\t\t\t\tcolor: inherit;\r\n\t\t\t\tvertical-align: middle;\r\n\t\t\t\tfont-size: 12px;\r\n\t\t\t\tposition: absolute;\r\n\t\t\t\tright: 20px;\r\n\t\t\t}\r\n\t\r\n\t.scrollbar-wrapper-open .el-menu-vertical-2 :deep .el-submenu.other .el-menu {\r\n\t\t\t\tborder: none;\r\n\t\t\t}\r\n\t\r\n\t.scrollbar-wrapper-open .el-menu-vertical-2 .el-submenu.other .el-menu .el-menu-item {\r\n\t\t\t\tpadding: 0 40px !important;\r\n\t\t\t\tcolor: #fff !important;\r\n\t\t\t\tbackground: #0d102c !important;\r\n\t\t\t\tline-height: 50px !important;\r\n\t\t\t\theight: 50px !important;\r\n\t\t\t}\r\n\t\r\n\t.scrollbar-wrapper-open .el-menu-vertical-2 .el-submenu.other .el-menu .el-menu-item:hover {\r\n\t\t\t\tpadding: 0 40px !important;\r\n\t\t\t\tcolor: #2ddcd3 !important;\r\n\t\t\t\tline-height: 50px !important;\r\n\t\t\t\theight: 50px !important;\r\n\t\t\t}\r\n\t\r\n\t.scrollbar-wrapper-open .el-menu-vertical-2 .el-submenu.other .el-menu .el-menu-item.is-active {\r\n\t\t\t\tpadding: 0 40px !important;\r\n\t\t\t\tcolor: #2ddcd3 !important;\r\n\t\t\t\tline-height: 50px !important;\r\n\t\t\t\theight: 50px !important;\r\n\t\t\t}\r\n\r\n\t// 竖向 样式�?close\r\n\t.scrollbar-wrapper-close .el-menu-vertical-2>.el-menu-item.other>.el-tooltip {\r\n\t\t\t\tcursor: pointer;\r\n\t\t\t\tpadding: 0 20px;\r\n\t\t\t\tcolor: #333;\r\n\t\t\t\twhite-space: nowrap;\r\n\t\t\t\tbackground: #fff;\r\n\t\t\t\tposition: relative;\r\n\t\t\t}\r\n\t\r\n\t.scrollbar-wrapper-close .el-menu-vertical-2>.el-menu-item.other>.el-tooltip:hover {\r\n\t\t\t\tcolor: #fff;\r\n\t\t\t\tbackground: blue;\r\n\t\t\t}\r\n\t\r\n\t.scrollbar-wrapper-close .el-menu-vertical-2>.el-menu-item.other.is-active>.el-tooltip {\r\n\t\t\t\tcolor: #fff;\r\n\t\t\t\tbackground: blue;\r\n\t\t\t}\r\n\t\r\n\t.scrollbar-wrapper-close .el-menu-vertical-2 .el-submenu.other :deep .el-submenu__title {\r\n\t\t\t\tcursor: pointer !important;\r\n\t\t\t\tpadding: 0 20px !important;\r\n\t\t\t\tcolor: #333 !important;\r\n\t\t\t\twhite-space: nowrap !important;\r\n\t\t\t\tbackground: #fff !important;\r\n\t\t\t\tposition: relative !important;\r\n\t\t\t}\r\n\t\r\n\t.scrollbar-wrapper-close .el-menu-vertical-2 .el-submenu.other :deep .el-submenu__title:hover {\r\n\t\t\t\tcolor: #fff !important;\r\n\t\t\t\tbackground: blue !important;\r\n\t\t\t}\r\n\t\r\n\t.scrollbar-wrapper-close .el-menu-vertical-2 .el-submenu.other :deep .el-submenu__title .iconfont {\r\n\t\t\t\tmargin: 0;\r\n\t\t\t\tcolor: inherit;\r\n\t\t\t\tdisplay: inline-block;\r\n\t\t\t\tvertical-align: middle;\r\n\t\t\t\twidth: 24px;\r\n\t\t\t\tfont-size: 18px;\r\n\t\t\t\ttext-align: center;\r\n\t\t\t}\r\n\t\r\n\t.scrollbar-wrapper-close .el-menu-vertical-2 .el-submenu.other :deep .el-submenu__title .el-submenu__icon-arrow {\r\n\t\t\t\tmargin: -7px 0 0 0;\r\n\t\t\t\ttop: 50%;\r\n\t\t\t\tcolor: inherit;\r\n\t\t\t\tdisplay: none;\r\n\t\t\t\tvertical-align: middle;\r\n\t\t\t\tfont-size: 12px;\r\n\t\t\t\tposition: absolute;\r\n\t\t\t\tright: 20px;\r\n\t\t\t}\r\n\t\r\n\t.scrollbar-wrapper-close .el-menu-vertical-2 .el-submenu.other .el-menu {\r\n\t\t\t\tborder: none;\r\n\t\t\t}\r\n\t\r\n\t.scrollbar-wrapper-close .el-menu-vertical-2 .el-submenu.other .el-menu--vertical.other .el-menu-item {\r\n\t\t\t\tbackground-color: #fff;\r\n\t\t\t\tpadding: 0 20px;\r\n\t\t\t\tcolor: #666;\r\n\t\t\t\tline-height: 56px;\r\n\t\t\t\theight: 56px;\r\n\t\t\t}\r\n\t\r\n\t.scrollbar-wrapper-close .el-menu-vertical-2 .el-submenu.other .el-menu--vertical.other .el-menu-item:hover {\r\n\t\t\t\tbackground-color: red !important;\r\n\t\t\t\tpadding: 0 20px;\r\n\t\t\t\tcolor: #fff;\r\n\t\t\t\tline-height: 56px;\r\n\t\t\t\theight: 56px;\r\n\t\t\t}\r\n\t\r\n\t.scrollbar-wrapper-close .el-menu-vertical-2 .el-submenu.other .el-menu--vertical.other .el-menu-item.is-active {\r\n\t\t\t\tbackground-color: blue !important;\r\n\t\t\t\tpadding: 0 20px;\r\n\t\t\t\tcolor: #fff;\r\n\t\t\t\tline-height: 56px;\r\n\t\t\t\theight: 56px;\r\n\t\t\t}\r\n\t\r\n\t// 竖向 样式�?open-首页\r\n\t.scrollbar-wrapper-open .el-menu-vertical-2>.el-menu-item.home>.el-tooltip {\r\n\t\t\t\tcursor: pointer;\r\n\t\t\t\tpadding: 0 20px;\r\n\t\t\t\tcolor: #fff;\r\n\t\t\t\twhite-space: nowrap;\r\n\t\t\t\tbackground: #0d102c;\r\n\t\t\t\tborder-color: #20233d;\r\n\t\t\t\tborder-width: 1px 0;\r\n\t\t\t\tposition: relative;\r\n\t\t\t\tborder-style: solid;\r\n\t\t\t}\r\n\t\r\n\t.scrollbar-wrapper-open .el-menu-vertical-2>.el-menu-item.home>.el-tooltip:hover {\r\n\t\t\t\tcolor: #2ddcd3;\r\n\t\t\t}\r\n\t\r\n\t.scrollbar-wrapper-open .el-menu-vertical-2>.el-menu-item.home.is-active>.el-tooltip {\r\n\t\t\t\tcolor: #2ddcd3;\r\n\t\t\t}\r\n\t\r\n\t.scrollbar-wrapper-open .el-menu-vertical-2 .el-submenu.home :deep .el-submenu__title {\r\n\t\t\t\tcursor: pointer !important;\r\n\t\t\t\tpadding: 0 20px !important;\r\n\t\t\t\tcolor: #fff !important;\r\n\t\t\t\twhite-space: nowrap !important;\r\n\t\t\t\tbackground: #0d102c !important;\r\n\t\t\t\tborder-color: #20233d !important;\r\n\t\t\t\tborder-width: 1px 0 !important;\r\n\t\t\t\tposition: relative !important;\r\n\t\t\t\tborder-style: solid !important;\r\n\t\t\t}\r\n\t\r\n\t.scrollbar-wrapper-open .el-menu-vertical-2 .el-submenu.home :deep .el-submenu__title:hover {\r\n\t\t\t\tcolor: #2ddcd3 !important;\r\n\t\t\t}\r\n\t\r\n\t.scrollbar-wrapper-open .el-menu-vertical-2 .el-submenu.home :deep .el-submenu__title .iconfont {\r\n\t\t\t\tborder-radius: 100%;\r\n\t\t\t\tmargin: 0 3px;\r\n\t\t\t\tcolor: inherit;\r\n\t\t\t\tbackground: linear-gradient(120deg, #0f75ff 60%, #2ddcd3  100%);\r\n\t\t\t\tdisplay: inline-block;\r\n\t\t\t\tvertical-align: middle;\r\n\t\t\t\twidth: 34px;\r\n\t\t\t\tfont-size: 18px;\r\n\t\t\t\tline-height: 30px;\r\n\t\t\t\ttext-align: center;\r\n\t\t\t\theight: 30px;\r\n\t\t\t}\r\n\t\r\n\t.scrollbar-wrapper-open .el-menu-vertical-2 .el-submenu.home :deep .el-submenu__title .el-submenu__icon-arrow {\r\n\t\t\t\tmargin: -7px 0 0 0;\r\n\t\t\t\ttop: 50%;\r\n\t\t\t\tcolor: inherit;\r\n\t\t\t\tvertical-align: middle;\r\n\t\t\t\tfont-size: 12px;\r\n\t\t\t\tposition: absolute;\r\n\t\t\t\tright: 20px;\r\n\t\t\t}\r\n\t\r\n\t.scrollbar-wrapper-open .el-menu-vertical-2 .el-submenu.home .el-menu {\r\n\t\t\t\tborder: none;\r\n\t\t\t}\r\n\t\r\n\t.scrollbar-wrapper-open .el-menu-vertical-2 .el-submenu.home .el-menu .el-menu-item {\r\n\t\t\t\tpadding: 0 40px;\r\n\t\t\t\tcolor: #fff;\r\n\t\t\t\tbackground: #0d102c;\r\n\t\t\t\tline-height: 50px;\r\n\t\t\t\theight: 50px;\r\n\t\t\t}\r\n\t\r\n\t.scrollbar-wrapper-open .el-menu-vertical-2 .el-submenu.home .el-menu .el-menu-item:hover {\r\n\t\t\t\tpadding: 0 40px;\r\n\t\t\t\tcolor: #2ddcd3;\r\n\t\t\t\tline-height: 50px;\r\n\t\t\t\theight: 50px;\r\n\t\t\t}\r\n\t\r\n\t.scrollbar-wrapper-open .el-menu-vertical-2 .el-submenu.home .el-menu .el-menu-item.is-active {\r\n\t\t\t\tpadding: 0 40px;\r\n\t\t\t\tcolor: #2ddcd3;\r\n\t\t\t\tline-height: 50px;\r\n\t\t\t\theight: 50px;\r\n\t\t\t}\r\n\t\r\n\t// 竖向 样式�?close-首页\r\n\t.scrollbar-wrapper-close .el-menu-vertical-2>.el-menu-item.home>.el-tooltip {\r\n\t\t\t\tcursor: pointer;\r\n\t\t\t\tpadding: 0 20px;\r\n\t\t\t\tcolor: #333;\r\n\t\t\t\twhite-space: nowrap;\r\n\t\t\t\tbackground: #fff;\r\n\t\t\t\tposition: relative;\r\n\t\t\t}\r\n\t\r\n\t.scrollbar-wrapper-close .el-menu-vertical-2>.el-menu-item.home>.el-tooltip:hover {\r\n\t\t\t\tcolor: #fff;\r\n\t\t\t\tbackground: blue;\r\n\t\t\t}\r\n\t\r\n\t.scrollbar-wrapper-close .el-menu-vertical-2>.el-menu-item.home.is-active>.el-tooltip {\r\n\t\t\t\tcolor: #fff;\r\n\t\t\t\tbackground: blue;\r\n\t\t\t}\r\n\t\r\n\t.scrollbar-wrapper-close .el-menu-vertical-2 .el-submenu.home :deep .el-submenu__title {\r\n\t\t\t\tcursor: pointer;\r\n\t\t\t\tpadding: 0 20px;\r\n\t\t\t\tcolor: #333;\r\n\t\t\t\twhite-space: nowrap;\r\n\t\t\t\tbackground: #fff;\r\n\t\t\t\tposition: relative;\r\n\t\t\t}\r\n\t\r\n\t.scrollbar-wrapper-close .el-menu-vertical-2 .el-submenu.home :deep .el-submenu__title:hover {\r\n\t\t\t\tcolor: #fff;\r\n\t\t\t\tbackground: blue;\r\n\t\t\t}\r\n\t\r\n\t.scrollbar-wrapper-close .el-menu-vertical-2 .el-submenu.home :deep .el-submenu__title .iconfont {\r\n\t\t\t\tmargin: 0;\r\n\t\t\t\tcolor: inherit;\r\n\t\t\t\tdisplay: inline-block;\r\n\t\t\t\tvertical-align: middle;\r\n\t\t\t\twidth: 24px;\r\n\t\t\t\tfont-size: 18px;\r\n\t\t\t\ttext-align: center;\r\n\t\t\t}\r\n\t\r\n\t.scrollbar-wrapper-close .el-menu-vertical-2 .el-submenu.home :deep .el-submenu__title .el-submenu__icon-arrow {\r\n\t\t\t\tmargin: -7px 0 0 0;\r\n\t\t\t\ttop: 50%;\r\n\t\t\t\tcolor: inherit;\r\n\t\t\t\tdisplay: none;\r\n\t\t\t\tvertical-align: middle;\r\n\t\t\t\tfont-size: 12px;\r\n\t\t\t\tposition: absolute;\r\n\t\t\t\tright: 20px;\r\n\t\t\t}\r\n\t\r\n\t.scrollbar-wrapper-close .el-menu-vertical-2 .el-submenu.home .el-menu {\r\n\t\t\t\tborder: none;\r\n\t\t\t}\r\n\t\r\n\t.scrollbar-wrapper-close .el-menu-vertical-2 .el-submenu.home .el-menu--vertical.home .el-menu-item {\r\n\t\t\t\tbackground-color: #fff;\r\n\t\t\t\tpadding: 0 20px;\r\n\t\t\t\tcolor: #666;\r\n\t\t\t\tline-height: 56px;\r\n\t\t\t\theight: 56px;\r\n\t\t\t}\r\n\t\r\n\t.scrollbar-wrapper-close .el-menu-vertical-2 .el-submenu.home .el-menu--vertical.home .el-menu-item:hover {\r\n\t\t\t\tbackground-color: red !important;\r\n\t\t\t\tpadding: 0 20px;\r\n\t\t\t\tcolor: #fff;\r\n\t\t\t\tline-height: 56px;\r\n\t\t\t\theight: 56px;\r\n\t\t\t}\r\n\t\r\n\t.scrollbar-wrapper-close .el-menu-vertical-2 .el-submenu.home .el-menu--vertical.home .el-menu-item.is-active {\r\n\t\t\t\tbackground-color: blue !important;\r\n\t\t\t\tpadding: 0 20px;\r\n\t\t\t\tcolor: #fff;\r\n\t\t\t\tline-height: 56px;\r\n\t\t\t\theight: 56px;\r\n\t\t\t}\r\n\t\r\n\t// 竖向 样式�?open-个人中心\r\n\t.scrollbar-wrapper-open .el-menu-vertical-2>.el-menu-item.user>.el-tooltip {\r\n\t\t\t\tcursor: pointer;\r\n\t\t\t\tpadding: 0 20px;\r\n\t\t\t\tcolor: #fff;\r\n\t\t\t\twhite-space: nowrap;\r\n\t\t\t\tbackground: #0d102c;\r\n\t\t\t\tborder-color: #20233d;\r\n\t\t\t\tborder-width: 1px 0;\r\n\t\t\t\tposition: relative;\r\n\t\t\t\tborder-style: solid;\r\n\t\t\t}\r\n\t\r\n\t.scrollbar-wrapper-open .el-menu-vertical-2>.el-menu-item.user>.el-tooltip:hover {\r\n\t\t\t\tcolor: #2ddcd3;\r\n\t\t\t}\r\n\t\r\n\t.scrollbar-wrapper-open .el-menu-vertical-2>.el-menu-item.user.is-active>.el-tooltip {\r\n\t\t\t\tcolor: #2ddcd3;\r\n\t\t\t}\r\n\t\r\n\t.scrollbar-wrapper-open .el-menu-vertical-2 .el-submenu.user :deep .el-submenu__title {\r\n\t\t\t\tcursor: pointer !important;\r\n\t\t\t\tpadding: 0 20px !important;\r\n\t\t\t\tcolor: #fff !important;\r\n\t\t\t\twhite-space: nowrap !important;\r\n\t\t\t\tbackground: #0d102c !important;\r\n\t\t\t\tborder-color: #20233d !important;\r\n\t\t\t\tborder-width: 1px 0 !important;\r\n\t\t\t\tposition: relative !important;\r\n\t\t\t\tborder-style: solid !important;\r\n\t\t\t}\r\n\t\r\n\t.scrollbar-wrapper-open .el-menu-vertical-2 .el-submenu.user :deep .el-submenu__title:hover {\r\n\t\t\t\tcolor: #2ddcd3 !important;\r\n\t\t\t}\r\n\t\r\n\t.scrollbar-wrapper-open .el-menu-vertical-2 .el-submenu.user :deep .el-submenu__title .iconfont {\r\n\t\t\t\tborder-radius: 100%;\r\n\t\t\t\tmargin: 0 3px;\r\n\t\t\t\tcolor: inherit;\r\n\t\t\t\tbackground: linear-gradient(120deg, #0f75ff 60%, #2ddcd3  100%);\r\n\t\t\t\tdisplay: inline-block;\r\n\t\t\t\tvertical-align: middle;\r\n\t\t\t\twidth: 34px;\r\n\t\t\t\tfont-size: 18px;\r\n\t\t\t\tline-height: 30px;\r\n\t\t\t\ttext-align: center;\r\n\t\t\t\theight: 30px;\r\n\t\t\t}\r\n\t\r\n\t.scrollbar-wrapper-open .el-menu-vertical-2 .el-submenu.user :deep .el-submenu__title .el-submenu__icon-arrow {\r\n\t\t\t\tmargin: -7px 0 0 0;\r\n\t\t\t\ttop: 50%;\r\n\t\t\t\tcolor: inherit;\r\n\t\t\t\tvertical-align: middle;\r\n\t\t\t\tfont-size: 12px;\r\n\t\t\t\tposition: absolute;\r\n\t\t\t\tright: 20px;\r\n\t\t\t}\r\n\t\r\n\t.scrollbar-wrapper-open .el-menu-vertical-2 :deep .el-submenu.user .el-menu {\r\n\t\t\t\tborder: none;\r\n\t\t\t}\r\n\t\r\n\t.scrollbar-wrapper-open .el-menu-vertical-2 .el-submenu.user .el-menu .el-menu-item {\r\n\t\t\t\tpadding: 0 40px !important;\r\n\t\t\t\tcolor: #fff !important;\r\n\t\t\t\tbackground: #0d102c !important;\r\n\t\t\t\tline-height: 50px !important;\r\n\t\t\t\theight: 50px !important;\r\n\t\t\t}\r\n\t\r\n\t.scrollbar-wrapper-open .el-menu-vertical-2 .el-submenu.user .el-menu .el-menu-item:hover {\r\n\t\t\t\tpadding: 0 40px !important;\r\n\t\t\t\tcolor: #2ddcd3 !important;\r\n\t\t\t\tline-height: 50px !important;\r\n\t\t\t\theight: 50px !important;\r\n\t\t\t}\r\n\t\r\n\t.scrollbar-wrapper-open .el-menu-vertical-2 .el-submenu.user .el-menu .el-menu-item.is-active {\r\n\t\t\t\tpadding: 0 40px !important;\r\n\t\t\t\tcolor: #2ddcd3 !important;\r\n\t\t\t\tline-height: 50px !important;\r\n\t\t\t\theight: 50px !important;\r\n\t\t\t}\r\n\t\r\n\t// 竖向 样式�?close-个人中心\r\n\t.scrollbar-wrapper-close .el-menu-vertical-2>.el-menu-item.user>.el-tooltip {\r\n\t\t\t\tcursor: pointer;\r\n\t\t\t\tpadding: 0 20px;\r\n\t\t\t\tcolor: #333;\r\n\t\t\t\twhite-space: nowrap;\r\n\t\t\t\tbackground: #fff;\r\n\t\t\t\tposition: relative;\r\n\t\t\t}\r\n\t\r\n\t.scrollbar-wrapper-close .el-menu-vertical-2>.el-menu-item.user>.el-tooltip:hover {\r\n\t\t\t\tcolor: #fff;\r\n\t\t\t\tbackground: blue;\r\n\t\t\t}\r\n\t\r\n\t.scrollbar-wrapper-close .el-menu-vertical-2>.el-menu-item.user.is-active>.el-tooltip {\r\n\t\t\t\tcolor: #fff;\r\n\t\t\t\tbackground: blue;\r\n\t\t\t}\r\n\t\r\n\t.scrollbar-wrapper-close .el-menu-vertical-2 .el-submenu.user :deep .el-submenu__title {\r\n\t\t\t\tcursor: pointer !important;\r\n\t\t\t\tpadding: 0 20px !important;\r\n\t\t\t\tcolor: #333 !important;\r\n\t\t\t\twhite-space: nowrap !important;\r\n\t\t\t\tbackground: #fff !important;\r\n\t\t\t\tposition: relative !important;\r\n\t\t\t}\r\n\t\r\n\t.scrollbar-wrapper-close .el-menu-vertical-2 .el-submenu.user :deep .el-submenu__title:hover {\r\n\t\t\t\tcolor: #fff !important;\r\n\t\t\t\tbackground: blue !important;\r\n\t\t\t}\r\n\t\r\n\t.scrollbar-wrapper-close .el-menu-vertical-2 .el-submenu.user :deep .el-submenu__title .iconfont {\r\n\t\t\t\tmargin: 0;\r\n\t\t\t\tcolor: inherit;\r\n\t\t\t\tdisplay: inline-block;\r\n\t\t\t\tvertical-align: middle;\r\n\t\t\t\twidth: 24px;\r\n\t\t\t\tfont-size: 18px;\r\n\t\t\t\ttext-align: center;\r\n\t\t\t}\r\n\t\r\n\t.scrollbar-wrapper-close .el-menu-vertical-2 .el-submenu.user :deep .el-submenu__title .el-submenu__icon-arrow {\r\n\t\t\t\tmargin: -7px 0 0 0;\r\n\t\t\t\ttop: 50%;\r\n\t\t\t\tcolor: inherit;\r\n\t\t\t\tdisplay: none;\r\n\t\t\t\tvertical-align: middle;\r\n\t\t\t\tfont-size: 12px;\r\n\t\t\t\tposition: absolute;\r\n\t\t\t\tright: 20px;\r\n\t\t\t}\r\n\t\r\n\t.scrollbar-wrapper-close .el-menu-vertical-2 .el-submenu.user .el-menu {\r\n\t\t\t\tborder: none;\r\n\t\t\t}\r\n\t\r\n\t.scrollbar-wrapper-close .el-menu-vertical-2 .el-submenu.user .el-menu--vertical.user .el-menu-item {\r\n\t\t\t\tbackground-color: #fff;\r\n\t\t\t\tpadding: 0 20px;\r\n\t\t\t\tcolor: #666;\r\n\t\t\t\tline-height: 56px;\r\n\t\t\t\theight: 56px;\r\n\t\t\t}\r\n\t\r\n\t.scrollbar-wrapper-close .el-menu-vertical-2 .el-submenu.user .el-menu--vertical.user .el-menu-item:hover {\r\n\t\t\t\tbackground-color: red !important;\r\n\t\t\t\tpadding: 0 20px;\r\n\t\t\t\tcolor: #fff;\r\n\t\t\t\tline-height: 56px;\r\n\t\t\t\theight: 56px;\r\n\t\t\t}\r\n\t\r\n\t.scrollbar-wrapper-close .el-menu-vertical-2 .el-submenu.user .el-menu--vertical.user .el-menu-item.is-active {\r\n\t\t\t\tbackground-color: blue !important;\r\n\t\t\t\tpadding: 0 20px;\r\n\t\t\t\tcolor: #fff;\r\n\t\t\t\tline-height: 56px;\r\n\t\t\t\theight: 56px;\r\n\t\t\t}\r\n</style>\r\n"]}]}