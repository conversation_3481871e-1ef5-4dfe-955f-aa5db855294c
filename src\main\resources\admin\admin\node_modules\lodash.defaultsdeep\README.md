# lodash.defaultsdeep v4.6.1

The [Lodash](https://lodash.com/) method `_.defaultsDeep` exported as a [Node.js](https://nodejs.org/) module.

## Installation

Using npm:
```bash
$ {sudo -H} npm i -g npm
$ npm i --save lodash.defaultsdeep
```

In Node.js:
```js
var defaultsDeep = require('lodash.defaultsdeep');
```

See the [documentation](https://lodash.com/docs#defaultsDeep) or [package source](https://github.com/lodash/lodash/blob/4.6.1-npm-packages/lodash.defaultsdeep) for more details.
