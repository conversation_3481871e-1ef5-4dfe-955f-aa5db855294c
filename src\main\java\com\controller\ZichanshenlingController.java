package com.controller;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Map;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Date;
import java.util.List;
import javax.servlet.http.HttpServletRequest;

import com.utils.ValidatorUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.mapper.Wrapper;
import com.annotation.IgnoreAuth;

import com.entity.ZichanshenlingEntity;
import com.entity.view.ZichanshenlingView;

import com.service.ZichanshenlingService;
import com.service.TokenService;
import com.utils.PageUtils;
import com.utils.R;
import com.utils.MPUtil;
import com.utils.MapUtils;
import com.utils.CommonUtil;
import java.io.IOException;

/**
 * 资产申领
 * 后端接口
 * <AUTHOR> @email 
 * @date 2024-02-19 14:47:30
 */
@RestController
@RequestMapping("/zichanshenling")
public class ZichanshenlingController {
    @Autowired
    private ZichanshenlingService zichanshenlingService;




    



    /**
     * 后端列表
     */
    @RequestMapping("/page")
    public R page(@RequestParam Map<String, Object> params,ZichanshenlingEntity zichanshenling,
		HttpServletRequest request){
		String tableName = request.getSession().getAttribute("tableName").toString();
		if(tableName.equals("yuangong")) {
			zichanshenling.setGonghao((String)request.getSession().getAttribute("username"));
		}
        EntityWrapper<ZichanshenlingEntity> ew = new EntityWrapper<ZichanshenlingEntity>();

		PageUtils page = zichanshenlingService.queryPage(params, MPUtil.sort(MPUtil.between(MPUtil.likeOrEq(ew, zichanshenling), params), params));

        return R.ok().put("data", page);
    }
    
    /**
     * 前端列表
     */
	@IgnoreAuth
    @RequestMapping("/list")
    public R list(@RequestParam Map<String, Object> params,ZichanshenlingEntity zichanshenling, 
		HttpServletRequest request){
        EntityWrapper<ZichanshenlingEntity> ew = new EntityWrapper<ZichanshenlingEntity>();

		PageUtils page = zichanshenlingService.queryPage(params, MPUtil.sort(MPUtil.between(MPUtil.likeOrEq(ew, zichanshenling), params), params));
        return R.ok().put("data", page);
    }



	/**
     * 列表
     */
    @RequestMapping("/lists")
    public R list( ZichanshenlingEntity zichanshenling){
       	EntityWrapper<ZichanshenlingEntity> ew = new EntityWrapper<ZichanshenlingEntity>();
      	ew.allEq(MPUtil.allEQMapPre( zichanshenling, "zichanshenling")); 
        return R.ok().put("data", zichanshenlingService.selectListView(ew));
    }

	 /**
     * 查询
     */
    @RequestMapping("/query")
    public R query(ZichanshenlingEntity zichanshenling){
        EntityWrapper< ZichanshenlingEntity> ew = new EntityWrapper< ZichanshenlingEntity>();
 		ew.allEq(MPUtil.allEQMapPre( zichanshenling, "zichanshenling")); 
		ZichanshenlingView zichanshenlingView =  zichanshenlingService.selectView(ew);
		return R.ok("查询资产申领成功").put("data", zichanshenlingView);
    }
	
    /**
     * 后端详情
     */
    @RequestMapping("/info/{id}")
    public R info(@PathVariable("id") Long id){
        ZichanshenlingEntity zichanshenling = zichanshenlingService.selectById(id);
        return R.ok().put("data", zichanshenling);
    }

    /**
     * 前端详情
     */
	@IgnoreAuth
    @RequestMapping("/detail/{id}")
    public R detail(@PathVariable("id") Long id){
        ZichanshenlingEntity zichanshenling = zichanshenlingService.selectById(id);
        return R.ok().put("data", zichanshenling);
    }
    



    /**
     * 后端保存
     */
    @RequestMapping("/save")
    public R save(@RequestBody ZichanshenlingEntity zichanshenling, HttpServletRequest request){
    	//ValidatorUtils.validateEntity(zichanshenling);
        zichanshenlingService.insert(zichanshenling);
        return R.ok();
    }
    
    /**
     * 前端保存
     */
    @RequestMapping("/add")
    public R add(@RequestBody ZichanshenlingEntity zichanshenling, HttpServletRequest request){
    	//ValidatorUtils.validateEntity(zichanshenling);
        zichanshenlingService.insert(zichanshenling);
        return R.ok();
    }





    /**
     * 修改
     */
    @RequestMapping("/update")
    @Transactional
    public R update(@RequestBody ZichanshenlingEntity zichanshenling, HttpServletRequest request){
        //ValidatorUtils.validateEntity(zichanshenling);
        zichanshenlingService.updateById(zichanshenling);//全部更新
        return R.ok();
    }

    /**
     * 审核
     */
    @RequestMapping("/shBatch")
    @Transactional
    public R update(@RequestBody Long[] ids, @RequestParam String sfsh, @RequestParam String shhf){
        List<ZichanshenlingEntity> list = new ArrayList<ZichanshenlingEntity>();
        for(Long id : ids) {
            ZichanshenlingEntity zichanshenling = zichanshenlingService.selectById(id);
            zichanshenling.setSfsh(sfsh);
            zichanshenling.setShhf(shhf);
            list.add(zichanshenling);
        }
        zichanshenlingService.updateBatchById(list);
        return R.ok();
    }


    

    /**
     * 删除
     */
    @RequestMapping("/delete")
    public R delete(@RequestBody Long[] ids){
        zichanshenlingService.deleteBatchIds(Arrays.asList(ids));
        return R.ok();
    }
    
	










}
