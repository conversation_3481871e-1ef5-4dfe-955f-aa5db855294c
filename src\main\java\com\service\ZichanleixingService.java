package com.service;

import com.baomidou.mybatisplus.mapper.Wrapper;
import com.baomidou.mybatisplus.service.IService;
import com.utils.PageUtils;
import com.entity.ZichanleixingEntity;
import java.util.List;
import java.util.Map;
import com.entity.vo.ZichanleixingVO;
import org.apache.ibatis.annotations.Param;
import com.entity.view.ZichanleixingView;


/**
 * 资产类型
 *
 * <AUTHOR> @email 
 * @date 2024-02-19 14:47:30
 */
public interface ZichanleixingService extends IService<ZichanleixingEntity> {

    PageUtils queryPage(Map<String, Object> params);
    
   	List<ZichanleixingVO> selectListVO(Wrapper<ZichanleixingEntity> wrapper);
   	
   	ZichanleixingVO selectVO(@Param("ew") Wrapper<ZichanleixingEntity> wrapper);
   	
   	List<ZichanleixingView> selectListView(Wrapper<ZichanleixingEntity> wrapper);
   	
   	ZichanleixingView selectView(@Param("ew") Wrapper<ZichanleixingEntity> wrapper);
   	
   	PageUtils queryPage(Map<String, Object> params,Wrapper<ZichanleixingEntity> wrapper);

   	

}

