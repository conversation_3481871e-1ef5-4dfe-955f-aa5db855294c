module.exports.pipe = require('pump')
module.exports.each = require('stream-each')
module.exports.pipeline = require('pumpify')
module.exports.duplex = require('duplexify')
module.exports.through = require('through2')
module.exports.concat = require('concat-stream')
module.exports.finished = require('end-of-stream')
module.exports.from = require('from2')
module.exports.to = require('flush-write-stream')
module.exports.parallel = require('parallel-transform')
