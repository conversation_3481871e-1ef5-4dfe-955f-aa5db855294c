{"remainingRequest": "E:\\java系统\\公司财务系统\\src\\main\\resources\\admin\\admin\\node_modules\\babel-loader\\lib\\index.js!E:\\java系统\\公司财务系统\\src\\main\\resources\\admin\\admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??ref--6!E:\\java系统\\公司财务系统\\src\\main\\resources\\admin\\admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!E:\\java系统\\公司财务系统\\src\\main\\resources\\admin\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\java系统\\公司财务系统\\src\\main\\resources\\admin\\admin\\src\\components\\index\\TagsView\\index.vue?vue&type=template&id=2b3b3dee&scoped=true", "dependencies": [{"path": "E:\\java系统\\公司财务系统\\src\\main\\resources\\admin\\admin\\src\\components\\index\\TagsView\\index.vue", "mtime": 1754631104896}, {"path": "E:\\java系统\\公司财务系统\\src\\main\\resources\\admin\\admin\\babel.config.js", "mtime": 1711079474000}, {"path": "E:\\java系统\\公司财务系统\\src\\main\\resources\\admin\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754805254356}, {"path": "E:\\java系统\\公司财务系统\\src\\main\\resources\\admin\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754805254356}, {"path": "E:\\java系统\\公司财务系统\\src\\main\\resources\\admin\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754805257322}, {"path": "E:\\java系统\\公司财务系统\\src\\main\\resources\\admin\\admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1754628163793}, {"path": "E:\\java系统\\公司财务系统\\src\\main\\resources\\admin\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754805254356}, {"path": "E:\\java系统\\公司财务系统\\src\\main\\resources\\admin\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754628160828}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["render", "_vm", "_c", "_self", "staticClass", "style", "padding", "margin", "borderColor", "background", "borderWidth", "width", "borderStyle", "height", "attrs", "id", "ref", "whiteSpace", "position", "_l", "visitedViews", "tag", "key", "path", "refInFor", "class", "isActive", "to", "query", "fullPath", "nativeOn", "mouseup", "$event", "button", "closeSelectedTag", "contextmenu", "preventDefault", "openMenu", "_v", "_s", "name", "meta", "affix", "on", "click", "stopPropagation", "_e", "directives", "rawName", "value", "visible", "expression", "left", "top", "selectedTag", "closeAllTags", "staticRenderFns", "_withStripped"], "sources": ["E:/java系统/公司财务系统/src/main/resources/admin/admin/src/components/index/TagsView/index.vue"], "sourcesContent": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _c(\n    \"div\",\n    {\n      staticClass: \"tags-view-container\",\n      style: {\n        padding: \"4px 30px\",\n        margin: \"0px 0 0px\",\n        borderColor: \"#d8dce5\",\n        background: \"none\",\n        borderWidth: \"0 0 1px\",\n        width: \"100%\",\n        borderStyle: \"solid\",\n        height: \"34px\",\n      },\n      attrs: { id: \"tags-view-container\" },\n    },\n    [\n      _c(\n        \"scroll-pane\",\n        { ref: \"scrollPane\", staticClass: \"tags-view-wrapper\" },\n        [\n          _c(\n            \"div\",\n            {\n              staticClass: \"tags-view-box\",\n              style: {\n                width: \"100%\",\n                whiteSpace: \"nowrap\",\n                position: \"relative\",\n                background: \"none\",\n              },\n            },\n            _vm._l(_vm.visitedViews, function (tag) {\n              return _c(\n                \"router-link\",\n                {\n                  key: tag.path,\n                  ref: \"tag\",\n                  refInFor: true,\n                  staticClass: \"tags-view-item\",\n                  class: _vm.isActive(tag) ? \"active\" : \"\",\n                  attrs: {\n                    to: {\n                      path: tag.path,\n                      query: tag.query,\n                      fullPath: tag.fullPath,\n                    },\n                    tag: \"span\",\n                  },\n                  nativeOn: {\n                    mouseup: function ($event) {\n                      if (\"button\" in $event && $event.button !== 1) return null\n                      return _vm.closeSelectedTag(tag)\n                    },\n                    contextmenu: function ($event) {\n                      $event.preventDefault()\n                      return _vm.openMenu(tag, $event)\n                    },\n                  },\n                },\n                [\n                  _c(\"span\", { staticClass: \"text\" }, [\n                    _vm._v(_vm._s(tag.name)),\n                  ]),\n                  !tag.meta.affix\n                    ? _c(\"span\", {\n                        staticClass: \"el-icon-close\",\n                        on: {\n                          click: function ($event) {\n                            $event.preventDefault()\n                            $event.stopPropagation()\n                            return _vm.closeSelectedTag(tag)\n                          },\n                        },\n                      })\n                    : _vm._e(),\n                ]\n              )\n            }),\n            1\n          ),\n        ]\n      ),\n      _c(\n        \"ul\",\n        {\n          directives: [\n            {\n              name: \"show\",\n              rawName: \"v-show\",\n              value: _vm.visible,\n              expression: \"visible\",\n            },\n          ],\n          staticClass: \"contextmenu\",\n          style: { left: _vm.left + \"px\", top: _vm.top + \"px\" },\n        },\n        [\n          !(_vm.selectedTag.meta && _vm.selectedTag.meta.affix)\n            ? _c(\n                \"li\",\n                {\n                  on: {\n                    click: function ($event) {\n                      return _vm.closeSelectedTag(_vm.selectedTag)\n                    },\n                  },\n                },\n                [_vm._v(\"Close\")]\n              )\n            : _vm._e(),\n          _c(\n            \"li\",\n            {\n              on: {\n                click: function ($event) {\n                  return _vm.closeAllTags(_vm.selectedTag)\n                },\n              },\n            },\n            [_vm._v(\"Close All\")]\n          ),\n        ]\n      ),\n    ],\n    1\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"], "mappings": ";AAAA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;EAC7B,IAAIC,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CACP,KAAK,EACL;IACEE,WAAW,EAAE,qBAAqB;IAClCC,KAAK,EAAE;MACLC,OAAO,EAAE,UAAU;MACnBC,MAAM,EAAE,WAAW;MACnBC,WAAW,EAAE,SAAS;MACtBC,UAAU,EAAE,MAAM;MAClBC,WAAW,EAAE,SAAS;MACtBC,KAAK,EAAE,MAAM;MACbC,WAAW,EAAE,OAAO;MACpBC,MAAM,EAAE;IACV,CAAC;IACDC,KAAK,EAAE;MAAEC,EAAE,EAAE;IAAsB;EACrC,CAAC,EACD,CACEb,EAAE,CACA,aAAa,EACb;IAAEc,GAAG,EAAE,YAAY;IAAEZ,WAAW,EAAE;EAAoB,CAAC,EACvD,CACEF,EAAE,CACA,KAAK,EACL;IACEE,WAAW,EAAE,eAAe;IAC5BC,KAAK,EAAE;MACLM,KAAK,EAAE,MAAM;MACbM,UAAU,EAAE,QAAQ;MACpBC,QAAQ,EAAE,UAAU;MACpBT,UAAU,EAAE;IACd;EACF,CAAC,EACDR,GAAG,CAACkB,EAAE,CAAClB,GAAG,CAACmB,YAAY,EAAE,UAAUC,GAAG,EAAE;IACtC,OAAOnB,EAAE,CACP,aAAa,EACb;MACEoB,GAAG,EAAED,GAAG,CAACE,IAAI;MACbP,GAAG,EAAE,KAAK;MACVQ,QAAQ,EAAE,IAAI;MACdpB,WAAW,EAAE,gBAAgB;MAC7BqB,KAAK,EAAExB,GAAG,CAACyB,QAAQ,CAACL,GAAG,CAAC,GAAG,QAAQ,GAAG,EAAE;MACxCP,KAAK,EAAE;QACLa,EAAE,EAAE;UACFJ,IAAI,EAAEF,GAAG,CAACE,IAAI;UACdK,KAAK,EAAEP,GAAG,CAACO,KAAK;UAChBC,QAAQ,EAAER,GAAG,CAACQ;QAChB,CAAC;QACDR,GAAG,EAAE;MACP,CAAC;MACDS,QAAQ,EAAE;QACRC,OAAO,EAAE,SAATA,OAAOA,CAAYC,MAAM,EAAE;UACzB,IAAI,QAAQ,IAAIA,MAAM,IAAIA,MAAM,CAACC,MAAM,KAAK,CAAC,EAAE,OAAO,IAAI;UAC1D,OAAOhC,GAAG,CAACiC,gBAAgB,CAACb,GAAG,CAAC;QAClC,CAAC;QACDc,WAAW,EAAE,SAAbA,WAAWA,CAAYH,MAAM,EAAE;UAC7BA,MAAM,CAACI,cAAc,CAAC,CAAC;UACvB,OAAOnC,GAAG,CAACoC,QAAQ,CAAChB,GAAG,EAAEW,MAAM,CAAC;QAClC;MACF;IACF,CAAC,EACD,CACE9B,EAAE,CAAC,MAAM,EAAE;MAAEE,WAAW,EAAE;IAAO,CAAC,EAAE,CAClCH,GAAG,CAACqC,EAAE,CAACrC,GAAG,CAACsC,EAAE,CAAClB,GAAG,CAACmB,IAAI,CAAC,CAAC,CACzB,CAAC,EACF,CAACnB,GAAG,CAACoB,IAAI,CAACC,KAAK,GACXxC,EAAE,CAAC,MAAM,EAAE;MACTE,WAAW,EAAE,eAAe;MAC5BuC,EAAE,EAAE;QACFC,KAAK,EAAE,SAAPA,KAAKA,CAAYZ,MAAM,EAAE;UACvBA,MAAM,CAACI,cAAc,CAAC,CAAC;UACvBJ,MAAM,CAACa,eAAe,CAAC,CAAC;UACxB,OAAO5C,GAAG,CAACiC,gBAAgB,CAACb,GAAG,CAAC;QAClC;MACF;IACF,CAAC,CAAC,GACFpB,GAAG,CAAC6C,EAAE,CAAC,CAAC,CAEhB,CAAC;EACH,CAAC,CAAC,EACF,CACF,CAAC,CAEL,CAAC,EACD5C,EAAE,CACA,IAAI,EACJ;IACE6C,UAAU,EAAE,CACV;MACEP,IAAI,EAAE,MAAM;MACZQ,OAAO,EAAE,QAAQ;MACjBC,KAAK,EAAEhD,GAAG,CAACiD,OAAO;MAClBC,UAAU,EAAE;IACd,CAAC,CACF;IACD/C,WAAW,EAAE,aAAa;IAC1BC,KAAK,EAAE;MAAE+C,IAAI,EAAEnD,GAAG,CAACmD,IAAI,GAAG,IAAI;MAAEC,GAAG,EAAEpD,GAAG,CAACoD,GAAG,GAAG;IAAK;EACtD,CAAC,EACD,CACE,EAAEpD,GAAG,CAACqD,WAAW,CAACb,IAAI,IAAIxC,GAAG,CAACqD,WAAW,CAACb,IAAI,CAACC,KAAK,CAAC,GACjDxC,EAAE,CACA,IAAI,EACJ;IACEyC,EAAE,EAAE;MACFC,KAAK,EAAE,SAAPA,KAAKA,CAAYZ,MAAM,EAAE;QACvB,OAAO/B,GAAG,CAACiC,gBAAgB,CAACjC,GAAG,CAACqD,WAAW,CAAC;MAC9C;IACF;EACF,CAAC,EACD,CAACrD,GAAG,CAACqC,EAAE,CAAC,OAAO,CAAC,CAClB,CAAC,GACDrC,GAAG,CAAC6C,EAAE,CAAC,CAAC,EACZ5C,EAAE,CACA,IAAI,EACJ;IACEyC,EAAE,EAAE;MACFC,KAAK,EAAE,SAAPA,KAAKA,CAAYZ,MAAM,EAAE;QACvB,OAAO/B,GAAG,CAACsD,YAAY,CAACtD,GAAG,CAACqD,WAAW,CAAC;MAC1C;IACF;EACF,CAAC,EACD,CAACrD,GAAG,CAACqC,EAAE,CAAC,WAAW,CAAC,CACtB,CAAC,CAEL,CAAC,CACF,EACD,CACF,CAAC;AACH,CAAC;AACD,IAAIkB,eAAe,GAAG,EAAE;AACxBxD,MAAM,CAACyD,aAAa,GAAG,IAAI;AAE3B,SAASzD,MAAM,EAAEwD,eAAe", "ignoreList": []}]}