{"remainingRequest": "E:\\java系统\\公司财务系统\\src\\main\\resources\\admin\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\java系统\\公司财务系统\\src\\main\\resources\\admin\\admin\\src\\views\\modules\\kaoqinxinxi\\add-or-update.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\java系统\\公司财务系统\\src\\main\\resources\\admin\\admin\\src\\views\\modules\\kaoqinxinxi\\add-or-update.vue", "mtime": 1754641465013}, {"path": "E:\\java系统\\公司财务系统\\src\\main\\resources\\admin\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754805254356}, {"path": "E:\\java系统\\公司财务系统\\src\\main\\resources\\admin\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754805257322}, {"path": "E:\\java系统\\公司财务系统\\src\\main\\resources\\admin\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754805254356}, {"path": "E:\\java系统\\公司财务系统\\src\\main\\resources\\admin\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754628160828}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["add-or-update.vue"], "names": [], "mappings": ";AA6HA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AAcA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;;;;;;;;;;;;;;AAcA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "add-or-update.vue", "sourceRoot": "src/views/modules/kaoqinxinxi", "sourcesContent": ["<template>\r\n\t<div class=\"addEdit-block\" :style='{\"padding\":\"30px\",\"margin\":\"0\"}'>\r\n\t\t<el-form\r\n\t\t\t:style='{\"border\":\"1px solid rgba(167, 180, 201,.3)  \",\"padding\":\"30px\",\"borderRadius\":\"6px\",\"background\":\"#fff\"}'\r\n\t\t\tclass=\"add-update-preview\"\r\n\t\t\tref=\"ruleForm\"\r\n\t\t\t:model=\"ruleForm\"\r\n\t\t\t:rules=\"rules\"\r\n\t\t\tlabel-width=\"80px\"\r\n\t\t>\r\n\t\t\t<template >\r\n\t\t\t\t<el-form-item :style='{\"margin\":\"0 0 20px 0\"}' class=\"select\" v-if=\"type!='info'\"  label=\"月份\" prop=\"yuefen\">\r\n\t\t\t\t\t<el-select :disabled=\"ro.yuefen\" v-model=\"ruleForm.yuefen\" placeholder=\"请选择月份\" >\r\n\t\t\t\t\t\t<el-option\r\n\t\t\t\t\t\t\tv-for=\"(item,index) in yuefenOptions\"\r\n\t\t\t\t\t\t\tv-bind:key=\"index\"\r\n\t\t\t\t\t\t\t:label=\"item\"\r\n\t\t\t\t\t\t\t:value=\"item\">\r\n\t\t\t\t\t\t</el-option>\r\n\t\t\t\t\t</el-select>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<el-form-item :style='{\"margin\":\"0 0 20px 0\"}' v-else class=\"input\" label=\"月份\" prop=\"yuefen\">\r\n\t\t\t\t\t<el-input v-model=\"ruleForm.yuefen\"\r\n\t\t\t\t\t\tplaceholder=\"月份\" readonly></el-input>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<el-form-item :style='{\"margin\":\"0 0 20px 0\"}' class=\"select\" v-if=\"type!='info'\" label=\"工号\" prop=\"gonghao\">\r\n\t\t\t\t\t<el-select :disabled=\"ro.gonghao\" @change=\"gonghaoChange\" v-model=\"ruleForm.gonghao\" placeholder=\"请选择工号\">\r\n\t\t\t\t\t\t<el-option\r\n\t\t\t\t\t\t\tv-for=\"(item,index) in gonghaoOptions\"\r\n\t\t\t\t\t\t\tv-bind:key=\"index\"\r\n\t\t\t\t\t\t\t:label=\"item\"\r\n\t\t\t\t\t\t\t:value=\"item\">\r\n\t\t\t\t\t\t</el-option>\r\n\t\t\t\t\t</el-select>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<el-form-item :style='{\"margin\":\"0 0 20px 0\"}' class=\"input\" v-else-if=\"ruleForm.gonghao\" label=\"工号\" prop=\"gonghao\">\r\n\t\t\t\t\t<el-input v-model=\"ruleForm.gonghao\" placeholder=\"工号\" readonly></el-input>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<el-form-item :style='{\"margin\":\"0 0 20px 0\"}' class=\"input\" v-if=\"type!='info'\"  label=\"姓名\" prop=\"xingming\">\r\n\t\t\t\t\t<el-input v-model=\"ruleForm.xingming\" placeholder=\"姓名\" clearable  :readonly=\"ro.xingming\"></el-input>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<el-form-item :style='{\"margin\":\"0 0 20px 0\"}' v-else class=\"input\" label=\"姓名\" prop=\"xingming\">\r\n\t\t\t\t\t<el-input v-model=\"ruleForm.xingming\" placeholder=\"姓名\" readonly></el-input>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<el-form-item :style='{\"margin\":\"0 0 20px 0\"}' class=\"input\" v-if=\"type!='info'\"  label=\"部门\" prop=\"bumen\">\r\n\t\t\t\t\t<el-input v-model=\"ruleForm.bumen\" placeholder=\"部门\" clearable  :readonly=\"ro.bumen\"></el-input>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<el-form-item :style='{\"margin\":\"0 0 20px 0\"}' v-else class=\"input\" label=\"部门\" prop=\"bumen\">\r\n\t\t\t\t\t<el-input v-model=\"ruleForm.bumen\" placeholder=\"部门\" readonly></el-input>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<el-form-item :style='{\"margin\":\"0 0 20px 0\"}' class=\"input\" v-if=\"type!='info'\"  label=\"职位\" prop=\"zhiwei\">\r\n\t\t\t\t\t<el-input v-model=\"ruleForm.zhiwei\" placeholder=\"职位\" clearable  :readonly=\"ro.zhiwei\"></el-input>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<el-form-item :style='{\"margin\":\"0 0 20px 0\"}' v-else class=\"input\" label=\"职位\" prop=\"zhiwei\">\r\n\t\t\t\t\t<el-input v-model=\"ruleForm.zhiwei\" placeholder=\"职位\" readonly></el-input>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<el-form-item :style='{\"margin\":\"0 0 20px 0\"}' class=\"input\" v-if=\"type!='info'\"  label=\"应出勤天数\" prop=\"yingchuqintianshu\">\r\n\t\t\t\t\t<el-input v-model.number=\"ruleForm.yingchuqintianshu\" placeholder=\"应出勤天数\" clearable  :readonly=\"ro.yingchuqintianshu\"></el-input>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<el-form-item :style='{\"margin\":\"0 0 20px 0\"}' v-else class=\"input\" label=\"应出勤天数\" prop=\"yingchuqintianshu\">\r\n\t\t\t\t\t<el-input v-model=\"ruleForm.yingchuqintianshu\" placeholder=\"应出勤天数\" readonly></el-input>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<el-form-item :style='{\"margin\":\"0 0 20px 0\"}' class=\"input\" v-if=\"type!='info'\"  label=\"请假天数\" prop=\"qingjiatianshu\">\r\n\t\t\t\t\t<el-input v-model.number=\"ruleForm.qingjiatianshu\" placeholder=\"请假天数\" clearable  :readonly=\"ro.qingjiatianshu\"></el-input>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<el-form-item :style='{\"margin\":\"0 0 20px 0\"}' v-else class=\"input\" label=\"请假天数\" prop=\"qingjiatianshu\">\r\n\t\t\t\t\t<el-input v-model=\"ruleForm.qingjiatianshu\" placeholder=\"请假天数\" readonly></el-input>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<el-form-item :style='{\"margin\":\"0 0 20px 0\"}' class=\"input\" v-if=\"type!='info'\"  label=\"迟到次数\" prop=\"chidaocishu\">\r\n\t\t\t\t\t<el-input v-model.number=\"ruleForm.chidaocishu\" placeholder=\"迟到次数\" clearable  :readonly=\"ro.chidaocishu\"></el-input>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<el-form-item :style='{\"margin\":\"0 0 20px 0\"}' v-else class=\"input\" label=\"迟到次数\" prop=\"chidaocishu\">\r\n\t\t\t\t\t<el-input v-model=\"ruleForm.chidaocishu\" placeholder=\"迟到次数\" readonly></el-input>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<el-form-item :style='{\"margin\":\"0 0 20px 0\"}' class=\"input\" v-if=\"type!='info'\"  label=\"早退次数\" prop=\"zaotuicishu\">\r\n\t\t\t\t\t<el-input v-model.number=\"ruleForm.zaotuicishu\" placeholder=\"早退次数\" clearable  :readonly=\"ro.zaotuicishu\"></el-input>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<el-form-item :style='{\"margin\":\"0 0 20px 0\"}' v-else class=\"input\" label=\"早退次数\" prop=\"zaotuicishu\">\r\n\t\t\t\t\t<el-input v-model=\"ruleForm.zaotuicishu\" placeholder=\"早退次数\" readonly></el-input>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<el-form-item :style='{\"margin\":\"0 0 20px 0\"}' class=\"input\" v-if=\"type!='info'\"  label=\"出差天数\" prop=\"chuchatianshu\">\r\n\t\t\t\t\t<el-input v-model.number=\"ruleForm.chuchatianshu\" placeholder=\"出差天数\" clearable  :readonly=\"ro.chuchatianshu\"></el-input>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<el-form-item :style='{\"margin\":\"0 0 20px 0\"}' v-else class=\"input\" label=\"出差天数\" prop=\"chuchatianshu\">\r\n\t\t\t\t\t<el-input v-model=\"ruleForm.chuchatianshu\" placeholder=\"出差天数\" readonly></el-input>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<el-form-item :style='{\"margin\":\"0 0 20px 0\"}' class=\"input\" v-if=\"type!='info'\" label=\"实到天数\" prop=\"shidaotianshu\">\r\n\t\t\t\t\t<el-input v-model=\"shidaotianshu\" placeholder=\"实到天数\" readonly></el-input>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<el-form-item :style='{\"margin\":\"0 0 20px 0\"}' class=\"input\" v-else-if=\"ruleForm.shidaotianshu\" label=\"实到天数\" prop=\"shidaotianshu\">\r\n\t\t\t\t\t<el-input v-model=\"ruleForm.shidaotianshu\" placeholder=\"实到天数\" readonly></el-input>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<el-form-item :style='{\"margin\":\"0 0 20px 0\"}' class=\"date\" v-if=\"type!='info'\" label=\"登记时间\" prop=\"dengjishijian\">\r\n\t\t\t\t\t<el-date-picker\r\n\t\t\t\t\t\tvalue-format=\"yyyy-MM-dd HH:mm:ss\"\r\n\t\t\t\t\t\tv-model=\"ruleForm.dengjishijian\" \r\n\t\t\t\t\t\ttype=\"datetime\"\r\n\t\t\t\t\t\t:readonly=\"ro.dengjishijian\"\r\n\t\t\t\t\t\tplaceholder=\"登记时间\"\r\n\t\t\t\t\t></el-date-picker>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<el-form-item :style='{\"margin\":\"0 0 20px 0\"}' class=\"input\" v-else-if=\"ruleForm.dengjishijian\" label=\"登记时间\" prop=\"dengjishijian\">\r\n\t\t\t\t\t<el-input v-model=\"ruleForm.dengjishijian\" placeholder=\"登记时间\" readonly></el-input>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t</template>\r\n\t\t\t<el-form-item :style='{\"padding\":\"0\",\"margin\":\"0\"}' class=\"btn\">\r\n\t\t\t\t<el-button class=\"btn3\"  v-if=\"type!='info'\" type=\"success\" @click=\"onSubmit\">\r\n\t\t\t\t\t<span class=\"icon iconfont icon-xihuan\" :style='{\"margin\":\"0 2px\",\"fontSize\":\"14px\",\"color\":\"#fff\",\"height\":\"40px\"}'></span>\r\n\t\t\t\t\t提交\r\n\t\t\t\t</el-button>\r\n\t\t\t\t<el-button class=\"btn4\" v-if=\"type!='info'\" type=\"success\" @click=\"back()\">\r\n\t\t\t\t\t<span class=\"icon iconfont icon-xihuan\" :style='{\"margin\":\"0 2px\",\"fontSize\":\"14px\",\"color\":\"#fff\",\"height\":\"40px\"}'></span>\r\n\t\t\t\t\t取消\r\n\t\t\t\t</el-button>\r\n\t\t\t\t<el-button class=\"btn5\" v-if=\"type=='info'\" type=\"success\" @click=\"back()\">\r\n\t\t\t\t\t<span class=\"icon iconfont icon-xihuan\" :style='{\"margin\":\"0 2px\",\"fontSize\":\"14px\",\"color\":\"#fff\",\"height\":\"40px\"}'></span>\r\n\t\t\t\t\t返回\r\n\t\t\t\t</el-button>\r\n\t\t\t</el-form-item>\r\n\t\t</el-form>\r\n    \r\n\r\n  </div>\r\n</template>\r\n<script>\r\n// 数字，邮件，手机，url，身份证校验\r\nimport { isNumber,isIntNumer,isEmail,isPhone, isMobile,isURL,checkIdCard } from \"@/utils/validate\";\r\nexport default {\r\n\tdata() {\r\n\t\tlet self = this\r\n\t\tvar validateIdCard = (rule, value, callback) => {\r\n\t\t\tif(!value){\r\n\t\t\t\tcallback();\r\n\t\t\t} else if (!checkIdCard(value)) {\r\n\t\t\t\tcallback(new Error(\"请输入正确的身份证号\"));\r\n\t\t\t} else {\r\n\t\t\t\tcallback();\r\n\t\t\t}\r\n\t\t};\r\n\t\tvar validateUrl = (rule, value, callback) => {\r\n\t\t\tif(!value){\r\n\t\t\t\tcallback();\r\n\t\t\t} else if (!isURL(value)) {\r\n\t\t\t\tcallback(new Error(\"请输入正确的URL地址\"));\r\n\t\t\t} else {\r\n\t\t\t\tcallback();\r\n\t\t\t}\r\n\t\t};\r\n\t\tvar validateMobile = (rule, value, callback) => {\r\n\t\t\tif(!value){\r\n\t\t\t\tcallback();\r\n\t\t\t} else if (!isMobile(value)) {\r\n\t\t\t\tcallback(new Error(\"请输入正确的手机号码\"));\r\n\t\t\t} else {\r\n\t\t\t\tcallback();\r\n\t\t\t}\r\n\t\t};\r\n\t\tvar validatePhone = (rule, value, callback) => {\r\n\t\t\tif(!value){\r\n\t\t\t\tcallback();\r\n\t\t\t} else if (!isPhone(value)) {\r\n\t\t\t\tcallback(new Error(\"请输入正确的电话号码\"));\r\n\t\t\t} else {\r\n\t\t\t\tcallback();\r\n\t\t\t}\r\n\t\t};\r\n\t\tvar validateEmail = (rule, value, callback) => {\r\n\t\t\tif(!value){\r\n\t\t\t\tcallback();\r\n\t\t\t} else if (!isEmail(value)) {\r\n\t\t\t\tcallback(new Error(\"请输入正确的邮箱地址\"));\r\n\t\t\t} else {\r\n\t\t\t\tcallback();\r\n\t\t\t}\r\n\t\t};\r\n\t\tvar validateNumber = (rule, value, callback) => {\r\n\t\t\tif(!value){\r\n\t\t\t\tcallback();\r\n\t\t\t} else if (!isNumber(value)) {\r\n\t\t\t\tcallback(new Error(\"请输入数字\"));\r\n\t\t\t} else {\r\n\t\t\t\tcallback();\r\n\t\t\t}\r\n\t\t};\r\n\t\tvar validateIntNumber = (rule, value, callback) => {\r\n\t\t\tif(!value){\r\n\t\t\t\tcallback();\r\n\t\t\t} else if (!isIntNumer(value)) {\r\n\t\t\t\tcallback(new Error(\"请输入整数\"));\r\n\t\t\t} else {\r\n\t\t\t\tcallback();\r\n\t\t\t}\r\n\t\t};\r\n\t\treturn {\r\n\t\t\tid: '',\r\n\t\t\ttype: '',\r\n\t\t\t\r\n\t\t\t\r\n\t\t\tro:{\r\n\t\t\t\tyuefen : false,\r\n\t\t\t\tgonghao : false,\r\n\t\t\t\txingming : false,\r\n\t\t\t\tbumen : false,\r\n\t\t\t\tzhiwei : false,\r\n\t\t\t\tyingchuqintianshu : false,\r\n\t\t\t\tqingjiatianshu : false,\r\n\t\t\t\tchidaocishu : false,\r\n\t\t\t\tzaotuicishu : false,\r\n\t\t\t\tchuchatianshu : false,\r\n\t\t\t\tshidaotianshu : false,\r\n\t\t\t\tdengjishijian : false,\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t\r\n\t\t\truleForm: {\r\n\t\t\t\tyuefen: '',\r\n\t\t\t\tgonghao: '',\r\n\t\t\t\txingming: '',\r\n\t\t\t\tbumen: '',\r\n\t\t\t\tzhiwei: '',\r\n\t\t\t\tyingchuqintianshu: '',\r\n\t\t\t\tqingjiatianshu: '',\r\n\t\t\t\tchidaocishu: '',\r\n\t\t\t\tzaotuicishu: '',\r\n\t\t\t\tchuchatianshu: '',\r\n\t\t\t\tshidaotianshu: '',\r\n\t\t\t\tdengjishijian: '',\r\n\t\t\t},\r\n\t\t\r\n\t\t\tyuefenOptions: [],\r\n\t\t\tgonghaoOptions: [],\r\n\r\n\t\t\t\r\n\t\t\trules: {\r\n\t\t\t\tyuefen: [\r\n\t\t\t\t],\r\n\t\t\t\tgonghao: [\r\n\t\t\t\t],\r\n\t\t\t\txingming: [\r\n\t\t\t\t],\r\n\t\t\t\tbumen: [\r\n\t\t\t\t],\r\n\t\t\t\tzhiwei: [\r\n\t\t\t\t],\r\n\t\t\t\tyingchuqintianshu: [\r\n\t\t\t\t\t{ validator: validateIntNumber, trigger: 'blur' },\r\n\t\t\t\t],\r\n\t\t\t\tqingjiatianshu: [\r\n\t\t\t\t\t{ validator: validateIntNumber, trigger: 'blur' },\r\n\t\t\t\t],\r\n\t\t\t\tchidaocishu: [\r\n\t\t\t\t\t{ validator: validateIntNumber, trigger: 'blur' },\r\n\t\t\t\t],\r\n\t\t\t\tzaotuicishu: [\r\n\t\t\t\t\t{ validator: validateIntNumber, trigger: 'blur' },\r\n\t\t\t\t],\r\n\t\t\t\tchuchatianshu: [\r\n\t\t\t\t\t{ validator: validateIntNumber, trigger: 'blur' },\r\n\t\t\t\t],\r\n\t\t\t\tshidaotianshu: [\r\n\t\t\t\t\t{ validator: validateIntNumber, trigger: 'blur' },\r\n\t\t\t\t],\r\n\t\t\t\tdengjishijian: [\r\n\t\t\t\t],\r\n\t\t\t}\r\n\t\t};\r\n\t},\r\n\tprops: [\"parent\"],\r\n\tcomputed: {\r\n\t\tshidaotianshu: {\r\n\t\t\tget: function () {\r\n\t\t\t\treturn 0+parseFloat(this.ruleForm.yingchuqintianshu==\"\"?0:this.ruleForm.yingchuqintianshu)-parseFloat(this.ruleForm.qingjiatianshu==\"\"?0:this.ruleForm.qingjiatianshu)-parseFloat(this.ruleForm.chuchatianshu==\"\"?0:this.ruleForm.chuchatianshu) || 0\r\n\t\t\t}\r\n\t\t},\r\n\r\n\r\n\r\n\t},\r\n    components: {\r\n    },\r\n\tcreated() {\r\n\t\tthis.ruleForm.dengjishijian = this.getCurDateTime()\r\n\t},\r\n\tmethods: {\r\n\t\t\r\n\t\t// 下载\r\n\t\tdownload(file){\r\n\t\t\twindow.open(`${file}`)\r\n\t\t},\r\n\t\t// 初始化\r\n\t\tinit(id,type) {\r\n\t\t\tif (id) {\r\n\t\t\t\tthis.id = id;\r\n\t\t\t\tthis.type = type;\r\n\t\t\t}\r\n\t\t\tif(this.type=='info'||this.type=='else'){\r\n\t\t\t\tthis.info(id);\r\n\t\t\t}else if(this.type=='logistics'){\r\n\t\t\t\tthis.logistics=false;\r\n\t\t\t\tthis.info(id);\r\n\t\t\t}else if(this.type=='cross'){\r\n\t\t\t\tvar obj = this.$storage.getObj('crossObj');\r\n\t\t\t\tfor (var o in obj){\r\n\t\t\t\t\t\tif(o=='yuefen'){\r\n\t\t\t\t\t\t\tthis.ruleForm.yuefen = obj[o];\r\n\t\t\t\t\t\t\tthis.ro.yuefen = true;\r\n\t\t\t\t\t\t\tcontinue;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif(o=='gonghao'){\r\n\t\t\t\t\t\t\tthis.ruleForm.gonghao = obj[o];\r\n\t\t\t\t\t\t\tthis.ro.gonghao = true;\r\n\t\t\t\t\t\t\tcontinue;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif(o=='xingming'){\r\n\t\t\t\t\t\t\tthis.ruleForm.xingming = obj[o];\r\n\t\t\t\t\t\t\tthis.ro.xingming = true;\r\n\t\t\t\t\t\t\tcontinue;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif(o=='bumen'){\r\n\t\t\t\t\t\t\tthis.ruleForm.bumen = obj[o];\r\n\t\t\t\t\t\t\tthis.ro.bumen = true;\r\n\t\t\t\t\t\t\tcontinue;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif(o=='zhiwei'){\r\n\t\t\t\t\t\t\tthis.ruleForm.zhiwei = obj[o];\r\n\t\t\t\t\t\t\tthis.ro.zhiwei = true;\r\n\t\t\t\t\t\t\tcontinue;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif(o=='yingchuqintianshu'){\r\n\t\t\t\t\t\t\tthis.ruleForm.yingchuqintianshu = obj[o];\r\n\t\t\t\t\t\t\tthis.ro.yingchuqintianshu = true;\r\n\t\t\t\t\t\t\tcontinue;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif(o=='qingjiatianshu'){\r\n\t\t\t\t\t\t\tthis.ruleForm.qingjiatianshu = obj[o];\r\n\t\t\t\t\t\t\tthis.ro.qingjiatianshu = true;\r\n\t\t\t\t\t\t\tcontinue;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif(o=='chidaocishu'){\r\n\t\t\t\t\t\t\tthis.ruleForm.chidaocishu = obj[o];\r\n\t\t\t\t\t\t\tthis.ro.chidaocishu = true;\r\n\t\t\t\t\t\t\tcontinue;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif(o=='zaotuicishu'){\r\n\t\t\t\t\t\t\tthis.ruleForm.zaotuicishu = obj[o];\r\n\t\t\t\t\t\t\tthis.ro.zaotuicishu = true;\r\n\t\t\t\t\t\t\tcontinue;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif(o=='chuchatianshu'){\r\n\t\t\t\t\t\t\tthis.ruleForm.chuchatianshu = obj[o];\r\n\t\t\t\t\t\t\tthis.ro.chuchatianshu = true;\r\n\t\t\t\t\t\t\tcontinue;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif(o=='shidaotianshu'){\r\n\t\t\t\t\t\t\tthis.ruleForm.shidaotianshu = obj[o];\r\n\t\t\t\t\t\t\tthis.ro.shidaotianshu = true;\r\n\t\t\t\t\t\t\tcontinue;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif(o=='dengjishijian'){\r\n\t\t\t\t\t\t\tthis.ruleForm.dengjishijian = obj[o];\r\n\t\t\t\t\t\t\tthis.ro.dengjishijian = true;\r\n\t\t\t\t\t\t\tcontinue;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t// 获取用户信息\r\n\t\t\tthis.$http({\r\n\t\t\t\turl: `${this.$storage.get('sessionTable')}/session`,\r\n\t\t\t\tmethod: \"get\"\r\n\t\t\t}).then(({ data }) => {\r\n\t\t\t\tif (data && data.code === 0) {\r\n\t\t\t\t\t\r\n\t\t\t\t\tvar json = data.data;\r\n\t\t\t\t\tif(((json.gonghao!=''&&json.gonghao) || json.gonghao==0) && this.$storage.get(\"role\")!=\"管理员\"){\r\n\t\t\t\t\t\tthis.ruleForm.gonghao = json.gonghao\r\n\t\t\t\t\t\tthis.ro.gonghao = true;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif(((json.xingming!=''&&json.xingming) || json.xingming==0) && this.$storage.get(\"role\")!=\"管理员\"){\r\n\t\t\t\t\t\tthis.ruleForm.xingming = json.xingming\r\n\t\t\t\t\t\tthis.ro.xingming = true;\r\n\t\t\t\t\t}\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.$message.error(data.msg);\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t\t\r\n            this.yuefenOptions = \"一月,二月,三月,四月,五月,六月,七月,八月,九月,十月,十一月,十二月\".split(',')\r\n            this.$http({\r\n\t\t\t\turl: `option/yuangong/gonghao`,\r\n\t\t\t\tmethod: \"get\"\r\n            }).then(({ data }) => {\r\n\t\t\t\tif (data && data.code === 0) {\r\n\t\t\t\t\tthis.gonghaoOptions = data.data;\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.$message.error(data.msg);\r\n\t\t\t\t}\r\n            });\r\n\t\t\t\r\n\t\t},\r\n\t\t\t// 下拉框\r\n\t\t\tgonghaoChange () {\r\n\t\t\t\tthis.$http({\r\n\t\t\t\t\turl: `follow/yuangong/gonghao?columnValue=`+ this.ruleForm.gonghao,\r\n\t\t\t\t\tmethod: \"get\"\r\n\t\t\t\t}).then(({ data }) => {\r\n\t\t\t\t\tif (data && data.code === 0) {\r\n\t\t\t\t\t\tif(data.data.xingming){\r\n\t\t\t\t\t\t\tthis.ruleForm.xingming = data.data.xingming\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif(data.data.bumen){\r\n\t\t\t\t\t\t\tthis.ruleForm.bumen = data.data.bumen\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif(data.data.zhiwei){\r\n\t\t\t\t\t\t\tthis.ruleForm.zhiwei = data.data.zhiwei\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tthis.$message.error(data.msg);\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n    // 多级联动参数\r\n\r\n    info(id) {\r\n      this.$http({\r\n        url: `kaoqinxinxi/info/${id}`,\r\n        method: \"get\"\r\n      }).then(({ data }) => {\r\n        if (data && data.code === 0) {\r\n        this.ruleForm = data.data;\r\n        //解决前台上传图片后台不显示的问题\r\n        let reg=new RegExp('../../../upload','g')//g代表全部\r\n        } else {\r\n          this.$message.error(data.msg);\r\n        }\r\n      });\r\n    },\r\n\r\n\r\n    // 提交\r\n    onSubmit() {\r\n        this.ruleForm.shidaotianshu = this.shidaotianshu\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\nvar objcross = this.$storage.getObj('crossObj');\r\n      //更新跨表属�?\r\n       var crossuserid;\r\n       var crossrefid;\r\n       var crossoptnum;\r\n       if(this.type=='cross'){\r\n                var statusColumnName = this.$storage.get('statusColumnName');\r\n                var statusColumnValue = this.$storage.get('statusColumnValue');\r\n                if(statusColumnName!='') {\r\n                        var obj = this.$storage.getObj('crossObj');\r\n                       if(statusColumnName && !statusColumnName.startsWith(\"[\")) {\r\n                               for (var o in obj){\r\n                                 if(o==statusColumnName){\r\n                                   obj[o] = statusColumnValue;\r\n                                 }\r\n                               }\r\n                               var table = this.$storage.get('crossTable');\r\n                             this.$http({\r\n                                 url: `${table}/update`,\r\n                                 method: \"post\",\r\n                                 data: obj\r\n                               }).then(({ data }) => {});\r\n                       } else {\r\n                               crossuserid=this.$storage.get('userid');\r\n                               crossrefid=obj['id'];\r\n                               crossoptnum=this.$storage.get('statusColumnName');\r\n                               crossoptnum=crossoptnum.replace(/\\[/,\"\").replace(/\\]/,\"\");\r\n                        }\r\n                }\r\n        }\r\n\t\tthis.$refs[\"ruleForm\"].validate(valid => {\r\n\t\t\tif (valid) {\r\n\t\t\t\tif(crossrefid && crossuserid) {\r\n\t\t\t\t\tthis.ruleForm.crossuserid = crossuserid;\r\n\t\t\t\t\tthis.ruleForm.crossrefid = crossrefid;\r\n\t\t\t\t\tlet params = { \r\n\t\t\t\t\t\tpage: 1, \r\n\t\t\t\t\t\tlimit: 10, \r\n\t\t\t\t\t\tcrossuserid:this.ruleForm.crossuserid,\r\n\t\t\t\t\t\tcrossrefid:this.ruleForm.crossrefid,\r\n\t\t\t\t\t} \r\n\t\t\t\tthis.$http({ \r\n\t\t\t\t\turl: \"kaoqinxinxi/page\", \r\n\t\t\t\t\tmethod: \"get\", \r\n\t\t\t\t\tparams: params \r\n\t\t\t\t}).then(({ \r\n\t\t\t\t\tdata \r\n\t\t\t\t}) => { \r\n\t\t\t\t\tif (data && data.code === 0) { \r\n\t\t\t\t\t\tif(data.data.total>=crossoptnum) {\r\n\t\t\t\t\t\t\tthis.$message.error(this.$storage.get('tips'));\r\n\t\t\t\t\t\t\treturn false;\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tthis.$http({\r\n\t\t\t\t\t\t\t\turl: `kaoqinxinxi/${!this.ruleForm.id ? \"save\" : \"update\"}`,\r\n\t\t\t\t\t\t\t\tmethod: \"post\",\r\n\t\t\t\t\t\t\t\tdata: this.ruleForm\r\n\t\t\t\t\t\t\t}).then(({ data }) => {\r\n\t\t\t\t\t\t\t\tif (data && data.code === 0) {\r\n\t\t\t\t\t\t\t\t\tthis.$message({\r\n\t\t\t\t\t\t\t\t\t\tmessage: \"操作成功\",\r\n\t\t\t\t\t\t\t\t\t\ttype: \"success\",\r\n\t\t\t\t\t\t\t\t\t\tduration: 1500,\r\n\t\t\t\t\t\t\t\t\t\tonClose: () => {\r\n\t\t\t\t\t\t\t\t\t\t\tthis.parent.showFlag = true;\r\n\t\t\t\t\t\t\t\t\t\t\tthis.parent.addOrUpdateFlag = false;\r\n\t\t\t\t\t\t\t\t\t\t\tthis.parent.kaoqinxinxiCrossAddOrUpdateFlag = false;\r\n\t\t\t\t\t\t\t\t\t\t\tthis.parent.search();\r\n\t\t\t\t\t\t\t\t\t\t\tthis.parent.contentStyleChange();\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\tthis.$message.error(data.msg);\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t});\r\n\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t} else { \r\n\t\t\t\t} \r\n\t\t\t});\r\n\t\t} else {\r\n\t\t\tthis.$http({\r\n\t\t\t\turl: `kaoqinxinxi/${!this.ruleForm.id ? \"save\" : \"update\"}`,\r\n\t\t\t\tmethod: \"post\",\r\n\t\t\t   data: this.ruleForm\r\n\t\t\t}).then(({ data }) => {\r\n\t\t\t\tif (data && data.code === 0) {\r\n\t\t\t\t\tthis.$message({\r\n\t\t\t\t\t\tmessage: \"操作成功\",\r\n\t\t\t\t\t\ttype: \"success\",\r\n\t\t\t\t\t\tduration: 1500,\r\n\t\t\t\t\t\tonClose: () => {\r\n\t\t\t\t\t\t\tthis.parent.showFlag = true;\r\n\t\t\t\t\t\t\tthis.parent.addOrUpdateFlag = false;\r\n\t\t\t\t\t\t\tthis.parent.kaoqinxinxiCrossAddOrUpdateFlag = false;\r\n\t\t\t\t\t\t\tthis.parent.search();\r\n\t\t\t\t\t\t\tthis.parent.contentStyleChange();\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t});\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.$message.error(data.msg);\r\n\t\t\t   }\r\n\t\t\t});\r\n\t\t }\r\n         }\r\n       });\r\n    },\r\n    // 获取uuid\r\n    getUUID () {\r\n      return new Date().getTime();\r\n    },\r\n    // 返回\r\n    back() {\r\n      this.parent.showFlag = true;\r\n      this.parent.addOrUpdateFlag = false;\r\n      this.parent.kaoqinxinxiCrossAddOrUpdateFlag = false;\r\n      this.parent.contentStyleChange();\r\n    },\r\n  }\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n\t.amap-wrapper {\r\n\t\twidth: 100%;\r\n\t\theight: 500px;\r\n\t}\r\n\t\r\n\t.search-box {\r\n\t\tposition: absolute;\r\n\t}\r\n\t\r\n\t.el-date-editor.el-input {\r\n\t\twidth: auto;\r\n\t}\r\n\t\r\n\t.add-update-preview .el-form-item :deep .el-form-item__label {\r\n\t  \t  padding: 0 10px 0 0;\r\n\t  \t  color: #374254;\r\n\t  \t  font-weight: 600;\r\n\t  \t  width: 80px;\r\n\t  \t  font-size: 14px;\r\n\t  \t  line-height: 40px;\r\n\t  \t  text-align: right;\r\n\t  \t}\r\n\t\r\n\t.add-update-preview .el-form-item :deep .el-form-item__content {\r\n\t  margin-left: 80px;\r\n\t}\r\n\t\r\n\t.add-update-preview .el-input :deep .el-input__inner {\r\n\t  \t  border: 1px solid rgba(167, 180, 201,.3);\r\n\t  \t  border-radius: 4px;\r\n\t  \t  padding: 0 12px;\r\n\t  \t  outline: none;\r\n\t  \t  color: #a7b4c9;\r\n\t  \t  width: 400px;\r\n\t  \t  font-size: 14px;\r\n\t  \t  height: 40px;\r\n\t  \t}\r\n\t.add-update-preview .el-input-number :deep .el-input__inner {\r\n\t\ttext-align: left;\r\n\t  \t  border: 1px solid rgba(167, 180, 201,.3);\r\n\t  \t  border-radius: 4px;\r\n\t  \t  padding: 0 12px;\r\n\t  \t  outline: none;\r\n\t  \t  color: #a7b4c9;\r\n\t  \t  width: 400px;\r\n\t  \t  font-size: 14px;\r\n\t  \t  height: 40px;\r\n\t  \t}\r\n\t.add-update-preview .el-input-number :deep .el-input-number__decrease {\r\n\t\tdisplay: none;\r\n\t}\r\n\t.add-update-preview .el-input-number :deep .el-input-number__increase {\r\n\t\tdisplay: none;\r\n\t}\r\n\t\r\n\t.add-update-preview .el-select :deep .el-input__inner {\r\n\t  \t  border:  1px solid rgba(167, 180, 201,.3) ;\r\n\t  \t  border-radius: 4px;\r\n\t  \t  padding: 0 10px;\r\n\t  \t  outline: none;\r\n\t  \t  color: #a7b4c9;\r\n\t  \t  width: 400px;\r\n\t  \t  font-size: 14px;\r\n\t  \t  height: 40px;\r\n\t  \t}\r\n\t\r\n\t.add-update-preview .el-date-editor :deep .el-input__inner {\r\n\t  \t  border:  1px solid rgba(167, 180, 201,.3) ;\r\n\t  \t  border-radius: 4px;\r\n\t  \t  padding: 0 10px 0 30px;\r\n\t  \t  outline: none;\r\n\t  \t  color: #a7b4c9;\r\n\t  \t  width: 400px;\r\n\t  \t  font-size: 14px;\r\n\t  \t  height: 40px;\r\n\t  \t}\r\n\t\r\n\t.add-update-preview :deep .el-upload--picture-card {\r\n\t\tbackground: transparent;\r\n\t\tborder: 0;\r\n\t\tborder-radius: 0;\r\n\t\twidth: auto;\r\n\t\theight: auto;\r\n\t\tline-height: initial;\r\n\t\tvertical-align: middle;\r\n\t}\r\n\t\r\n\t.add-update-preview :deep .upload .upload-img {\r\n\t  \t  border: 2px dashed rgba(167, 180, 201,.3) ;\r\n\t  \t  cursor: pointer;\r\n\t  \t  border-radius: 6px;\r\n\t  \t  color: #a7b4c9  ;\r\n\t  \t  width: 200px;\r\n\t  \t  font-size: 32px;\r\n\t  \t  line-height: 200px;\r\n\t  \t  text-align: center;\r\n\t  \t  height: 200px;\r\n\t  \t}\r\n\t\r\n\t.add-update-preview :deep .el-upload-list .el-upload-list__item {\r\n\t  \t  border: 2px dashed rgba(167, 180, 201,.3) ;\r\n\t  \t  cursor: pointer;\r\n\t  \t  border-radius: 6px;\r\n\t  \t  color: #a7b4c9  ;\r\n\t  \t  width: 200px;\r\n\t  \t  font-size: 32px;\r\n\t  \t  line-height: 200px;\r\n\t  \t  text-align: center;\r\n\t  \t  height: 200px;\r\n\t  \t}\r\n\t\r\n\t.add-update-preview :deep .el-upload .el-icon-plus {\r\n\t  \t  border: 2px dashed rgba(167, 180, 201,.3) ;\r\n\t  \t  cursor: pointer;\r\n\t  \t  border-radius: 6px;\r\n\t  \t  color: #a7b4c9  ;\r\n\t  \t  width: 200px;\r\n\t  \t  font-size: 32px;\r\n\t  \t  line-height: 200px;\r\n\t  \t  text-align: center;\r\n\t  \t  height: 200px;\r\n\t  \t}\r\n\t\r\n\t.add-update-preview .el-textarea :deep .el-textarea__inner {\r\n\t  \t  border: 1px solid rgba(167, 180, 201,.3)     ;\r\n\t  \t  border-radius: 4px;\r\n\t  \t  padding: 12px;\r\n\t  \t  outline: none;\r\n\t  \t  color: #a7b4c9 ;\r\n\t  \t  width: 400px;\r\n\t  \t  font-size: 14px;\r\n\t  \t  height: 120px;\r\n\t  \t}\r\n\t\r\n\t.add-update-preview .btn .btn1 {\r\n\t\t\t\tborder: 0;\r\n\t\t\t\tcursor: pointer;\r\n\t\t\t\tborder-radius: 4px;\r\n\t\t\t\tpadding: 0 24px;\r\n\t\t\t\tmargin: 4px;\r\n\t\t\t\toutline: none;\r\n\t\t\t\tcolor: #fff;\r\n\t\t\t\tbackground: #6574cd;\r\n\t\t\t\twidth: auto;\r\n\t\t\t\tfont-size: 14px;\r\n\t\t\t\theight: 40px;\r\n\t\t\t}\r\n\t\r\n\t.add-update-preview .btn .btn1:hover {\r\n\t\t\t\topacity: 0.8;\r\n\t\t\t}\r\n\t\r\n\t.add-update-preview .btn .btn2 {\r\n\t\t\t\tborder: 0;\r\n\t\t\t\tcursor: pointer;\r\n\t\t\t\tborder-radius: 4px;\r\n\t\t\t\tpadding: 0 24px;\r\n\t\t\t\tmargin: 4px;\r\n\t\t\t\toutline: none;\r\n\t\t\t\tcolor: #fff;\r\n\t\t\t\tbackground: #6574cd;\r\n\t\t\t\twidth: auto;\r\n\t\t\t\tfont-size: 14px;\r\n\t\t\t\theight: 40px;\r\n\t\t\t}\r\n\t\r\n\t.add-update-preview .btn .btn2:hover {\r\n\t\t\t\topacity: 0.8;\r\n\t\t\t}\r\n\t\r\n\t.add-update-preview .btn .btn3 {\r\n\t\t\t\tborder: 0;\r\n\t\t\t\tcursor: pointer;\r\n\t\t\t\tborder-radius: 4px;\r\n\t\t\t\tpadding: 0 24px;\r\n\t\t\t\tmargin: 4px;\r\n\t\t\t\toutline: none;\r\n\t\t\t\tcolor: #fff;\r\n\t\t\t\tbackground: #ff2b88;\r\n\t\t\t\twidth: auto;\r\n\t\t\t\tfont-size: 14px;\r\n\t\t\t\theight: 40px;\r\n\t\t\t}\r\n\t\r\n\t.add-update-preview .btn .btn3:hover {\r\n\t\t\t\topacity: 0.8;\r\n\t\t\t}\r\n\t\r\n\t.add-update-preview .btn .btn4 {\r\n\t\t\t\tborder: 0;\r\n\t\t\t\tcursor: pointer;\r\n\t\t\t\tborder-radius: 4px;\r\n\t\t\t\tpadding: 0 24px;\r\n\t\t\t\tmargin: 4px;\r\n\t\t\t\toutline: none;\r\n\t\t\t\tcolor: #fff;\r\n\t\t\t\tbackground: #ff2b88;\r\n\t\t\t\twidth: auto;\r\n\t\t\t\tfont-size: 14px;\r\n\t\t\t\theight: 40px;\r\n\t\t\t}\r\n\t\r\n\t.add-update-preview .btn .btn4:hover {\r\n\t\t\t\topacity: 0.8;\r\n\t\t\t}\r\n\t\r\n\t.add-update-preview .btn .btn5 {\r\n\t\t\t\tborder: 0;\r\n\t\t\t\tcursor: pointer;\r\n\t\t\t\tborder-radius: 4px;\r\n\t\t\t\tpadding: 0 24px;\r\n\t\t\t\tmargin: 4px;\r\n\t\t\t\toutline: none;\r\n\t\t\t\tcolor: #fff;\r\n\t\t\t\tbackground: #ff2b88;\r\n\t\t\t\twidth: auto;\r\n\t\t\t\tfont-size: 14px;\r\n\t\t\t\theight: 40px;\r\n\t\t\t}\r\n\t\r\n\t.add-update-preview .btn .btn5:hover {\r\n\t\t\t\topacity: 0.8;\r\n\t\t\t}\r\n</style>\r\n"]}]}